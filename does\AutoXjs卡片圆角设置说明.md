# AutoXjs 卡片圆角设置成功原因说明

## 问题背景
在AutoXjs项目中，最初卡片设置了 `cardCornerRadius="16dp"` 属性，但卡片仍然显示为直角，圆角效果不生效。

## 解决方案
通过修改卡片的背景属性设置，成功实现了圆角效果。

## 关键修改

### ❌ 错误的设置方式
```xml
<card
    id="游戏数据卡片"
    background={全局样式.颜色主题.背景次色}
    cardCornerRadius="16dp"
    cardElevation="2dp">
```

### ✅ 正确的设置方式
```xml
<card
    id="游戏数据卡片"
    cardBackgroundColor={全局样式.颜色主题.背景次色}
    cardCornerRadius="16dp"
    cardElevation="2dp">
```

## 核心原因分析

### 1. **属性名称差异**
- **错误属性**: `background` - 这是通用View的背景属性
- **正确属性**: `cardBackgroundColor` - 这是Card控件专用的背景属性

### 2. **AutoXjs Card控件特性**
根据AutoXjs官方文档，Card控件有专门的属性：
- `cardBackgroundColor`: 卡片的背景颜色
- `cardCornerRadius`: 卡片的圆角半径
- `cardElevation`: 卡片的阴影高度

### 3. **属性优先级**
当同时设置 `background` 和 `cardBackgroundColor` 时：
- `background` 属性会覆盖Card控件的内部圆角处理
- `cardBackgroundColor` 属性会正确应用到Card的圆角背景上

### 4. **渲染机制**
- 使用 `background` 时，AutoXjs将其作为普通View背景处理，忽略圆角
- 使用 `cardBackgroundColor` 时，AutoXjs会将背景与圆角一起渲染

## 官方文档支持
根据AutoXjs官方文档：
```
cardBackgroundColor: 卡片的背景颜色。比如 cardBackgroundColor="#ffffff"。
cardCornerRadius: 卡片的圆角半径。
cardElevation: 设置卡片在z轴上的高度，来控制阴影的大小。
```

## 最佳实践

### Card控件圆角设置标准格式：
```xml
<card
    cardBackgroundColor="#FFFFFF"
    cardCornerRadius="16dp"
    cardElevation="2dp">
    <!-- 卡片内容 -->
</card>
```

### 常用圆角值推荐：
- **小圆角**: 8dp - 适用于小型卡片或输入框
- **中圆角**: 12dp - 适用于按钮或中型卡片
- **大圆角**: 16dp - 适用于主要内容卡片
- **超大圆角**: 20dp+ - 适用于特殊设计需求

## 总结
卡片圆角设置成功的关键在于使用正确的Card控件专用属性 `cardBackgroundColor` 而不是通用的 `background` 属性。这确保了AutoXjs能够正确处理圆角渲染，使 `cardCornerRadius` 属性生效。
