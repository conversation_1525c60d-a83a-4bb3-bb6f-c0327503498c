/**
 * Magic 游戏辅助脚本 - Google Play商店登录管理器（简洁版）
 * 基于AutoXjs ozobiozobi v6.5.8.17开发
 * 遵循简洁代码规则：用最少的代码，最灵活的参数，解决最多的实际问题
 */

// 加载核心模块
var de模块 = require("./de.js");
var 日志管理器 = require("../../简洁日志管理器.js");

// 脚本状态
var 脚本状态 = {
    正在运行: false,
    当前线程: null,
    开始时间: null
};

/**
 * 启动_登录 - 简洁版异步登录管理器
 * @param {Function} 回调函数 - 统一回调函数(结果, 错误信息)
 * @param {Object} 配置参数 - 可选配置：{邮箱, 密码, 文件编号}
 * @returns {Boolean} 启动是否成功
 */
function 启动_登录(回调函数, 配置参数) {
    配置参数 = 配置参数 || {};

    // 支持文件编号：0=google.txt，1-100000=google1.txt到google100000.txt
    if (配置参数.文件编号 !== undefined) {
        配置参数.文件编号 = 配置参数.文件编号;
    } else {
        配置参数.文件编号 = 0;  // 默认使用google.txt
    }

    if (脚本状态.正在运行) {
        if (回调函数) 回调函数(false, "脚本已在运行");
        return false;
    }

    if (!auto.service) {
        if (回调函数) 回调函数(false, "缺少无障碍权限");
        return false;
    }

    脚本状态.正在运行 = true;
    脚本状态.开始时间 = new Date();

    日志管理器.信息("启动Google Play商店登录", "Google登录");

    脚本状态.当前线程 = threads.start(function() {
        try {
            var 结果 = de模块.登陆_Play商店(
                配置参数.邮箱,
                配置参数.密码,
                配置参数.文件编号,
                配置参数
            );

            ui.post(() => {
                if (回调函数) 回调函数(结果, null);
            });

        } catch (e) {
            ui.post(() => {
                if (回调函数) 回调函数(false, e.message);
            });
        } finally {
            脚本状态.正在运行 = false;
            脚本状态.当前线程 = null;
            脚本状态.开始时间 = null;
        }
    });

    return true;
}

/**
 * 停止_登录 - 简洁版停止函数
 * @returns {Boolean} 停止是否成功
 */
function 停止_登录() {
    try {
        if (脚本状态.当前线程) {
            脚本状态.当前线程.interrupt();
        }

        脚本状态.正在运行 = false;
        脚本状态.当前线程 = null;
        脚本状态.开始时间 = null;

        日志管理器.成功("Google登录脚本已停止", "Google登录");
        return true;
    } catch (e) {
        日志管理器.错误("停止登录失败: " + e.message, "Google登录");
        return false;
    }
}

/**
 * 获取_运行状态 - 简洁版状态查询
 * @returns {Object} 运行状态信息
 */
function 获取_运行状态() {
    return {
        正在运行: 脚本状态.正在运行,
        开始时间: 脚本状态.开始时间,
        运行时长: 脚本状态.开始时间 ? (Date.now() - 脚本状态.开始时间.getTime()) : 0
    };
}

// 导出模块功能
module.exports = {
    // 简洁版核心功能
    启动_登录: 启动_登录,
    停止_登录: 停止_登录,
    获取_运行状态: 获取_运行状态,

    // 兼容性接口
    异步启动登录: 启动_登录,
    停止登录脚本: 停止_登录,
    获取运行状态: 获取_运行状态,
    登陆Play商店: de模块.登陆_Play商店,
    de模块: de模块
};
