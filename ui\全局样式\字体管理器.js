/**
 * 字体管理器 - 负责下载和管理FontAwesome字体文件
 * 支持自动下载、缓存和加载FontAwesome字体
 */

"ui";

// 字体配置
var 字体配置 = {
    FontAwesome: {
        名称: "FontAwesome",
        版本: "5.15.2",
        文件名: "FontAwesome.ttf",
        下载地址: "https://github.com/FortAwesome/Font-Awesome/releases/download/5.15.2/fontawesome-free-5.15.2-desktop.zip",
        本地路径: "/storage/emulated/0/脚本/ui/全局样式/fonts/FontAwesome.ttf",
        备用地址: [
            "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.2/webfonts/fa-solid-900.ttf",
            "https://use.fontawesome.com/releases/v5.15.2/webfonts/fa-solid-900.ttf"
        ]
    },
    Roboto: {
        名称: "Roboto",
        版本: "2.0",
        文件名: "Roboto-Regular.ttf",
        下载地址: "https://fonts.google.com/download?family=Roboto",
        本地路径: "/storage/emulated/0/脚本/ui/全局样式/fonts/Roboto-Regular.ttf",
        备用地址: [
            "https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2"
        ]
    },
    RobotoBold: {
        名称: "RobotoBold",
        版本: "2.0",
        文件名: "Roboto-Bold.ttf",
        下载地址: "https://fonts.google.com/download?family=Roboto",
        本地路径: "/storage/emulated/0/脚本/ui/全局样式/fonts/Roboto-Bold.ttf",
        备用地址: [
            "https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2"
        ]
    }
};

// 字体管理器主类
var 字体管理器 = {
    
    /**
     * 检查字体文件是否存在
     * @param {String} 字体名称 
     * @returns {Boolean}
     */
    检查字体存在: function(字体名称) {
        try {
            var 配置 = 字体配置[字体名称];
            if (!配置) return false;
            
            return files.exists(配置.本地路径);
        } catch (e) {
            console.error("检查字体文件失败:", e);
            return false;
        }
    },

    /**
     * 加载字体文件
     * @param {String} 字体名称 
     * @returns {android.graphics.Typeface|null}
     */
    加载字体: function(字体名称) {
        try {
            var 配置 = 字体配置[字体名称];
            if (!配置) {
                console.warn("未找到字体配置:", 字体名称);
                return null;
            }

            if (!this.检查字体存在(字体名称)) {
                console.warn("字体文件不存在:", 配置.本地路径);
                return null;
            }

            var typeface = android.graphics.Typeface.createFromFile(配置.本地路径);
            console.log("字体加载成功:", 字体名称);
            return typeface;
        } catch (e) {
            console.error("加载字体失败:", e);
            return null;
        }
    },

    /**
     * 创建字体目录
     */
    创建字体目录: function() {
        try {
            var 字体目录 = "/storage/emulated/0/脚本/ui/全局样式/fonts/";
            if (!files.exists(字体目录)) {
                files.ensureDir(字体目录);
                console.log("字体目录创建成功:", 字体目录);
            }
            return true;
        } catch (e) {
            console.error("创建字体目录失败:", e);
            return false;
        }
    },

    /**
     * 下载字体文件
     * @param {String} 字体名称 
     * @param {Function} 回调函数 
     */
    下载字体: function(字体名称, 回调函数) {
        try {
            var 配置 = 字体配置[字体名称];
            if (!配置) {
                if (回调函数) 回调函数(false, "未找到字体配置");
                return;
            }

            // 创建字体目录
            if (!this.创建字体目录()) {
                if (回调函数) 回调函数(false, "无法创建字体目录");
                return;
            }

            console.log("开始下载字体:", 字体名称);
            
            // 使用备用地址下载（简化版本，直接下载TTF文件）
            var 下载地址 = 配置.备用地址[0]; // 使用第一个备用地址
            
            // 在子线程中下载
            threads.start(function() {
                try {
                    var response = http.get(下载地址);
                    if (response.statusCode == 200) {
                        // 保存文件
                        files.writeBytes(配置.本地路径, response.body.bytes());
                        
                        ui.post(function() {
                            console.log("字体下载成功:", 字体名称);
                            if (回调函数) 回调函数(true, "下载成功");
                        });
                    } else {
                        ui.post(function() {
                            console.error("字体下载失败，状态码:", response.statusCode);
                            if (回调函数) 回调函数(false, "下载失败: " + response.statusCode);
                        });
                    }
                } catch (e) {
                    ui.post(function() {
                        console.error("字体下载异常:", e);
                        if (回调函数) 回调函数(false, "下载异常: " + e.message);
                    });
                }
            });

        } catch (e) {
            console.error("下载字体失败:", e);
            if (回调函数) 回调函数(false, "下载失败: " + e.message);
        }
    },

    /**
     * 获取或下载字体
     * @param {String} 字体名称 
     * @param {Function} 回调函数 
     */
    获取字体: function(字体名称, 回调函数) {
        try {
            // 首先尝试加载现有字体
            var typeface = this.加载字体(字体名称);
            if (typeface) {
                if (回调函数) 回调函数(typeface, null);
                return;
            }

            // 如果字体不存在，尝试下载
            console.log("字体文件不存在，开始下载...");
            var self = this;
            this.下载字体(字体名称, function(成功, 消息) {
                if (成功) {
                    // 下载成功后重新加载
                    var newTypeface = self.加载字体(字体名称);
                    if (回调函数) 回调函数(newTypeface, newTypeface ? null : "下载后加载失败");
                } else {
                    if (回调函数) 回调函数(null, 消息);
                }
            });

        } catch (e) {
            console.error("获取字体失败:", e);
            if (回调函数) 回调函数(null, "获取字体失败: " + e.message);
        }
    },

    /**
     * 删除字体文件
     * @param {String} 字体名称 
     */
    删除字体: function(字体名称) {
        try {
            var 配置 = 字体配置[字体名称];
            if (!配置) return false;

            if (files.exists(配置.本地路径)) {
                files.remove(配置.本地路径);
                console.log("字体文件已删除:", 配置.本地路径);
                return true;
            }
            return false;
        } catch (e) {
            console.error("删除字体文件失败:", e);
            return false;
        }
    },

    /**
     * 获取字体信息
     * @param {String} 字体名称 
     */
    获取字体信息: function(字体名称) {
        var 配置 = 字体配置[字体名称];
        if (!配置) return null;

        return {
            名称: 配置.名称,
            版本: 配置.版本,
            文件名: 配置.文件名,
            本地路径: 配置.本地路径,
            存在: this.检查字体存在(字体名称),
            大小: this.检查字体存在(字体名称) ? files.size(配置.本地路径) : 0
        };
    },

    /**
     * 列出所有可用字体
     */
    列出所有字体: function() {
        var 字体列表 = [];
        for (var 字体名称 in 字体配置) {
            字体列表.push(this.获取字体信息(字体名称));
        }
        return 字体列表;
    }
};

// 便捷函数
function 获取FontAwesome字体(回调函数) {
    字体管理器.获取字体("FontAwesome", 回调函数);
}

function 获取Roboto字体(回调函数) {
    字体管理器.获取字体("Roboto", 回调函数);
}

function 获取RobotoBold字体(回调函数) {
    字体管理器.获取字体("RobotoBold", 回调函数);
}

function 检查FontAwesome字体() {
    return 字体管理器.检查字体存在("FontAwesome");
}

function 下载FontAwesome字体(回调函数) {
    字体管理器.下载字体("FontAwesome", 回调函数);
}

// 导出模块
module.exports = {
    字体管理器: 字体管理器,
    字体配置: 字体配置,
    获取FontAwesome字体: 获取FontAwesome字体,
    获取Roboto字体: 获取Roboto字体,
    获取RobotoBold字体: 获取RobotoBold字体,
    检查FontAwesome字体: 检查FontAwesome字体,
    下载FontAwesome字体: 下载FontAwesome字体
};
