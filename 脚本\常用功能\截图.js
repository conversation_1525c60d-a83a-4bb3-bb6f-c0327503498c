/**
 * 截图找图通用模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

function 查找_图片(图片路径, 区域x, 区域y, 区域宽, 区域高, 等待秒数, 间隔毫秒, 动作, 重试次数, 相似度) {
    try {
        // 设置默认参数
        等待秒数 = 等待秒数 || 5;
        间隔毫秒 = 间隔毫秒 || 1000;
        动作 = 动作 || "查找";  // 查找、判断点击
        重试次数 = 重试次数 || 3;
        相似度 = 相似度 || 0.8;

        console.log("🔍 开始" + 动作 + "图片: " + 图片路径 + " (相似度: " + 相似度 + ")");

        // 截取屏幕
        var 屏幕截图 = captureScreen();
        if (!屏幕截图) {
            console.log("❌ 截屏失败");
            return 动作 === "查找" ? null : false;
        }

        console.log("🎯 在指定区域" + 动作 + "图片...");

        // 在指定时间内循环查找
        var 开始时间 = new Date().getTime();
        var 结束时间 = 开始时间 + 等待秒数 * 1000;

        do {
            // 每次循环都重新读取图片模板（避免资源被回收）
            var 图片 = images.read(图片路径);
            if (!图片) {
                console.log("❌ 图片读取失败: " + 图片路径);
                屏幕截图.recycle();
                return 动作 === "查找" ? null : false;
            }

            // 获取图片尺寸（点击时需要）
            var 图片宽度 = 图片.getWidth();
            var 图片高度 = 图片.getHeight();

            // 在指定区域查找图片（使用相似度）
            var 位置;
            if (区域x !== undefined && 区域y !== undefined && 区域宽 !== undefined && 区域高 !== undefined) {
                console.log("🎯 在指定区域查找图片，区域: (" + 区域x + "," + 区域y + "," + 区域宽 + "," + 区域高 + ")");
                位置 = images.findImageInRegion(屏幕截图, 图片, 区域x, 区域y, 区域宽, 区域高, 相似度);
            } else {
                console.log("🎯 在全屏查找图片");
                位置 = images.findImage(屏幕截图, 图片, {threshold: 相似度});
            }

            if (位置) {
                console.log("✅ 找到图片，位置: (" + 位置.x + ", " + 位置.y + ")");

                // 根据动作类型处理
                if (动作 === "查找") {
                    // 只查找，不点击
                    屏幕截图.recycle();
                    图片.recycle();
                    return 位置;
                } else if (动作 === "判断点击") {
                    // 判断点击功能：点击后再次判断
                    // 保存图片尺寸（避免后续图片被释放后无法获取）
                    var 保存的图片宽度 = 图片宽度;
                    var 保存的图片高度 = 图片高度;

                    for (var i = 1; i <= 重试次数; i++) {
                        console.log("🎯 第" + i + "次尝试点击");

                        // 随机等待后点击
                        var 随机等待 = random(500, 2000);
                        console.log("⏳ 随机等待 " + 随机等待 + " 毫秒后点击");
                        sleep(随机等待);

                        // 点击图片中心位置（更准确）
                        var 中心x = 位置.x + Math.floor(保存的图片宽度 / 2);
                        var 中心y = 位置.y + Math.floor(保存的图片高度 / 2);
                        click(中心x, 中心y);
                        console.log("🎯 已点击图片中心位置: (" + 中心x + ", " + 中心y + ")");

                        // 等待一下让界面响应
                        sleep(1000);

                        // 重新截屏检查图片是否还存在
                        屏幕截图.recycle();
                        屏幕截图 = captureScreen();
                        if (!屏幕截图) {
                            console.log("❌ 重新截屏失败，跳过此次重试");
                            continue;
                        }

                        // 重新读取图片模板（避免资源被释放）
                        var 重新读取图片 = images.read(图片路径);
                        if (!重新读取图片) {
                            console.log("❌ 重新读取图片失败");
                            continue;
                        }

                        // 再次查找图片模板（使用相似度）
                        var 新位置;
                        if (区域x !== undefined && 区域y !== undefined && 区域宽 !== undefined && 区域高 !== undefined) {
                            新位置 = images.findImageInRegion(屏幕截图, 重新读取图片, 区域x, 区域y, 区域宽, 区域高, 相似度);
                        } else {
                            新位置 = images.findImage(屏幕截图, 重新读取图片, {threshold: 相似度});
                        }

                        // 释放重新读取的图片资源
                        重新读取图片.recycle();

                        if (!新位置) {
                            // 获取图片文件名
                            var 图片名称 = 图片路径.split('/').pop().replace('.png', '').replace('.jpg', '').replace('.jpeg', '');
                            console.log("✅ 重试了" + i + "次，" + 图片名称 + "没找到，执行下一步");
                            屏幕截图.recycle();
                            图片.recycle();
                            return true;
                        } else {
                            console.log("⚠️ 第" + i + "次点击后图片仍存在，位置: (" + 新位置.x + ", " + 新位置.y + ")");
                            位置 = 新位置;  // 更新位置信息
                        }
                    }

                    // 获取图片文件名
                    var 图片名称 = 图片路径.split('/').pop().replace('.png', '').replace('.jpg', '').replace('.jpeg', '');
                    console.log("❌ 重试了" + 重试次数 + "次，" + 图片名称 + "仍然存在，可能点击无效");
                    屏幕截图.recycle();
                    图片.recycle();
                    return false;
                }
            }

            // 释放本次循环的图片资源（仅在查找模式下需要）
            if (动作 === "查找") {
                图片.recycle();
            }

            // 等待指定间隔后继续查找
            sleep(间隔毫秒);

            // 重新截屏
            屏幕截图.recycle();
            屏幕截图 = captureScreen();
            if (!屏幕截图) {
                console.log("❌ 重新截屏失败，停止查找");
                return 动作 === "查找" ? null : false;
            }

        } while (new Date().getTime() < 结束时间);

        // 未找到图片的处理
        console.log("⚠️ " + 等待秒数 + "秒内未找到图片");

        // 释放资源
        屏幕截图.recycle();

        return 动作 === "查找" ? null : false;

    } catch (error) {
        console.error("❌ " + 动作 + "图片时发生错误: " + error);
        return 动作 === "查找" ? null : false;
    }
}


// 导出模块
module.exports = {
    查找_图片: 查找_图片
};