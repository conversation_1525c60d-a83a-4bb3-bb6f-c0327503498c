# 三区域公式识别设计方案 - 公式3.js

## 📋 项目概述

基于现有的`公式识别完整版.js v4.6.0`，创建新文件`公式3.js`，实现三区域识别方案，支持两位数×两位数的数学表达式识别。

### 🎯 设计目标

1. **移植核心功能**：答案区域识别、双数字组合、间距判断完整移植
2. **优化识别方式**：从单一大区域改为三个独立区域
3. **简化算法逻辑**：不移植210px硬性条件和复杂重复检测
4. **保持函数名称**：`计算_公式()`和`答案位置()`函数名不变
5. **单文件设计**：不使用模块导入，功能完整独立

## 🔍 区域定义

### 三个识别区域
- **前数字区域**：38,410,175,153 （第一个数字，支持1-2位）
- **运算符区域**：203,425,103,120 （+、-、×、÷）
- **后数字区域**：282,418,130,143 （第二个数字，支持1-2位）

### 答案区域（保持不变）
- **黄区**：103,648,136,68
- **蓝区**：303,650,136,66  
- **绿区**：103,788,131,70
- **红区**：303,785,131,71

## 🔧 核心功能保留

### 1. 答案识别功能（完整保留）
```javascript
// 第390-765行：完整移植
function 答案位置(外部截图) {
    // 四个答案区域识别
    // 数字组合逻辑
    // 相似度计算
    // 坐标转换
}
```

### 2. 双数字组合逻辑（必须保留）
```javascript
// 支持识别：11、22、33、44、55、66、77、88、99等
// 第610-646行的核心逻辑
if (第一个.数字 === "1" || 第二个.数字 === "1") {
    // 包含数字1：间距 -5~37像素
    间距合理 = (真实间距 >= -5 && 真实间距 < 37);
} else {
    // 其他数字：间距 -7~10像素
    间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
}
```

### 3. 三数字组合逻辑（必须保留）
```javascript
// 支持识别：111、222、333等
// 第648-702行的核心逻辑
// 双重间距判断：间距1合理 && 间距2合理
```

## � 不移植的功能

### 1. 重复表达式检测（不移植到新文件）
- **第19-99行**：`应用复数组合`函数 - 基于210px的复杂检测
- **第102-169行**：`构建重复表达式`函数 - 重复表达式构建
- **第60-95行**：210px距离判断条件 - 硬性距离限制

### 2. 复杂间距算法（不移植到新文件）
- **第239-283行**：`间距合理`函数 - 在三区域模式下不需要
- **第286-314行**：`应用间距算法`函数 - 复杂的全局间距应用

### 3. 硬性条件（不移植到新文件）
- **第63行**：`var 符合距离条件 = 总距离 > 210;` - 210px硬性限制
- **第65行**：`if (所有数字相同 && 符合距离条件)` - 基于距离的判断

## 🏗️ 新文件架构

### 文件名：`公式3.js`

### 主要函数（2个）- 单函数多接口设计
```javascript
// 1. 主函数 - 保持函数名不变，内部集成多接口
function 计算_公式() {
    // 内部调用统一的三区域识别引擎
    var 前数字 = 三区域识别引擎(全屏截图, "前数字");
    var 运算符 = 三区域识别引擎(全屏截图, "运算符");
    var 后数字 = 三区域识别引擎(全屏截图, "后数字");
    // + 答案识别 + 计算匹配 + 点击功能
}

// 2. 答案识别函数 - 保持函数名不变，完整移植
function 答案位置(外部截图) {
    // 完整移植现有的答案识别功能（第390-765行）
    // 包含双数字组合、间距判断等所有逻辑
}
```

### 内部接口设计（单函数多接口）
```javascript
// 统一的三区域识别引擎 - 单函数多接口设计
function 三区域识别引擎(全屏截图, 区域类型) {
    // 根据区域类型选择不同的处理逻辑
    switch(区域类型) {
        case "前数字":
            // 识别38,410,175,153区域
            // 支持单数字、双数字、三数字组合
            // 应用间距判断（含1数字：-5~37px，其他：-7~10px）
            break;
        case "运算符":
            // 识别203,425,103,120区域
            // 识别+、-、×、÷运算符
            break;
        case "后数字":
            // 识别282,418,130,143区域
            // 支持单数字、双数字、三数字组合
            // 应用间距判断（含1数字：-5~37px，其他：-7~10px）
            break;
    }
    // 返回统一格式的识别结果
}
```

## 📊 功能对比

| 功能模块 | 当前文件 | 新文件 | 变化 |
|----------|----------|--------|------|
| **主函数** | `计算_公式()` | `计算_公式()` | ✅保持不变 |
| **答案识别** | `答案位置()` | `答案位置()` | ✅完整移植 |
| **表达式识别** | 单一大区域 | 三个小区域 | 🔄改进 |
| **双数字组合** | ✅支持 | ✅保留 | ✅完整保留 |
| **间距判断** | ✅支持 | ✅保留 | ✅完整保留 |
| **重复表达式** | 复杂检测 | ❌不移植 | 🔄简化 |
| **210px判断** | ✅有 | ❌不移植 | 🔄优化 |
| **点击功能** | ✅有 | ✅保留 | ✅完整保留 |

## 🎯 预期效果

### 性能提升
- **处理速度**：3-5秒 → 1-2秒（提升50-60%）
- **识别精度**：70-80% → 85-95%（提升15-25%）
- **代码行数**：~1124行 → ~600行（减少46%）

### 支持范围
- **单位数计算**：1+2、3×4、5÷6、7-8
- **双位数计算**：11+22、33×44、55÷66、77-88
- **混合计算**：1×22、33+4、55÷6、7-88
- **最大支持**：99×99=9801

### 识别优势
- ✅ **无需距离判断**：每个区域独立识别
- ✅ **无需间距算法**：区域位置固定
- ✅ **无需复杂组合**：直接按区域顺序拼接
- ✅ **支持两位数**：每个数字区域可识别1-2位数

## 📝 实施计划

### 第一阶段：架构设计
1. 创建新文件`公式3.js`
2. 设计三区域识别架构
3. 定义主函数和内部接口

### 第二阶段：功能移植
1. 完整移植`答案位置()`函数（第390-765行）
2. 移植双数字组合逻辑（第610-702行）
3. 移植间距判断逻辑（第624-628行）

### 第三阶段：单函数多接口实现
1. 实现统一的`三区域识别引擎(全屏截图, 区域类型)`函数
2. 集成前数字区域识别（38,410,175,153）
3. 集成运算符区域识别（203,425,103,120）
4. 集成后数字区域识别（282,418,130,143）

### 第四阶段：集成测试
1. 集成三区域结果拼接
2. 保留计算和点击功能
3. 测试双数字识别效果

## 🔍 关键技术点

### 1. 双数字识别算法
```javascript
// 基于真实间距的双数字组合
var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
var 真实间距 = 第二个.x坐标 - 第一个右边缘;

// 间距判断规则
if (第一个.数字 === "1" || 第二个.数字 === "1") {
    间距合理 = (真实间距 >= -5 && 真实间距 < 37);
} else {
    间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
}
```

### 2. 三区域拼接逻辑
```javascript
function 三区域识别表达式(全屏截图) {
    var 前数字 = 识别前数字区域(全屏截图);
    var 运算符 = 识别运算符区域(全屏截图);
    var 后数字 = 识别后数字区域(全屏截图);
    
    if (前数字 && 运算符 && 后数字) {
        var 表达式 = 前数字 + 运算符 + 后数字 + "=";
        return 表达式;
    }
    
    return null;
}
```

### 3. 模板匹配参数
- **表达式数字相似度**：0.72（大幅提高识别率）
- **答案区域相似度**：0.84（提高精度）
- **运算符相似度**：0.9（保持精度）

## 📚 参考资料

- **原文件**：`公式识别完整版.js v4.6.0`
- **AutoXjs文档**：https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html
- **模板匹配算法**：基于OpenCV的归一化互相关系数
- **图像处理**：使用AutoXjs内置的images模块

---

**文档版本**：v1.0  
**创建时间**：2025年1月  
**最后更新**：2025年1月  
**状态**：设计阶段
