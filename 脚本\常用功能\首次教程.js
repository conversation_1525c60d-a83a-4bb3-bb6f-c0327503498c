/**
 * 首次教程模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

// 导入截图模块 - 使用相对路径
var 截图模块 = require('./截图.js');

// 全局左滑坐标数组 - 开始坐标和结束坐标
var 左滑 = [
    {开始: {x: 256, y: 668}, 结束: {x: 10, y: 664}},
    {开始: {x: 264, y: 644}, 结束: {x: 26, y: 672}},
    {开始: {x: 264, y: 644}, 结束: {x: 22, y: 592}},
    {开始: {x: 244, y: 702}, 结束: {x: 22, y: 672}},
    {开始: {x: 244, y: 702}, 结束: {x: 26, y: 700}},
    {开始: {x: 258, y: 656}, 结束: {x: 32, y: 662}},
    {开始: {x: 238, y: 638}, 结束: {x: 30, y: 665}},
    {开始: {x: 238, y: 638}, 结束: {x: 32, y: 688}},
    {开始: {x: 248, y: 672}, 结束: {x: 32, y: 688}},
    {开始: {x: 260, y: 694}, 结束: {x: 37, y: 679}},
    {开始: {x: 260, y: 694}, 结束: {x: 37, y: 679}}
];

// 全局右滑坐标数组 - 开始坐标和结束坐标
var 右滑 = [
    {开始: {x: 140, y: 602}, 结束: {x: 366, y: 628}},
    {开始: {x: 126, y: 620}, 结束: {x: 360, y: 642}},
    {开始: {x: 124, y: 602}, 结束: {x: 334, y: 656}},
    {开始: {x: 124, y: 602}, 结束: {x: 334, y: 656}},
    {开始: {x: 108, y: 634}, 结束: {x: 362, y: 672}},
    {开始: {x: 108, y: 634}, 结束: {x: 346, y: 650}},
    {开始: {x: 108, y: 634}, 结束: {x: 350, y: 630}},
    {开始: {x: 108, y: 634}, 结束: {x: 336, y: 664}},
    {开始: {x: 112, y: 642}, 结束: {x: 342, y: 684}}
];

// 全局上滑坐标数组 - 开始坐标和结束坐标
var 上滑 = [
    {开始: {x: 248, y: 598}, 结束: {x: 278, y: 590}},
    {开始: {x: 296, y: 596}, 结束: {x: 278, y: 428}},
    {开始: {x: 296, y: 596}, 结束: {x: 290, y: 440}},
    {开始: {x: 296, y: 596}, 结束: {x: 246, y: 446}},
    {开始: {x: 254, y: 612}, 结束: {x: 246, y: 446}},
    {开始: {x: 288, y: 598}, 结束: {x: 246, y: 446}},
    {开始: {x: 312, y: 598}, 结束: {x: 262, y: 436}},
    {开始: {x: 244, y: 638}, 结束: {x: 262, y: 436}},
    {开始: {x: 238, y: 622}, 结束: {x: 274, y: 424}}
];

// 全局下滑坐标数组 - 开始坐标和结束坐标
var 下滑 = [
    {开始: {x: 272, y: 618}, 结束: {x: 252, y: 632}},
    {开始: {x: 270, y: 606}, 结束: {x: 270, y: 788}},
    {开始: {x: 270, y: 606}, 结束: {x: 270, y: 788}},
    {开始: {x: 262, y: 624}, 结束: {x: 330, y: 778}},
    {开始: {x: 278, y: 620}, 结束: {x: 286, y: 762}},
    {开始: {x: 280, y: 608}, 结束: {x: 242, y: 764}},
    {开始: {x: 268, y: 614}, 结束: {x: 242, y: 764}},
    {开始: {x: 252, y: 632}, 结束: {x: 278, y: 788}},
    {开始: {x: 252, y: 632}, 结束: {x: 278, y: 788}}
];

function 首次_教程() {
    try {
        console.log("🎓 开始首次教程流程");

        // 第一步：判断感兴趣按钮（重试2次）
        console.log("📋 步骤1: 判断感兴趣按钮（最多重试2次）");
        var 感兴趣存在 = false;
        var 感兴趣重试次数 = 0;
        var 感兴趣最大重试 = 2;

        while (!感兴趣存在 && 感兴趣重试次数 < 感兴趣最大重试) {
            感兴趣重试次数++;
            console.log("🔄 第" + 感兴趣重试次数 + "次尝试查找感兴趣按钮");

            感兴趣存在 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/感兴趣.png",
                27, 260, 478, 616,  // 区域范围 (27,260,478,616)
                2, 200,            // 2秒内每200毫秒查找一次
                "判断"             // 判断模式：找到则点击，找不到则跳过
            );

            if (感兴趣存在) {
                console.log("✅ 感兴趣按钮点击成功（第" + 感兴趣重试次数 + "次尝试）");
                break;
            } else {
                console.log("⚠️ 第" + 感兴趣重试次数 + "次尝试未找到感兴趣按钮");

                // 如果不是最后一次重试，则等待后继续
                if (感兴趣重试次数 < 感兴趣最大重试) {
                    var 感兴趣重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 感兴趣重试等待 + " 毫秒后进行下一次重试");
                    sleep(感兴趣重试等待);
                }
            }
        }

        if (!感兴趣存在) {
            console.log("⚠️ 经过" + 感兴趣最大重试 + "次重试仍未找到感兴趣按钮，继续执行后续步骤");
        }

        // 第二步：随机等待0.5-2秒
        var 随机等待 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待 + " 毫秒后查找右箭头");
        sleep(随机等待);

        // 第三步：点击右箭头（重试2次）
        console.log("📋 步骤2: 点击教程右箭头（最多重试2次）");
        var 右箭头成功 = false;
        var 重试次数 = 0;
        var 最大重试 = 2;

        while (!右箭头成功 && 重试次数 < 最大重试) {
            重试次数++;
            console.log("🔄 第" + 重试次数 + "次尝试查找教程右箭头");

            右箭头成功 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/教程右箭头1.png",
                27, 260, 478, 616,  // 区域范围 (27,260,478,616)
                2, 200,            // 2秒内每200毫秒查找一次
                "判断"             // 判断模式：找到则点击，找不到则跳过
            );

            if (右箭头成功) {
                console.log("✅ 教程右箭头点击成功（第" + 重试次数 + "次尝试）");
                break;
            } else {
                console.log("⚠️ 第" + 重试次数 + "次尝试未找到教程右箭头");

                // 如果不是最后一次重试，则等待后继续
                if (重试次数 < 最大重试) {
                    var 重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 重试等待 + " 毫秒后进行下一次重试");
                    sleep(重试等待);
                }
            }
        }

        if (!右箭头成功) {
            console.log("❌ 经过" + 最大重试 + "次重试仍未找到教程右箭头");
        }

        // 第四步：随机等待后查找进入赛车按钮
        var 赛车等待 = random(500, 2000);
        console.log("⏳ 随机等待 " + 赛车等待 + " 毫秒后查找进入赛车按钮");
        sleep(赛车等待);

        console.log("📋 步骤3: 判断进入赛车按钮（最多重试2次）");
        var 赛车成功 = false;
        var 赛车重试次数 = 0;
        var 赛车最大重试 = 2;

        while (!赛车成功 && 赛车重试次数 < 赛车最大重试) {
            赛车重试次数++;
            console.log("🔄 第" + 赛车重试次数 + "次尝试查找进入赛车按钮");

            赛车成功 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/进入赛车.png",
                23, 398, 486, 530,  // 区域范围 (23,398,486,530)
                2, 200,            // 2秒内每200毫秒查找一次
                "判断"             // 判断模式：找到则点击，找不到则跳过
            );

            if (赛车成功) {
                console.log("✅ 进入赛车按钮点击成功（第" + 赛车重试次数 + "次尝试）");
                break;
            } else {
                console.log("⚠️ 第" + 赛车重试次数 + "次尝试未找到进入赛车按钮");

                // 如果不是最后一次重试，则等待后继续
                if (赛车重试次数 < 赛车最大重试) {
                    var 赛车重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 赛车重试等待 + " 毫秒后进行下一次重试");
                    sleep(赛车重试等待);
                }
            }
        }

        if (!赛车成功) {
            console.log("⚠️ 经过" + 赛车最大重试 + "次重试仍未找到进入赛车按钮，跳过此步骤");
        }

        // 第五步：随机等待后查找游戏开始按钮
        var 游戏开始等待 = random(500, 2000);
        console.log("⏳ 随机等待 " + 游戏开始等待 + " 毫秒后查找游戏开始按钮");
        sleep(游戏开始等待);

        console.log("📋 步骤4: 判断游戏开始按钮（最多重试2次）");
        var 游戏开始成功 = false;
        var 游戏开始重试次数 = 0;
        var 游戏开始最大重试 = 2;

        while (!游戏开始成功 && 游戏开始重试次数 < 游戏开始最大重试) {
            游戏开始重试次数++;
            console.log("🔄 第" + 游戏开始重试次数 + "次尝试查找游戏开始按钮");

            游戏开始成功 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/游戏开始.png",
                61, 358, 442, 506,  // 区域范围 (61,358,442,506)
                2, 200,            // 2秒内每200毫秒查找一次
                "判断"             // 判断模式：找到则点击，找不到则跳过
            );

            if (游戏开始成功) {
                console.log("✅ 游戏开始按钮点击成功（第" + 游戏开始重试次数 + "次尝试）");
                break;
            } else {
                console.log("⚠️ 第" + 游戏开始重试次数 + "次尝试未找到游戏开始按钮");

                // 如果不是最后一次重试，则等待后继续
                if (游戏开始重试次数 < 游戏开始最大重试) {
                    var 游戏开始重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 游戏开始重试等待 + " 毫秒后进行下一次重试");
                    sleep(游戏开始重试等待);
                }
            }
        }

        if (!游戏开始成功) {
            console.log("⚠️ 经过" + 游戏开始最大重试 + "次重试仍未找到游戏开始按钮，跳过此步骤");
        }

        // 第六步：随机等待后查找赛车开始界面并执行左滑
        var 赛车界面等待 = random(500, 2000);
        console.log("⏳ 随机等待 " + 赛车界面等待 + " 毫秒后查找赛车开始界面");
        sleep(赛车界面等待);

        console.log("📋 步骤5: 判断赛车开始界面");
        var 赛车界面成功 = 截图模块.查找_图片(
            "/storage/emulated/0/magic/assets/通用模板/赛车开始界面.png",
            9, 92, 502, 558,  // 区域范围 (9,92,502,558)
            2, 200,           // 2秒内每200毫秒查找一次
            "查找"            // 查找模式：找到则执行左滑，找不到则跳过
        );

        if (赛车界面成功) {
            console.log("✅ 找到赛车开始界面，开始执行左滑操作");

            // 随机选择一个滑动坐标组合
            var 随机索引 = random(0, 左滑.length - 1);
            var 选中坐标 = 左滑[随机索引];

            // 滑动时间范围：800-1500毫秒
            var 滑动时间 = random(800, 1500);

            console.log("🎯 执行左滑: 从(" + 选中坐标.开始.x + "," + 选中坐标.开始.y + ") 到 (" + 选中坐标.结束.x + "," + 选中坐标.结束.y + ") 耗时:" + 滑动时间 + "ms");

            // 执行滑动操作
            swipe(选中坐标.开始.x, 选中坐标.开始.y, 选中坐标.结束.x, 选中坐标.结束.y, 滑动时间);

            console.log("✅ 左滑操作完成");



            console.log("� 步骤6: 判断赛车开始界面2");
            var 赛车界面2成功 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/赛车开始界面2.png",
                9, 92, 502, 558,  // 区域范围 (9,92,502,558)
                2, 200,           // 2秒内每200毫秒查找一次
                "查找"            // 查找模式：找到则执行右滑，找不到则跳过
            );

            if (赛车界面2成功) {
                console.log("✅ 找到赛车开始界面2，开始执行右滑操作");

                // 随机选择一个右滑坐标组合
                var 右滑随机索引 = random(0, 右滑.length - 1);
                var 右滑选中坐标 = 右滑[右滑随机索引];

                // 右滑时间范围：1000-2000毫秒（比左滑稍长一些）
                var 右滑时间 = random(1000, 2000);

                console.log("🎯 执行右滑: 从(" + 右滑选中坐标.开始.x + "," + 右滑选中坐标.开始.y + ") 到 (" + 右滑选中坐标.结束.x + "," + 右滑选中坐标.结束.y + ") 耗时:" + 右滑时间 + "ms");

                // 执行右滑操作
                swipe(右滑选中坐标.开始.x, 右滑选中坐标.开始.y, 右滑选中坐标.结束.x, 右滑选中坐标.结束.y, 右滑时间);

                console.log("✅ 右滑操作完成");

                // 随机延时5-6秒
                var 右滑后等待 = random(5000, 6000);
                console.log("⏳ 右滑完成后等待 " + 右滑后等待 + " 毫秒");
                sleep(右滑后等待);
            } else {
                console.log("⚠️ 未找到赛车开始界面2，跳过右滑操作");
            }
        } else {
            console.log("⚠️ 未找到赛车开始界面，跳过左滑操作");
        }

        // 无论是否找到赛车开始界面，都执行后续的延时和右滑操作
        // 随机延时2-3秒
        var 滑动后等待 = random(2000, 3000);
        console.log("⏳ 等待 " + 滑动后等待 + " 毫秒后继续执行");
        sleep(滑动后等待);

        // 第七步：查找赛车开始界面2并执行右滑
        var 右滑延时 = random(200, 1000);
        console.log("⏳ 随机等待 " + 右滑延时 + " 毫秒后查找赛车开始界面2");
        sleep(右滑延时);

        console.log("📋 步骤6: 判断赛车开始界面2");
        var 赛车界面2成功 = 截图模块.查找_图片(
            "/storage/emulated/0/magic/assets/通用模板/赛车开始界面2.png",
            9, 92, 502, 558,  // 区域范围 (9,92,502,558)
            10, 5000,         // 10秒内每5秒查找一次
            "查找"            // 查找模式：找到则执行右滑，找不到则跳过
        );

        if (赛车界面2成功) {
            console.log("✅ 找到赛车开始界面2，开始执行右滑操作");

            // 随机选择一个右滑坐标组合
            var 右滑随机索引 = random(0, 右滑.length - 1);
            var 右滑选中坐标 = 右滑[右滑随机索引];

            // 右滑时间范围：1000-2000毫秒（比左滑稍长一些）
            var 右滑时间 = random(1000, 2000);

            console.log("🎯 执行右滑: 从(" + 右滑选中坐标.开始.x + "," + 右滑选中坐标.开始.y + ") 到 (" + 右滑选中坐标.结束.x + "," + 右滑选中坐标.结束.y + ") 耗时:" + 右滑时间 + "ms");

            // 执行右滑操作
            swipe(右滑选中坐标.开始.x, 右滑选中坐标.开始.y, 右滑选中坐标.结束.x, 右滑选中坐标.结束.y, 右滑时间);

            console.log("✅ 右滑操作完成");
        } else {
            console.log("⚠️ 未找到赛车开始界面2，跳过右滑操作");
        }

        // 无论是否找到赛车开始界面2，都执行后续的延时和上滑操作
        // 随机延时5-6秒
        var 右滑后等待 = random(5000, 6000);
        console.log("⏳ 等待 " + 右滑后等待 + " 毫秒后执行上滑");
        sleep(右滑后等待);

        console.log("📋 步骤7: 执行上滑操作（最多重试2次）");
        var 上滑成功 = false;
        var 上滑重试次数 = 0;
        var 上滑最大重试 = 2;

        while (!上滑成功 && 上滑重试次数 < 上滑最大重试) {
            上滑重试次数++;
            console.log("🔄 第" + 上滑重试次数 + "次尝试执行上滑操作");

            // 随机选择一个上滑坐标组合
            var 上滑随机索引 = random(0, 上滑.length - 1);
            var 上滑选中坐标 = 上滑[上滑随机索引];

            // 上滑时间范围：1000-2000毫秒
            var 上滑时间 = random(1000, 2000);

            console.log("🎯 执行上滑: 从(" + 上滑选中坐标.开始.x + "," + 上滑选中坐标.开始.y + ") 到 (" + 上滑选中坐标.结束.x + "," + 上滑选中坐标.结束.y + ") 耗时:" + 上滑时间 + "ms");

            // 执行上滑操作
            swipe(上滑选中坐标.开始.x, 上滑选中坐标.开始.y, 上滑选中坐标.结束.x, 上滑选中坐标.结束.y, 上滑时间);

            console.log("✅ 第" + 上滑重试次数 + "次上滑操作完成");

            // 假设上滑成功（因为无法检测上滑结果，所以每次都认为成功）
            上滑成功 = true;

            // 如果不是最后一次重试，则等待后继续
            if (!上滑成功 && 上滑重试次数 < 上滑最大重试) {
                var 上滑重试等待 = random(1000, 2000);
                console.log("⏳ 等待 " + 上滑重试等待 + " 毫秒后进行下一次重试");
                sleep(上滑重试等待);
            }
        }

        if (上滑成功) {
            console.log("✅ 上滑操作最终完成");
        } else {
            console.log("⚠️ 经过" + 上滑最大重试 + "次重试，上滑操作完成");
        }

        // 第九步：上滑后延时5-7秒，然后执行下滑操作
        var 下滑前等待 = random(5000, 7000);
        console.log("⏳ 上滑完成后等待 " + 下滑前等待 + " 毫秒后执行下滑");
        sleep(下滑前等待);

        console.log("📋 步骤8: 执行下滑操作（最多重试2次）");
        var 下滑成功 = false;
        var 下滑重试次数 = 0;
        var 下滑最大重试 = 2;

        while (!下滑成功 && 下滑重试次数 < 下滑最大重试) {
            下滑重试次数++;
            console.log("🔄 第" + 下滑重试次数 + "次尝试执行下滑操作");

            // 随机选择一个下滑坐标组合
            var 下滑随机索引 = random(0, 下滑.length - 1);
            var 下滑选中坐标 = 下滑[下滑随机索引];

            // 下滑时间范围：1000-2000毫秒
            var 下滑时间 = random(1000, 2000);

            console.log("🎯 执行下滑: 从(" + 下滑选中坐标.开始.x + "," + 下滑选中坐标.开始.y + ") 到 (" + 下滑选中坐标.结束.x + "," + 下滑选中坐标.结束.y + ") 耗时:" + 下滑时间 + "ms");

            // 执行下滑操作
            swipe(下滑选中坐标.开始.x, 下滑选中坐标.开始.y, 下滑选中坐标.结束.x, 下滑选中坐标.结束.y, 下滑时间);

            console.log("✅ 第" + 下滑重试次数 + "次下滑操作完成");

            // 假设下滑成功（因为无法检测下滑结果，所以每次都认为成功）
            下滑成功 = true;

            // 如果不是最后一次重试，则等待后继续
            if (!下滑成功 && 下滑重试次数 < 下滑最大重试) {
                var 下滑重试等待 = random(1000, 2000);
                console.log("⏳ 等待 " + 下滑重试等待 + " 毫秒后进行下一次重试");
                sleep(下滑重试等待);
            }
        }

        if (下滑成功) {
            console.log("✅ 下滑操作最终完成");
        } else {
            console.log("⚠️ 经过" + 下滑最大重试 + "次重试，下滑操作完成");
        }

        // 第十步：下滑后延时10-12秒，然后查找结束教程按钮
        var 结束教程前等待 = random(10000, 12000);
        console.log("⏳ 下滑完成后等待 " + 结束教程前等待 + " 毫秒后查找结束教程按钮");
        sleep(结束教程前等待);

        console.log("📋 步骤9: 查找结束教程按钮（最多重试2次）");
        var 结束教程成功 = false;
        var 结束教程重试次数 = 0;
        var 结束教程最大重试 = 7;

        while (!结束教程成功 && 结束教程重试次数 < 结束教程最大重试) {
            结束教程重试次数++;
            console.log("🔄 第" + 结束教程重试次数 + "次尝试查找结束教程按钮");

            结束教程成功 = 截图模块.查找_图片(
                "/storage/emulated/0/magic/assets/通用模板/结束教程.png",
                95, 590, 198, 148,  // 区域范围 (95,590,198,148)
                3, 200,            // 3秒内每200毫秒查找一次
                "判断"             // 判断模式：找到则点击，找不到则跳过
            );

            if (结束教程成功) {
                // 随机延时0.2-0.5秒
                var 点击前延时 = random(200, 500);
                console.log("⏳ 找到结束教程按钮，等待 " + 点击前延时 + " 毫秒后点击");
                sleep(点击前延时);

                console.log("✅ 结束教程按钮点击完成（第" + 结束教程重试次数 + "次尝试）");

                // 点击后验证是否还在结束教程界面
                sleep(1000); // 等待1秒让界面响应
                var 验证结果 = 截图模块.查找_图片(
                    "/storage/emulated/0/magic/assets/通用模板/结束教程.png",
                    95, 590, 198, 148,  // 区域范围 (95,590,198,148)
                    2, 200,            // 2秒内每200毫秒查找一次
                    "查找"             // 查找模式：只查找不点击，返回位置或null
                );

                if (验证结果 === null) {
                    console.log("✅ 验证成功：已离开结束教程界面");
                    结束教程成功 = true;
                    break;
                } else {
                    console.log("⚠️ 验证失败：仍在结束教程界面，点击未生效，继续重试");
                    结束教程成功 = false;
                    // 继续循环，返回到第十步重新查找和点击
                }
            } else {
                console.log("⚠️ 第" + 结束教程重试次数 + "次尝试未找到结束教程按钮");

                // 如果不是最后一次重试，则等待后继续
                if (结束教程重试次数 < 结束教程最大重试) {
                    var 结束教程重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 结束教程重试等待 + " 毫秒后进行下一次重试");
                    sleep(结束教程重试等待);
                }
            }
        }

        if (!结束教程成功) {
            console.log("⚠️ 经过" + 结束教程最大重试 + "次重试仍未找到结束教程按钮，跳过此步骤");
        }

        // 首次教程结束
        console.log("🎓 首次教程结束");

        return true;

    } catch (error) {
        console.error("❌ 首次教程时发生错误: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    首次_教程: 首次_教程
};

// // 直接运行测试（单独运行时取消注释下面3行）
// console.log("🧪 开始测试首次教程功能");
// var 结果 = 首次_教程();
// console.log("🎯 测试结果: " + (结果 ? "✅ 成功" : "❌ 失败"));