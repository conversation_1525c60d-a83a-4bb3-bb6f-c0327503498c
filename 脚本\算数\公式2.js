// 公式2.js - 三区域公式识别简化版 v2.0.0
// 基于公式3.js v1.9.13完整移植，保持函数名不变，功能一模一样
// 简化输出：只保留最终表达式、计算结果、答案区结果
// 支持两位数×两位数的数学表达式识别，集成数字5和6闭合区域检测

// 导入数字5和6闭合区域检测模块
var 数字5和6检测模块 = require('./数字5和6闭合区域检测.js');

// 导入截图功能模块
var 截图模块 = require('../常用功能/截图.js');

// 核心功能：
// - 三区域识别：前数字(39,441,158,101) + 运算符(189,444,95,83) + 后数字(277,440,109,88)
// - 答案识别：四个答案区域的1-3位数字组合识别
// - 数字5和6闭合区域检测：解决识别混淆问题
// - 计算匹配：自动计算表达式并匹配正确答案
// - 自动点击：随机位置点击正确答案

// 相似度阈值配置：
// 【表达式区域】前数字和后数字：0.90
// 【表达式区域】运算符：0.7
// 【答案区域】四个答案区域：0.8


// 请求屏幕捕获权限
auto.waitFor();
if (!requestScreenCapture()) {
    console.log("❌ 屏幕捕获权限获取失败");
    exit();
}

// 统一的三区域识别引擎 - 单函数多接口设计
function 三区域识别引擎(全屏截图, 区域类型) {
    // 根据区域类型定义坐标和模板
    var 区域配置 = {
        "前数字": {
            坐标: [39, 441, 158, 101],
            模板类型: "数字",
            描述: "第一个数字区域"
        },
        "运算符": {
            坐标: [189, 444, 95, 83],
            模板类型: "运算符",
            描述: "运算符区域"
        },
        "后数字": {
            坐标: [277, 440, 109, 88],
            模板类型: "数字",
            描述: "第二个数字区域"
        }
    };

    var 当前配置 = 区域配置[区域类型];
    if (!当前配置) {
        return null;
    }

    // 截取指定区域
    var 区域图像 = images.clip(全屏截图, 当前配置.坐标[0], 当前配置.坐标[1], 当前配置.坐标[2], 当前配置.坐标[3]);
    if (!区域图像) {
        return null;
    }

    // 灰度处理
    var 灰度图像 = images.grayscale(区域图像);
    区域图像.recycle();

    if (!灰度图像) {
        return null;
    }
    
    // 根据模板类型选择不同的识别逻辑
    var 识别结果 = null;

    if (当前配置.模板类型 === "数字") {
        识别结果 = 识别数字区域(灰度图像, 区域类型, 当前配置);
    } else if (当前配置.模板类型 === "运算符") {
        识别结果 = 识别运算符区域(灰度图像, 区域类型, 当前配置);
    }

    // 清理资源
    灰度图像.recycle();

    return 识别结果;
}

// 数字区域识别函数（支持双数字组合和间距判断）
function 识别数字区域(灰度图像, 区域类型, 区域配置) {
    
    // 加载数字模板（分离式多模板设计）
    var 数字模板 = {
        "0": {图片: images.read(files.path("../../assets/算数游戏/0.png")), 类型: "标准"},
        "0_1": {图片: images.read(files.path("../../assets/算数游戏/0_1.png")), 类型: "变体", 实际数字: "0"},
        "1": {图片: images.read(files.path("../../assets/算数游戏/1.png")), 类型: "标准"},
        "1_1": {图片: images.read(files.path("../../assets/算数游戏/1_1.png")), 类型: "变体", 实际数字: "1"},
        "2": {图片: images.read(files.path("../../assets/算数游戏/2.png")), 类型: "标准"},
        "2_1": {图片: images.read(files.path("../../assets/算数游戏/2_1.png")), 类型: "变体", 实际数字: "2"},
        "2_3": {图片: images.read(files.path("../../assets/算数游戏/2_3.png")), 类型: "变体", 实际数字: "2"},
        "3": {图片: images.read(files.path("../../assets/算数游戏/3.png")), 类型: "标准"},
        "3_1": {图片: images.read(files.path("../../assets/算数游戏/3_1.png")), 类型: "变体", 实际数字: "3"},
        "4": {图片: images.read(files.path("../../assets/算数游戏/4.png")), 类型: "标准"},
        "4_1": {图片: images.read(files.path("../../assets/算数游戏/4_1.png")), 类型: "变体", 实际数字: "4"},
        "5": {图片: images.read(files.path("../../assets/算数游戏/5.png")), 类型: "标准"},
        "5_1": {图片: images.read(files.path("../../assets/算数游戏/5_1.png")), 类型: "变体", 实际数字: "5"},
        "6": {图片: images.read(files.path("../../assets/算数游戏/6.png")), 类型: "标准"},
        "6_1": {图片: images.read(files.path("../../assets/算数游戏/6_1.png")), 类型: "变体", 实际数字: "6"},
        "7": {图片: images.read(files.path("../../assets/算数游戏/7.png")), 类型: "标准"},
        "7_1": {图片: images.read(files.path("../../assets/算数游戏/7_1.png")), 类型: "变体", 实际数字: "7"},
        "8": {图片: images.read(files.path("../../assets/算数游戏/8.png")), 类型: "标准"},
        "8_1": {图片: images.read(files.path("../../assets/算数游戏/8_1.png")), 类型: "变体", 实际数字: "8"},
        "9": {图片: images.read(files.path("../../assets/算数游戏/9.png")), 类型: "标准"},
        "9_1": {图片: images.read(files.path("../../assets/算数游戏/9_1.png")), 类型: "变体", 实际数字: "9"},
        "9_2": {图片: images.read(files.path("../../assets/算数游戏/9_2.png")), 类型: "变体", 实际数字: "9"}
    };

    // 动态获取模板尺寸（分离式设计）
    for (var 模板键 in 数字模板) {
        var 模板 = 数字模板[模板键];
        if (模板.图片) {
            模板.尺寸 = {
                宽: 模板.图片.getWidth(),
                高: 模板.图片.getHeight()
            };
        }
    }
    
    // 识别所有数字（分离式多模板匹配）
    var 找到的数字 = [];
    var 数字相似度阈值 = 0.90; // 【表达式区域】前数字和后数字的相似度阈值/表达式的阈值

    for (var 模板键 in 数字模板) {
        var 模板信息 = 数字模板[模板键];

        if (!模板信息.图片) {
            continue;
        }

        // 确保模板也是灰度图像
        var 灰度模板 = images.grayscale(模板信息.图片);
        if (!灰度模板) {
            continue;
        }

        var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
            threshold: 数字相似度阈值,
            max: 5,
            level: -1
        });

        // 清理模板资源
        灰度模板.recycle();

        if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
            匹配结果.matches.forEach(function(match) {
                // 面积验证已移除，直接使用相似度结果
                if (true) {
                    // 计算中心点坐标（需要加上区域偏移量）
                    var 中心x = match.point.x + Math.round(模板信息.尺寸.宽 / 2);
                    var 中心y = match.point.y + Math.round(模板信息.尺寸.高 / 2);

                    // 确定实际数字（处理变体模板）
                    var 实际数字 = 模板信息.实际数字 || 模板键;
                    var 最终相似度 = match.similarity;
                    var 验证详情 = "";

                    // 对数字5和6进行闭合区域检测验证
                    if (实际数字 === "5" || 实际数字 === "6") {
                        try {
                            // 提取匹配区域的图像
                            var 匹配区域图像 = images.clip(灰度图像, match.point.x, match.point.y, 模板信息.尺寸.宽, 模板信息.尺寸.高);
                            if (匹配区域图像) {
                                var 验证结果 = 数字5和6检测模块.数字5和6闭合区域检测(匹配区域图像, 实际数字, match.similarity);
                                最终相似度 = 验证结果.置信度;
                                验证详情 = 验证结果.检测详情;
                                匹配区域图像.recycle();
                            }
                        } catch (e) {
                            // 静默处理验证异常
                        }
                    }

                    找到的数字.push({
                        数字: 实际数字,
                        x坐标: match.point.x,
                        y坐标: match.point.y,
                        中心点: [中心x, 中心y], // 这里是相对于区域的中心点
                        相似度: 最终相似度, // 使用验证后的相似度
                        原始相似度: match.similarity, // 保留原始相似度
                        模板尺寸: 模板信息.尺寸,
                        模板类型: 模板信息.类型,
                        模板键: 模板键,
                        位置: match.point,
                        验证详情: 验证详情
                    });
                }
            });
        }
    }
    
    // console.log("🔍 " + 区域类型 + "找到数字: 【" + 找到的数字.length + "】个");

    if (找到的数字.length === 0) {
        return null;
    }
    
    // 按x坐标排序
    找到的数字.sort(function(a, b) { return a.x坐标 - b.x坐标; });
    
    // 应用双数字组合逻辑（移植自原文件第610-646行）
    var 最佳组合 = null;
    var 最高总相似度 = 0;
    
    // 尝试单个数字
    for (var s = 0; s < 找到的数字.length; s++) {
        var 单个数字 = 找到的数字[s];
        if (单个数字.相似度 > 最高总相似度) {
            最高总相似度 = 单个数字.相似度;
            最佳组合 = [单个数字];
        }
    }
    
    // 尝试两个数字的组合（基于真实间距规律）
    for (var i = 0; i < 找到的数字.length - 1; i++) {
        for (var j = i + 1; j < 找到的数字.length; j++) {
            var 第一个 = 找到的数字[i];
            var 第二个 = 找到的数字[j];
            
            // 正确计算间距：第一个数字右边缘到第二个数字左边缘的距离
            var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
            var 真实间距 = 第二个.x坐标 - 第一个右边缘;

            // 根据数字类型判断合理间距
            var 间距合理 = false;

            if (第一个.数字 === "1" || 第二个.数字 === "1") {
                // 包含数字1：间距应该 < 37像素
                间距合理 = (真实间距 >= -5 && 真实间距 < 37);
            } else {
                // 其他数字组合：间距应该 -7~10像素
                间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
            }

            if (间距合理) {
                var 总相似度 = 第一个.相似度 + 第二个.相似度;
                if (总相似度 > 最高总相似度) {
                    最高总相似度 = 总相似度;
                    最佳组合 = [第一个, 第二个];
                }
            }
        }
    }



    // 构建最终数字
    var 完整数字 = "";
    if (最佳组合) {
        最佳组合.forEach(function(数字) {
            完整数字 += 数字.数字;
        });
        // console.log("🔍 " + 区域类型 + "识别结果: 【" + 完整数字 + "】");
    }

    return 完整数字 || null;
}

// 运算符区域识别函数
function 识别运算符区域(灰度图像, 区域类型, 区域配置) {
    
    // 加载运算符模板
    var 运算符模板 = {
        "+": {图片: images.read(files.path("../../assets/算数游戏/+.png"))},
        "-": {图片: images.read(files.path("../../assets/算数游戏/减号.png"))},
        "×": {图片: images.read(files.path("../../assets/算数游戏/x.png"))},
        "÷": {图片: images.read(files.path("../../assets/算数游戏/除.png"))}
    };

    // 动态获取运算符模板尺寸
    for (var 运算符 in 运算符模板) {
        if (运算符模板[运算符].图片) {
            运算符模板[运算符].尺寸 = {
                宽: 运算符模板[运算符].图片.getWidth(),
                高: 运算符模板[运算符].图片.getHeight()
            };
        }
    }
    
    var 运算符相似度阈值 = 0.7; // 【表达式区域】运算符的相似度阈值
    var 最佳运算符 = null;
    var 最高相似度 = 0;
    var 所有运算符结果 = [];

    for (var 运算符 in 运算符模板) {
        var 模板信息 = 运算符模板[运算符];
        if (!模板信息.图片) {
            continue;
        }

        // 确保模板也是灰度图像
        var 灰度模板 = images.grayscale(模板信息.图片);
        if (!灰度模板) {
            continue;
        }

        var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
            threshold: 运算符相似度阈值, // 使用正常的运算符相似度阈值
            max: 1,
            level: -1
        });

        // 清理模板资源
        灰度模板.recycle();

        if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
            var match = 匹配结果.matches[0];
            var 实际相似度 = match.similarity;

            // 面积验证已移除，直接使用相似度结果
            // 记录结果
            所有运算符结果.push({
                运算符: 运算符,
                相似度: 实际相似度,
                位置: match.point,
                达到阈值: true, // 已通过matchTemplate阈值
                面积验证: true // 面积验证已移除，默认通过
            });

            if (实际相似度 > 最高相似度) {
                最高相似度 = 实际相似度;
                最佳运算符 = 运算符;
            }
        }
    }
    

    
    if (最佳运算符) {
        // console.log("🔍 运算符识别结果: 【" + 最佳运算符 + "】");
    }

    return 最佳运算符;
}

// 答案位置识别函数 - 完整移植自原文件，保持函数名不变
function 答案位置(外部截图) {
    try {
        // 使用外部传入的截图
        var 屏幕图 = 外部截图;
        if (!屏幕图) {
            return {};
        }

        // 定义四个答案区域 - 完全复制原文件
        var 答案区域列表 = [
            {名称: "黄区", 坐标: [103, 648, 136, 68], 颜色: "🟨"},
            {名称: "蓝区", 坐标: [303, 650, 136, 66], 颜色: "🟦"},
            {名称: "绿区", 坐标: [103, 788, 131, 70], 颜色: "🟩"},
            {名称: "红区", 坐标: [303, 785, 131, 71], 颜色: "🟥"}
        ];

        // 加载数字模板 - 答案区域专用
        var 数字模板 = {
            "0": {图片: images.read(files.path("../../assets/算数游戏/答案数字/0.png"))},
            "1": {图片: images.read(files.path("../../assets/算数游戏/答案数字/1.png"))},
            "2": {图片: images.read(files.path("../../assets/算数游戏/答案数字/2.png"))},
            "3": {图片: images.read(files.path("../../assets/算数游戏/答案数字/3.png"))},
            "4": {图片: images.read(files.path("../../assets/算数游戏/答案数字/4.png"))},
            "5": {图片: images.read(files.path("../../assets/算数游戏/答案数字/5.png"))},
            "6": {图片: images.read(files.path("../../assets/算数游戏/答案数字/6.png"))},
            "7": {图片: images.read(files.path("../../assets/算数游戏/答案数字/7.png"))},
            "8": {图片: images.read(files.path("../../assets/算数游戏/答案数字/8.png"))},
            "9": {图片: images.read(files.path("../../assets/算数游戏/答案数字/9.png"))}
        };

        // 动态获取答案数字模板尺寸
        for (var 数字 in 数字模板) {
            if (数字模板[数字].图片) {
                数字模板[数字].尺寸 = {
                    宽: 数字模板[数字].图片.getWidth(),
                    高: 数字模板[数字].图片.getHeight()
                };
            }
        }

        // 检查模板加载情况
        var 成功加载数 = 0;
        for (var 数字 in 数字模板) {
            if (数字模板[数字].图片) {
                成功加载数++;
            }
        }


        // 识别结果
        var 识别结果 = {};

        // 遍历每个答案区域 - 移植原文件逻辑
        console.log("------------------------答案区域识别功能--------------------------");
        答案区域列表.forEach(function(区域, 索引) {
            // 截取区域
            var 区域图像 = images.clip(屏幕图, 区域.坐标[0], 区域.坐标[1], 区域.坐标[2], 区域.坐标[3]);
            if (!区域图像) {
                return;
            }

            // 灰度处理
            var 灰度图像 = images.grayscale(区域图像);
            区域图像.recycle();

            if (!灰度图像) {
                return;
            }

            // 识别数字（应用双数字组合逻辑）
            var 找到的数字 = [];
            var 答案相似度阈值 = 0.8; // 【答案区域阈值】四个答案区域的数字相似度阈值

            for (var 数字 in 数字模板) {
                var 模板信息 = 数字模板[数字];
                if (!模板信息.图片) continue;

                // 确保模板也是灰度图像
                var 灰度模板 = images.grayscale(模板信息.图片);
                if (!灰度模板) continue;

                var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
                    threshold: 答案相似度阈值, // 答案区域保持原阈值
                    max: 5,
                    level: -1
                });

                // 清理模板资源
                灰度模板.recycle();

                if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
                    匹配结果.matches.forEach(function(match) {
                        var 最终相似度 = match.similarity;
                        var 验证详情 = "";

                        // 对数字5和6进行闭合区域检测验证（答案区域）
                        if (数字 === "5" || 数字 === "6") {
                            try {
                                // 提取匹配区域的图像
                                var 匹配区域图像 = images.clip(灰度图像, match.point.x, match.point.y, 模板信息.尺寸.宽, 模板信息.尺寸.高);
                                if (匹配区域图像) {
                                    // 答案区域使用固定阈值40（答案区闭合阈值）
                                    var 答案区阈值 = 40; // 答案区域闭合阈值，经过测试确定
                                    var 验证结果 = 数字5和6检测模块.数字5和6闭合区域检测(匹配区域图像, 数字, match.similarity, 答案区阈值);
                                    最终相似度 = 验证结果.置信度;
                                    验证详情 = 验证结果.检测详情;
                                    匹配区域图像.recycle();

                                }
                            } catch (e) {
                                // 静默处理验证异常
                            }
                        }

                        // 计算中心点坐标（需要加上区域偏移量）
                        var 中心x = match.point.x + Math.round(模板信息.尺寸.宽 / 2);
                        var 中心y = match.point.y + Math.round(模板信息.尺寸.高 / 2);

                        找到的数字.push({
                            数字: 数字,
                            x坐标: match.point.x,
                            y坐标: match.point.y,
                            中心点: [中心x, 中心y], // 这里是相对于区域的中心点
                            相似度: 最终相似度, // 使用验证后的相似度
                            原始相似度: match.similarity, // 保留原始相似度
                            模板尺寸: 模板信息.尺寸,
                            验证详情: 验证详情
                        });
                    });
                }
            }




            if (找到的数字.length > 0) {
                // 按x坐标排序
                找到的数字.sort(function(a, b) { return a.x坐标 - b.x坐标; });

                // 应用双数字组合逻辑（简化版本）
                var 最佳组合 = null;
                var 最高总相似度 = 0;

                // 尝试单个数字
                for (var s = 0; s < 找到的数字.length; s++) {
                    var 单个数字 = 找到的数字[s];
                    if (单个数字.相似度 > 最高总相似度) {
                        最高总相似度 = 单个数字.相似度;
                        最佳组合 = [单个数字];
                    }
                }

                // 尝试两个数字的组合
                for (var i = 0; i < 找到的数字.length - 1; i++) {
                    for (var j = i + 1; j < 找到的数字.length; j++) {
                        var 第一个 = 找到的数字[i];
                        var 第二个 = 找到的数字[j];

                        // 计算间距
                        var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
                        var 真实间距 = 第二个.x坐标 - 第一个右边缘;

                        // 间距判断
                        var 间距合理 = false;
                        if (第一个.数字 === "1" || 第二个.数字 === "1") {
                            间距合理 = (真实间距 >= -5 && 真实间距 < 37);
                        } else {
                            间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
                        }

                        if (间距合理) {
                            var 总相似度 = 第一个.相似度 + 第二个.相似度;
                            if (总相似度 > 最高总相似度) {
                                最高总相似度 = 总相似度;
                                最佳组合 = [第一个, 第二个];
                            }
                        }
                    }
                }

                // 尝试三个数字的组合
                for (var i = 0; i < 找到的数字.length - 2; i++) {
                    for (var j = i + 1; j < 找到的数字.length - 1; j++) {
                        for (var k = j + 1; k < 找到的数字.length; k++) {
                            var 第一个 = 找到的数字[i];
                            var 第二个 = 找到的数字[j];
                            var 第三个 = 找到的数字[k];

                            // 计算第一个到第二个的间距
                            var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
                            var 间距1 = 第二个.x坐标 - 第一个右边缘;

                            // 计算第二个到第三个的间距
                            var 第二个右边缘 = 第二个.x坐标 + 第二个.模板尺寸.宽;
                            var 间距2 = 第三个.x坐标 - 第二个右边缘;

                            // 判断两个间距是否都合理
                            var 间距1合理 = false;
                            var 间距2合理 = false;

                            if (第一个.数字 === "1" || 第二个.数字 === "1") {
                                间距1合理 = (间距1 >= -5 && 间距1 < 37);
                            } else {
                                间距1合理 = (间距1 >= -7 && 间距1 <= 10);
                            }

                            if (第二个.数字 === "1" || 第三个.数字 === "1") {
                                间距2合理 = (间距2 >= -5 && 间距2 < 37);
                            } else {
                                间距2合理 = (间距2 >= -7 && 间距2 <= 10);
                            }

                            if (间距1合理 && 间距2合理) {
                                var 总相似度 = 第一个.相似度 + 第二个.相似度 + 第三个.相似度;
                                if (总相似度 > 最高总相似度) {
                                    最高总相似度 = 总相似度;
                                    最佳组合 = [第一个, 第二个, 第三个];
                                }
                            }
                        }
                    }
                }

                // 构建最终数字
                var 完整数字 = "";
                if (最佳组合) {
                    最佳组合.forEach(function(数字) {
                        完整数字 += 数字.数字;
                    });

                    // 计算中心点坐标
                    var 最左x = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标; }));
                    var 最右x = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标 + 数字.模板尺寸.宽; }));
                    var 最上y = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标; }));
                    var 最下y = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标 + 数字.模板尺寸.高; }));

                    var 中心x = 区域.坐标[0] + (最左x + 最右x) / 2;
                    var 中心y = 区域.坐标[1] + (最上y + 最下y) / 2;

                    识别结果[区域.名称] = {
                        数字: 完整数字,
                        坐标: [区域.坐标[0] + 最左x, 区域.坐标[1] + 最上y, 最右x - 最左x, 最下y - 最上y],
                        中心点: [Math.round(中心x), Math.round(中心y)]
                    };

                    console.log("    ✅ " + 区域.名称 + "识别成功: 【" + 完整数字 + "】 坐标范围【" + (区域.坐标[0] + 最左x) + "," + (区域.坐标[1] + 最上y) + "," + (最右x - 最左x) + "," + (最下y - 最上y) + "】");
                }
            } else {
                console.log("    ❌ " + 区域.名称 + "未找到数字");
            }

            // 清理资源
            灰度图像.recycle();
        });

        return 识别结果;

    } catch (e) {
        console.log("❌ 答案识别失败: " + e);
        return {};
    }
}

// 主要计算函数 - 保持函数名不变，采用三区域识别
function 计算_公式() {
    // 第一步：检测是否在开始界面
    console.log("🔍 第一步：检测是否在开始界面...");
    var 检测是否在开始界面 = "../../assets/算数游戏/新手教程图片/点击分数.png";

    var 开始界面检测结果 = 截图模块.查找_图片(
        检测是否在开始界面,    // 图片路径
        2,                     // 区域x
        2,                     // 区域y
        536,                   // 区域宽
        120,                   // 区域高
        20,                    // 等待秒数（20秒）
        500,                   // 间隔毫秒（每0.5秒找一次）
        "查找",                // 查找动作
        1,                     // 重试1次
        0.9                    // 置信度0.9
    );

    if (开始界面检测结果) {
        console.log("✅ 检测到开始界面，开始进入表达式识别");
    } else {
        console.log("⚠️ 未检测到开始界面，但继续执行表达式识别");
    }


    
    var 循环次数 = 107; // 固定循环次数为107次
    // 在103-106次范围内随机选择一次来故意点击错误答案
    var 故意点错次数 = Math.floor(Math.random() * 4) + 103; // 随机选择103-106次中的一次
    console.log("🎯 本次执行循环次数: " + 循环次数);
    console.log("🎯 故意点错次数: " + 故意点错次数);

    for (var 循环 = 1; 循环 <= 循环次数; 循环++) {
        try {
            // 一次性截取全屏，避免重复截屏
            var 全屏截图 = captureScreen();
            if (!全屏截图) {
                console.log("❌ 全屏截图失败");
                continue; // 截图失败，跳过本次循环
            } else {
                console.log("------------------------截图功能--------------------------");
                console.log("✅ 全屏截图成功，开始三区域识别");
            }

            // 1. 三区域识别表达式
            // console.log("------------------------三区域识别功能--------------------------");
            var 前数字结果 = 三区域识别引擎(全屏截图, "前数字");
            // console.log("------------------------运算符区域--------------------------");
            var 运算符结果 = 三区域识别引擎(全屏截图, "运算符");
            // console.log("------------------------后数字区域--------------------------");
            var 后数字结果 = 三区域识别引擎(全屏截图, "后数字");

            // 2. 识别结果汇总
            console.log("------------------------识别结果汇总--------------------------");


            // 拼接表达式
            var 表达式 = null;
            if (前数字结果 && 运算符结果 && 后数字结果) {
                表达式 = 前数字结果 + 运算符结果 + 后数字结果 + "=";
                console.log("✅ 三区域识别成功: 【" + 表达式 + "】");
            } else {
                console.log("❌ 表达式识别不完整:");
                console.log("  前数字: " + (前数字结果 || "未识别"));
                console.log("  运算符: " + (运算符结果 || "未识别"));
                console.log("  后数字: " + (后数字结果 || "未识别"));
                console.log("⚠️ 表达式识别不完整，跳过本次循环，继续下一次");

                // 清理资源
                全屏截图.recycle();
                continue; // 跳过本次循环，继续下一次
            }

            var 公式结果 = 表达式;

            // 2. 识别答案区域（调用移植的答案位置函数）
            console.log("------------------------答案识别功能--------------------------");
            var 答案结果 = 答案位置(全屏截图);

            // 3. 答案识别完成
            console.log("------------------------答案识别完成--------------------------");

            // 3. 统一清理全屏截图资源
            全屏截图.recycle();

            // 4. 计算表达式并找到正确答案
            var 计算结果 = null;
            var 正确答案区域 = null;

            // 解析并计算表达式
            if (公式结果 && 公式结果 !== "无" && 公式结果 !== "识别失败") {
                try {
                    // 移除等号，只保留表达式部分
                    var 计算表达式 = 公式结果.replace(/=/g, "");

                    // 替换运算符为JavaScript可识别的符号
                    计算表达式 = 计算表达式.replace(/×/g, "*");
                    计算表达式 = 计算表达式.replace(/÷/g, "/");

                    // 安全计算表达式
                    if (/^[\d+\-*/\s()]+$/.test(计算表达式)) {
                        计算结果 = eval(计算表达式);

                        // 在答案区域中查找正确答案
                        for (var 区域名 in 答案结果) {
                            var 区域数据 = 答案结果[区域名];
                            if (区域数据.数字 == 计算结果) {
                                正确答案区域 = {
                                    区域名: 区域名,
                                    数字: 区域数据.数字,
                                    坐标: 区域数据.坐标,
                                    中心点: 区域数据.中心点
                                };
                                break;
                            }
                        }
                    } else {
                        console.log("❌ 表达式包含非法字符: " + 计算表达式);
                    }
                } catch (e) {
                    console.log("❌ 计算表达式失败: " + e);
                }
            }

            console.log("------------------------计算匹配功能--------------------------");
            console.log("🧮 计算匹配:");
            console.log("  表达式: " + 公式结果);
            console.log("  计算结果: " + (计算结果 || "失败"));
            console.log("  正确答案: " + (正确答案区域 ? 正确答案区域.区域名 + "【" + 正确答案区域.数字 + "】 坐标范围【" + 正确答案区域.坐标[0] + "," + 正确答案区域.坐标[1] + "," + 正确答案区域.坐标[2] + "," + 正确答案区域.坐标[3] + "】" : "未找到"));

            // 5. 返回完整结果（包含计算结果和正确答案位置）
            var 完整结果 = {
                公式: 公式结果,
                答案: 答案结果,
                计算结果: 计算结果,
                正确答案: 正确答案区域
            };

            console.log("------------------------最终结果输出--------------------------");
            console.log("🎯 最终结果:");
            console.log("  " + 完整结果.公式 + " → 【" + (完整结果.计算结果 || "失败") + "】 → " + (完整结果.正确答案 ? 完整结果.正确答案.区域名 + "【" + 完整结果.正确答案.数字 + "】 坐标范围【" + 完整结果.正确答案.坐标[0] + "," + 完整结果.正确答案.坐标[1] + "," + 完整结果.正确答案.坐标[2] + "," + 完整结果.正确答案.坐标[3] + "】" : "未找到"));

            // 6. 点击答案（根据循环次数决定点击正确还是错误答案）
            if (完整结果.正确答案) {
                console.log("------------------------点击功能--------------------------");

                // 判断是否故意点击错误答案（只在指定的随机次数时触发）
                var 故意点错 = (循环 === 故意点错次数);
                var 目标答案区域 = null;
                var 点击类型 = "";

                if (故意点错) {
                    // 故意点击错误答案：从其他答案区域中随机选择一个
                    var 错误答案列表 = [];
                    for (var 区域名 in 答案结果) {
                        var 区域数据 = 答案结果[区域名];
                        // 排除正确答案，收集错误答案
                        if (区域数据.数字 != 计算结果) {
                            错误答案列表.push({
                                区域名: 区域名,
                                数字: 区域数据.数字,
                                坐标: 区域数据.坐标,
                                中心点: 区域数据.中心点
                            });
                        }
                    }

                    if (错误答案列表.length > 0) {
                        // 随机选择一个错误答案
                        var 随机索引 = Math.floor(Math.random() * 错误答案列表.length);
                        目标答案区域 = 错误答案列表[随机索引];
                        点击类型 = "到达次数" + 循环 + "次了，点击错误答案区域结束";
                    } else {
                        // 如果没有错误答案，还是点击正确答案
                        目标答案区域 = 完整结果.正确答案;
                        点击类型 = "到达次数" + 循环 + "次了，无错误选项，点击正确答案";
                    }
                } else {
                    // 正常点击正确答案
                    目标答案区域 = 完整结果.正确答案;
                    点击类型 = "✅ 点击正确答案";
                }

                // 在坐标范围内随机生成点击位置
                var 答案坐标 = 目标答案区域.坐标;
                var 随机x = 答案坐标[0] + Math.floor(Math.random() * 答案坐标[2]);
                var 随机y = 答案坐标[1] + Math.floor(Math.random() * 答案坐标[3]);

                console.log("🖱️ " + 点击类型 + ": " + 目标答案区域.区域名 + "【" + 目标答案区域.数字 + "】 坐标范围【" + 答案坐标[0] + "," + 答案坐标[1] + "," + 答案坐标[2] + "," + 答案坐标[3] + "】 随机点击位置【" + 随机x + "," + 随机y + "】");

                // 随机延时150-200毫秒
                var 随机延时 = Math.floor(Math.random() * (200 - 100 + 1)) + 150;
                sleep(随机延时);

                click(随机x, 随机y);
                console.log("✅ 已执行点击，延时: " + 随机延时 + "毫秒");
            } else {
                console.log("❌ 未找到正确答案");
                console.log("⏭️ 跳过点击，继续下一次循环");
            }

            // 循环间隔
            if (循环 < 循环次数) {
                var 循环延时 = Math.floor(Math.random() * (300 - 100 + 1)) + 200;
                sleep(循环延时); // 每次循环间隔250-300毫秒
                console.log("⏱️ 循环间隔: " + 循环延时 + "毫秒");
            }

        } catch (e) {
            console.log("❌ 第" + 循环 + "次循环执行失败: " + e);
        }

        // 在循环结束时显示循环次数
        console.log("------------------------第" + 循环 + "次循环完成--------------------------");
    }

    console.log("-------------V：1.9.13-----------执行完成--------------------------");
}



// 导出模块
module.exports = {
    计算_公式: 计算_公式
};
