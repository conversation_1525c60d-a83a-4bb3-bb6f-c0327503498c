/**
 * 批量截图测试
 * 截图100张并保存
 */

// 请求截图权限
if (!requestScreenCapture()) {
    console.log("❌ 截图权限获取失败");
    exit();
}
console.log("✅ 截图权限获取成功");

/**
 * 批量截图功能
 */
function 批量截图() {
    try {
        console.log("🎯 开始批量截图");
        
        // 使用测试成功的路径
        var 保存目录 = "/sdcard/Pictures/Screenshots";

        console.log("✅ 使用目录: " + 保存目录);
        
        // 截图参数
        var 总数量 = 100;
        var 成功数量 = 0;
        var 失败数量 = 0;
        var 开始时间 = new Date().getTime();
        
        console.log("📋 开始截图，共" + 总数量 + "张");
        
        for (var i = 1; i <= 总数量; i++) {
            try {
                // 生成文件名
                var 时间戳 = new Date().getTime();
                var 文件名 = "magic_" + i + "_" + 时间戳 + ".png";
                var 文件路径 = files.join(保存目录, 文件名);
                
                // 截图
                captureScreen(文件路径);
                
                // 验证文件
                if (files.exists(文件路径)) {
                    成功数量++;
                    if (i % 10 === 0) {
                        console.log("✅ 已完成 " + i + "/" + 总数量 + " 张截图");
                    }
                } else {
                    失败数量++;
                    console.log("❌ 第" + i + "张截图文件未找到");
                }
                
                // 无延时，以最快速度截图
                
            } catch (error) {
                失败数量++;
                console.error("❌ 第" + i + "张截图发生错误: " + error);
            }
        }
        
        var 结束时间 = new Date().getTime();
        var 总用时 = 结束时间 - 开始时间;
        
        console.log("🎉 批量截图完成!");
        console.log("📊 统计结果:");
        console.log("   成功截图: " + 成功数量 + " 张");
        console.log("   失败截图: " + 失败数量 + " 张");
        console.log("   总用时: " + 总用时 + " 毫秒 (" + (总用时/1000).toFixed(2) + " 秒)");
        if (成功数量 > 0) {
            console.log("   平均速度: " + (总用时/成功数量).toFixed(2) + " 毫秒/张");
        }
        console.log("   保存目录: " + 保存目录);
        
        return 成功数量 > 0;
        
    } catch (error) {
        console.error("❌ 批量截图发生错误: " + error);
        return false;
    }
}

// 运行测试
console.log("🧪 开始执行批量截图测试");
var 结果 = 批量截图();
console.log("🎯 测试结果: " + (结果 ? "✅ 成功" : "❌ 失败"));
