// 公式3.js - 三区域公式识别 v1.9.13
// 基于公式识别完整版.js v4.6.0，采用三区域识别方案
// 支持两位数×两位数的数学表达式识别，保持函数名不变
// 区域定义：前数字(39,441,158,101) + 运算符(189,444,95,83) + 后数字(277,440,109,88)
// 移植功能：答案识别、双数字组合、间距判断、计算匹配、点击功能
// 新增功能：动态模板尺寸获取，统一相似度阈值，优化识别精度

// 导入数字5和6闭合区域检测模块
var 数字5和6检测模块 = require('./数字5和6闭合区域检测.js');
// v1.4.0修改：移除标注系统，纯验证模式
// v1.5.0新增：移除硬编码尺寸，动态获取模板尺寸，统一相似度阈值0.8，优化识别精度
// v1.6.0新增：为表达式区数字增加相似度输出，一行显示所有数字的相似度信息
// v1.6.1优化：在识别结果中增加每个数字的相似度详情和总相似度显示
// v1.6.2修复：修正中心点坐标计算，加上区域偏移量，显示全屏坐标系的真实中心点
// v1.7.0重大更新：支持多尺寸模板匹配，为数字0增加变体模板0_1.png，自动选择最佳匹配
// v1.7.1调试版本：增加详细的模板加载和匹配调试信息，排查识别问题
// v1.7.2重构版本：改用分离式多模板设计，将0_1作为独立模板键，避免数组遍历问题
// v1.7.3修复版本：统一中心点坐标显示，所有输出都使用全屏坐标系，增加模板类型标识
// v1.7.4调试版本：增加详细的双数字组合间距分析调试信息，排查间距判断问题
// v1.7.5扩展版本：为数字2增加变体模板2_1.png，支持更多数字的多尺寸识别
// v1.7.6调整版本：修改后数字区域坐标为(274,445,108,70)，优化识别范围
// v1.7.7调整版本：修改后数字区域坐标为(277,440,109,88)，优化后数字识别范围
// v1.7.8扩展版本：为数字1增加变体模板1_1.png，支持数字1的多尺寸识别
// v1.7.9扩展版本：为数字2增加第二个变体模板2_3.png，数字2现在有3个模板
// v1.8.0优化版本：优化变体模板显示，输出具体模板键名(如2_1、2_3)而非通用"变体"标识
// v1.8.1扩展版本：为数字3和数字4分别增加变体模板3_1.png和4_1.png
// v1.8.2扩展版本：为数字9增加变体模板9_1.png，支持数字9的多尺寸识别
// v1.8.3测试版本：注释掉保护机制的continue跳过逻辑，方便测试多模板识别效果
// v1.9.0重大版本：为数字5、6、7、8分别增加变体模板，现在所有数字0-9都有多尺寸模板支持
// v1.9.1扩展版本：为数字9增加第二个变体模板9_2.png，数字9现在有3个模板，与数字2并列最多
// v1.9.2智能版本：集成数字5和6闭合区域检测技术，解决5/6识别混淆问题，提高识别准确率
// v1.9.3优化版本：优化闭合区域检测阈值，使用多阈值遍历测试，提高5/6区分准确率
// v1.9.4修复版本：使用OpenCV Java API实现轮廓检测，修复AutoXjs中不存在的findContours函数问题
// v1.9.5完善版本：基于牙叔教程，使用正确的AutoXjs OpenCV API实现轮廓检测，完善数字5和6区分功能
// v1.9.6优化版本：测试成功后优化性能，只使用最优阈值80，去掉多阈值遍历，提高检测效率
// v1.9.7扩展版本：为答案区域四个区域也增加数字5和6闭合区域检测功能，全面覆盖所有数字识别场景
// v1.9.8完善版本：为答案区域添加完整的最佳组合选择算法和详细调试输出，与表达式区域保持一致
// v1.9.9调优版本：为答案区域设置独立的闭合检测阈值120，恢复简单组合逻辑，专门优化答案区域识别
// v1.9.10修复版本：恢复答案区域的双数字和三数字组合逻辑，保持完整的最佳组合选择功能
// v1.9.11测试版本：为答案区域添加多阈值自动测试功能，测试6个阈值(80,100,120,150,180,200)并自动选择最佳结果
// v1.9.12细化版本：扩展阈值测试范围，测试17个细化阈值(60-220，每10为一档)，更精确地找到最佳阈值
// v1.9.13优化版本：经过测试确定答案区域最佳阈值为140，固定使用此阈值，提高检测效率
//

// 【表达式区域】前数字和后数字：0.8 (第120行)
// 【表达式区域】运算符：0.7 (第622行)
// 【答案区域】四个答案区域：0.75 (第808行)
//


// 请求屏幕捕获权限
auto.waitFor();
if (!requestScreenCapture()) {
    console.log("❌ 屏幕捕获权限获取失败");
    exit();
}
console.log("✅ 屏幕捕获权限获取成功");





// 统一的三区域识别引擎 - 单函数多接口设计
function 三区域识别引擎(全屏截图, 区域类型) {
    console.log("🔍 三区域识别引擎: " + 区域类型);
    
    // 根据区域类型定义坐标和模板
    var 区域配置 = {
        "前数字": {
            坐标: [39, 441, 158, 101],
            模板类型: "数字",
            描述: "第一个数字区域"
        },
        "运算符": {
            坐标: [189, 444, 95, 83],
            模板类型: "运算符",
            描述: "运算符区域"
        },
        "后数字": {
            坐标: [277, 440, 109, 88],
            模板类型: "数字",
            描述: "第二个数字区域"
        }
    };
    
    var 当前配置 = 区域配置[区域类型];
    if (!当前配置) {
        console.log("❌ 未知的区域类型: " + 区域类型);
        return null;
    }
    
    // 截取指定区域
    var 区域图像 = images.clip(全屏截图, 当前配置.坐标[0], 当前配置.坐标[1], 当前配置.坐标[2], 当前配置.坐标[3]);
    if (!区域图像) {
        console.log("❌ 截取" + 当前配置.描述 + "失败");
        return null;
    }
    
    console.log("✅ 成功截取" + 当前配置.描述 + ": " + 当前配置.坐标.join(","));
    
    // 灰度处理
    var 灰度图像 = images.grayscale(区域图像);
    区域图像.recycle();
    
    if (!灰度图像) {
        console.log("❌ " + 当前配置.描述 + "灰度处理失败");
        return null;
    }
    
    console.log("✅ " + 当前配置.描述 + "灰度处理完成");
    
    // 根据模板类型选择不同的识别逻辑
    var 识别结果 = null;

    if (当前配置.模板类型 === "数字") {
        识别结果 = 识别数字区域(灰度图像, 区域类型, 当前配置);
    } else if (当前配置.模板类型 === "运算符") {
        识别结果 = 识别运算符区域(灰度图像, 区域类型, 当前配置);
    }

    // 清理资源
    灰度图像.recycle();

    return 识别结果;
}

// 数字区域识别函数（支持双数字组合和间距判断）
function 识别数字区域(灰度图像, 区域类型, 区域配置) {
    console.log("🔢 开始识别数字区域: " + 区域类型);
    
    // 加载数字模板（分离式多模板设计）
    var 数字模板 = {
        "0": {图片: images.read(files.path("../../assets/算数游戏/0.png")), 类型: "标准"},
        "0_1": {图片: images.read(files.path("../../assets/算数游戏/0_1.png")), 类型: "变体", 实际数字: "0"},
        "1": {图片: images.read(files.path("../../assets/算数游戏/1.png")), 类型: "标准"},
        "1_1": {图片: images.read(files.path("../../assets/算数游戏/1_1.png")), 类型: "变体", 实际数字: "1"},
        "2": {图片: images.read(files.path("../../assets/算数游戏/2.png")), 类型: "标准"},
        "2_1": {图片: images.read(files.path("../../assets/算数游戏/2_1.png")), 类型: "变体", 实际数字: "2"},
        "2_3": {图片: images.read(files.path("../../assets/算数游戏/2_3.png")), 类型: "变体", 实际数字: "2"},
        "3": {图片: images.read(files.path("../../assets/算数游戏/3.png")), 类型: "标准"},
        "3_1": {图片: images.read(files.path("../../assets/算数游戏/3_1.png")), 类型: "变体", 实际数字: "3"},
        "4": {图片: images.read(files.path("../../assets/算数游戏/4.png")), 类型: "标准"},
        "4_1": {图片: images.read(files.path("../../assets/算数游戏/4_1.png")), 类型: "变体", 实际数字: "4"},
        "5": {图片: images.read(files.path("../../assets/算数游戏/5.png")), 类型: "标准"},
        "5_1": {图片: images.read(files.path("../../assets/算数游戏/5_1.png")), 类型: "变体", 实际数字: "5"},
        "6": {图片: images.read(files.path("../../assets/算数游戏/6.png")), 类型: "标准"},
        "6_1": {图片: images.read(files.path("../../assets/算数游戏/6_1.png")), 类型: "变体", 实际数字: "6"},
        "7": {图片: images.read(files.path("../../assets/算数游戏/7.png")), 类型: "标准"},
        "7_1": {图片: images.read(files.path("../../assets/算数游戏/7_1.png")), 类型: "变体", 实际数字: "7"},
        "8": {图片: images.read(files.path("../../assets/算数游戏/8.png")), 类型: "标准"},
        "8_1": {图片: images.read(files.path("../../assets/算数游戏/8_1.png")), 类型: "变体", 实际数字: "8"},
        "9": {图片: images.read(files.path("../../assets/算数游戏/9.png")), 类型: "标准"},
        "9_1": {图片: images.read(files.path("../../assets/算数游戏/9_1.png")), 类型: "变体", 实际数字: "9"},
        "9_2": {图片: images.read(files.path("../../assets/算数游戏/9_2.png")), 类型: "变体", 实际数字: "9"}
    };

    // 动态获取模板尺寸（分离式设计）
    for (var 模板键 in 数字模板) {
        var 模板 = 数字模板[模板键];
        if (模板.图片) {
            模板.尺寸 = {
                宽: 模板.图片.getWidth(),
                高: 模板.图片.getHeight()
            };
        }
    }
    
    // 检查模板加载（分离式设计）
    var 加载成功 = 0;
    var 总模板数量 = 0;
    var 加载详情 = [];
    for (var 模板键 in 数字模板) {
        var 模板 = 数字模板[模板键];
        总模板数量++;
        if (模板.图片) {
            加载成功++;
            var 显示名 = 模板.实际数字 ? 模板.实际数字 + "(" + 模板.类型 + ")" : 模板键 + "(" + 模板.类型 + ")";
            加载详情.push(显示名);
        } else {
            var 显示名 = 模板.实际数字 ? 模板.实际数字 + "(" + 模板.类型 + ")" : 模板键 + "(" + 模板.类型 + ")";
            console.log("❌ 模板加载失败: " + 显示名);
        }
    }
    console.log("✅ 数字模板加载: " + 加载成功 + "/" + 总模板数量 + "个");
    console.log("  📋 加载成功的模板: " + 加载详情.join(", "));
    
    // 识别所有数字（分离式多模板匹配）
    var 找到的数字 = [];
    var 数字相似度阈值 = 0.90; // 【表达式区域】前数字和后数字的相似度阈值/表达式的阈值

    for (var 模板键 in 数字模板) {
        var 模板信息 = 数字模板[模板键];

        if (!模板信息.图片) {
            console.log("  ⚠️ 跳过空模板: " + 模板键 + "(" + 模板信息.类型 + ")");
            continue;
        }

        // 确保模板也是灰度图像
        var 灰度模板 = images.grayscale(模板信息.图片);
        if (!灰度模板) {
            console.log("  ❌ 灰度转换失败: " + 模板键 + "(" + 模板信息.类型 + ")");
            continue;
        }

        var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
            threshold: 数字相似度阈值,
            max: 5,
            level: -1
        });

        // 清理模板资源
        灰度模板.recycle();

        if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
            匹配结果.matches.forEach(function(match) {
                // 面积验证已移除，直接使用相似度结果
                if (true) {
                    // 计算中心点坐标（需要加上区域偏移量）
                    var 中心x = match.point.x + Math.round(模板信息.尺寸.宽 / 2);
                    var 中心y = match.point.y + Math.round(模板信息.尺寸.高 / 2);

                    // 确定实际数字（处理变体模板）
                    var 实际数字 = 模板信息.实际数字 || 模板键;
                    var 最终相似度 = match.similarity;
                    var 验证详情 = "";

                    // 对数字5和6进行闭合区域检测验证
                    if (实际数字 === "5" || 实际数字 === "6") {
                        try {
                            // 提取匹配区域的图像
                            var 匹配区域图像 = images.clip(灰度图像, match.point.x, match.point.y, 模板信息.尺寸.宽, 模板信息.尺寸.高);
                            if (匹配区域图像) {
                                var 验证结果 = 数字5和6检测模块.数字5和6闭合区域检测(匹配区域图像, 实际数字, match.similarity);
                                最终相似度 = 验证结果.置信度;
                                验证详情 = 验证结果.检测详情;
                                匹配区域图像.recycle();

                                // 如果验证不通过且置信度被大幅降低，记录详情
                                if (!验证结果.是否通过) {
                                    console.log("    ⚠️ 数字" + 实际数字 + "验证失败: " + 验证详情);
                                }
                            }
                        } catch (e) {
                            console.log("    ❌ 数字" + 实际数字 + "验证异常: " + e.toString());
                        }
                    }

                    找到的数字.push({
                        数字: 实际数字,
                        x坐标: match.point.x,
                        y坐标: match.point.y,
                        中心点: [中心x, 中心y], // 这里是相对于区域的中心点
                        相似度: 最终相似度, // 使用验证后的相似度
                        原始相似度: match.similarity, // 保留原始相似度
                        模板尺寸: 模板信息.尺寸,
                        模板类型: 模板信息.类型,
                        模板键: 模板键,
                        位置: match.point,
                        验证详情: 验证详情
                    });
                }
            });
        }
    }
    
    console.log("🔍 " + 区域类型 + "找到数字: 【" + 找到的数字.length + "】个");

    // 输出所有找到数字的相似度信息（一行显示，包含验证结果）
    if (找到的数字.length > 0) {
        var 相似度信息 = [];
        找到的数字.forEach(function(数字) {
            var 显示名称 = 数字.模板类型 === "变体" ? 数字.模板键 : 数字.数字;
            var 相似度显示 = 数字.相似度.toFixed(3);

            // 如果有验证详情且相似度被调整，显示验证信息
            if (数字.验证详情 && 数字.原始相似度 && Math.abs(数字.相似度 - 数字.原始相似度) > 0.01) {
                相似度显示 += "(" + 数字.原始相似度.toFixed(3) + "→验证)";
            }

            相似度信息.push("【" + 显示名称 + "】" + 相似度显示);
        });
        console.log("  📊 " + 区域类型 + "相似度: " + 相似度信息.join(" | "));

        // 输出验证详情
        找到的数字.forEach(function(数字) {
            if (数字.验证详情 && 数字.验证详情 !== "") {
                console.log("    🔍 " + 数字.模板键 + "验证: " + 数字.验证详情);
            }
        });
    }

    if (找到的数字.length === 0) {
        console.log("❌ " + 区域类型 + "未找到任何数字");
        return null;
    }
    
    // 按x坐标排序
    找到的数字.sort(function(a, b) { return a.x坐标 - b.x坐标; });
    
    // 应用双数字组合逻辑（移植自原文件第610-646行）
    var 最佳组合 = null;
    var 最高总相似度 = 0;
    
    // 尝试单个数字
    for (var s = 0; s < 找到的数字.length; s++) {
        var 单个数字 = 找到的数字[s];
        // 计算全屏坐标的中心点（保持与最终结果一致）
        var 全屏中心x = 区域配置.坐标[0] + 单个数字.中心点[0];
        var 全屏中心y = 区域配置.坐标[1] + 单个数字.中心点[1];
        var 显示名称 = 单个数字.模板类型 === "变体" ? 单个数字.模板键 : 单个数字.数字;
        console.log("  单数字分析: 【" + 显示名称 + "】(中心:" + 全屏中心x + "," + 全屏中心y + " 相似度:" + 单个数字.相似度.toFixed(3) + ")");
        if (单个数字.相似度 > 最高总相似度) {
            最高总相似度 = 单个数字.相似度;
            最佳组合 = [单个数字];
            console.log("    -> 更新最佳单数字");
        }
    }
    
    // 尝试两个数字的组合（基于真实间距规律）
    for (var i = 0; i < 找到的数字.length - 1; i++) {
        for (var j = i + 1; j < 找到的数字.length; j++) {
            var 第一个 = 找到的数字[i];
            var 第二个 = 找到的数字[j];
            
            // 正确计算间距：第一个数字右边缘到第二个数字左边缘的距离
            var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
            var 真实间距 = 第二个.x坐标 - 第一个右边缘;

            // 详细的间距调试信息
            var 第一个显示名称 = 第一个.模板类型 === "变体" ? 第一个.模板键 : 第一个.数字;
            var 第二个显示名称 = 第二个.模板类型 === "变体" ? 第二个.模板键 : 第二个.数字;
            console.log("    🔍 间距分析: 【" + 第一个显示名称 + "】x:" + 第一个.x坐标 + " 宽:" + 第一个.模板尺寸.宽 + " 右边缘:" + 第一个右边缘 + " + 【" + 第二个显示名称 + "】x:" + 第二个.x坐标 + " = 间距:" + 真实间距 + "px");

            // 根据数字类型判断合理间距（移植自原文件第623-629行）
            var 间距合理 = false;
            var 间距规则 = "";

            if (第一个.数字 === "1" || 第二个.数字 === "1") {
                // 包含数字1：间距应该 < 37像素
                间距合理 = (真实间距 >= -5 && 真实间距 < 37);
                间距规则 = "含1规则(-5~37px)";
            } else {
                // 其他数字组合：间距应该 -7~10像素
                间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
                间距规则 = "普通规则(-7~10px)";
            }

            console.log("      📏 " + 间距规则 + " 结果:" + (间距合理 ? "✅合理" : "❌不合理"));

            if (间距合理) {
                var 总相似度 = 第一个.相似度 + 第二个.相似度;
                var 间距类型 = (第一个.数字 === "1" || 第二个.数字 === "1") ? "含1" : "普通";

                // 计算全屏坐标的中心点（保持与最终结果一致）
                var 第一个全屏中心x = 区域配置.坐标[0] + 第一个.中心点[0];
                var 第一个全屏中心y = 区域配置.坐标[1] + 第一个.中心点[1];
                var 第二个全屏中心x = 区域配置.坐标[0] + 第二个.中心点[0];
                var 第二个全屏中心y = 区域配置.坐标[1] + 第二个.中心点[1];
                var 间距类型 = (第一个.数字 === "1" || 第二个.数字 === "1") ? "含1" : "普通";

                console.log("  ✅ 组合分析: 【" + 第一个显示名称 + "】(中心:" + 第一个全屏中心x + "," + 第一个全屏中心y + " 相似度:" + 第一个.相似度.toFixed(3) + ") + 【" + 第二个显示名称 + "】(中心:" + 第二个全屏中心x + "," + 第二个全屏中心y + " 相似度:" + 第二个.相似度.toFixed(3) + ") 真实间距:" + 真实间距 + "px(" + 间距类型 + ") 总相似度:" + 总相似度.toFixed(3));

                if (总相似度 > 最高总相似度) {
                    最高总相似度 = 总相似度;
                    最佳组合 = [第一个, 第二个];
                    console.log("    -> 更新最佳组合");
                }
            }
        }
    }



    // 构建最终数字
    var 完整数字 = "";
    var 中心点信息 = [];
    var 相似度详情 = [];
    if (最佳组合) {
        最佳组合.forEach(function(数字) {
            完整数字 += 数字.数字;
            // 计算全屏坐标的中心点（加上区域偏移量）
            var 全屏中心x = 区域配置.坐标[0] + 数字.中心点[0];
            var 全屏中心y = 区域配置.坐标[1] + 数字.中心点[1];
            中心点信息.push("(" + 全屏中心x + "," + 全屏中心y + ")");
            var 显示名称 = 数字.模板类型 === "变体" ? 数字.模板键 : 数字.数字;
            相似度详情.push("【" + 显示名称 + "】" + 数字.相似度.toFixed(3));
        });
        var 总相似度 = 最佳组合.reduce(function(sum, 数字) { return sum + 数字.相似度; }, 0);
        console.log("✅ " + 区域类型 + "识别结果: 【" + 完整数字 + "】 中心点:" + 中心点信息.join("+") + " 相似度:" + 相似度详情.join("+") + " 总:" + 总相似度.toFixed(3));
    }
    
    return 完整数字 || null;
}

// 运算符区域识别函数
function 识别运算符区域(灰度图像, 区域类型, 区域配置) {
    console.log("🔣 开始识别运算符区域: " + 区域类型);
    
    // 加载运算符模板
    var 运算符模板 = {
        "+": {图片: images.read(files.path("../../assets/算数游戏/+.png"))},
        "-": {图片: images.read(files.path("../../assets/算数游戏/减号.png"))},
        "×": {图片: images.read(files.path("../../assets/算数游戏/x.png"))},
        "÷": {图片: images.read(files.path("../../assets/算数游戏/除.png"))}
    };

    // 动态获取运算符模板尺寸
    for (var 运算符 in 运算符模板) {
        if (运算符模板[运算符].图片) {
            运算符模板[运算符].尺寸 = {
                宽: 运算符模板[运算符].图片.getWidth(),
                高: 运算符模板[运算符].图片.getHeight()
            };
        }
    }
    
    var 运算符相似度阈值 = 0.7; // 【表达式区域】运算符的相似度阈值
    var 最佳运算符 = null;
    var 最高相似度 = 0;
    var 所有运算符结果 = [];

    console.log("  🔍 运算符检测详情:");

    for (var 运算符 in 运算符模板) {
        var 模板信息 = 运算符模板[运算符];
        if (!模板信息.图片) {
            console.log("    【" + 运算符 + "】 模板加载失败");
            continue;
        }

        // 确保模板也是灰度图像
        var 灰度模板 = images.grayscale(模板信息.图片);
        if (!灰度模板) {
            console.log("    【" + 运算符 + "】 模板灰度转换失败");
            continue;
        }

        var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
            threshold: 运算符相似度阈值, // 使用正常的运算符相似度阈值
            max: 1,
            level: -1
        });

        // 清理模板资源
        灰度模板.recycle();

        if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
            var match = 匹配结果.matches[0];
            var 实际相似度 = match.similarity;

            // 面积验证已移除，直接使用相似度结果
            // 记录结果
            所有运算符结果.push({
                运算符: 运算符,
                相似度: 实际相似度,
                位置: match.point,
                达到阈值: true, // 已通过matchTemplate阈值
                面积验证: true // 面积验证已移除，默认通过
            });

            if (true) {
                console.log("    【" + 运算符 + "】 ✅匹配成功 | 相似度:" + 实际相似度.toFixed(3) + " | 位置:(" + match.point.x + "," + match.point.y + ") | 阈值:" + 运算符相似度阈值);

                if (实际相似度 > 最高相似度) {
                    最高相似度 = 实际相似度;
                    最佳运算符 = 运算符;
                }
            }
        } else {
            console.log("    【" + 运算符 + "】 ❌完全无匹配 | 相似度:0.000 | 位置:无 | 阈值:" + 运算符相似度阈值);
        }
    }
    
    console.log("  📊 运算符检测总结:");

    if (最佳运算符) {
        console.log("✅ " + 区域类型 + "识别结果: 【" + 最佳运算符 + "】【" + 最高相似度.toFixed(3) + "】");

    } else {
        console.log("❌ " + 区域类型 + "识别失败: 未找到符合要求的运算符");

        // 显示所有检测结果的汇总
        if (所有运算符结果.length > 0) {
            var 运算符详情 = [];
            所有运算符结果.forEach(function(结果) {
                var 状态 = 结果.面积验证 ? "✅" : "❌";
                运算符详情.push("【" + 结果.运算符 + "】" + 状态 + "【" + 结果.相似度.toFixed(3) + "】");
            });
            console.log("🔍 检测到的运算符: " + 运算符详情.join(" | "));
        } else {
            console.log("🚫 完全无匹配: 所有运算符相似度均低于阈值" + 运算符相似度阈值);
        }
    }
    
    return 最佳运算符;
}

// 答案位置识别函数 - 完整移植自原文件，保持函数名不变
function 答案位置(外部截图) {
    console.log("🔍 答案区查找:");

    try {
        // 使用外部传入的截图
        var 屏幕图 = 外部截图;
        if (!屏幕图) {
            console.log("❌ 外部截图无效");
            return {};
        }

        // 定义四个答案区域 - 完全复制原文件
        var 答案区域列表 = [
            {名称: "黄区", 坐标: [103, 648, 136, 68], 颜色: "🟨"},
            {名称: "蓝区", 坐标: [303, 650, 136, 66], 颜色: "🟦"},
            {名称: "绿区", 坐标: [103, 788, 131, 70], 颜色: "🟩"},
            {名称: "红区", 坐标: [303, 785, 131, 71], 颜色: "🟥"}
        ];

        // 加载数字模板 - 答案区域专用
        var 数字模板 = {
            "0": {图片: images.read(files.path("../../assets/算数游戏/答案数字/0.png"))},
            "1": {图片: images.read(files.path("../../assets/算数游戏/答案数字/1.png"))},
            "2": {图片: images.read(files.path("../../assets/算数游戏/答案数字/2.png"))},
            "3": {图片: images.read(files.path("../../assets/算数游戏/答案数字/3.png"))},
            "4": {图片: images.read(files.path("../../assets/算数游戏/答案数字/4.png"))},
            "5": {图片: images.read(files.path("../../assets/算数游戏/答案数字/5.png"))},
            "6": {图片: images.read(files.path("../../assets/算数游戏/答案数字/6.png"))},
            "7": {图片: images.read(files.path("../../assets/算数游戏/答案数字/7.png"))},
            "8": {图片: images.read(files.path("../../assets/算数游戏/答案数字/8.png"))},
            "9": {图片: images.read(files.path("../../assets/算数游戏/答案数字/9.png"))}
        };

        // 动态获取答案数字模板尺寸
        for (var 数字 in 数字模板) {
            if (数字模板[数字].图片) {
                数字模板[数字].尺寸 = {
                    宽: 数字模板[数字].图片.getWidth(),
                    高: 数字模板[数字].图片.getHeight()
                };
            }
        }

        // 检查模板加载情况
        var 成功加载数 = 0;
        for (var 数字 in 数字模板) {
            if (数字模板[数字].图片) {
                成功加载数++;
            }
        }
        console.log("✅ 答案数字模板加载: " + 成功加载数 + "/10个");

        // 识别结果
        var 识别结果 = {};

        // 遍历每个答案区域 - 移植原文件逻辑
        console.log("------------------------答案区域识别功能--------------------------");
        console.log("🎯 答案识别:");
        答案区域列表.forEach(function(区域, 索引) {
            if (索引 > 0) {
                console.log("  ────────────────────────────────────────");
            }

            console.log("  🔍 开始识别【" + 区域.名称 + "】区域...");

            // 截取区域
            var 区域图像 = images.clip(屏幕图, 区域.坐标[0], 区域.坐标[1], 区域.坐标[2], 区域.坐标[3]);
            if (!区域图像) {
                console.log("    ❌ 截取" + 区域.名称 + "失败");
                return;
            }

            // 灰度处理
            var 灰度图像 = images.grayscale(区域图像);
            区域图像.recycle();

            if (!灰度图像) {
                console.log("    ❌ " + 区域.名称 + "灰度处理失败");
                return;
            }

            // 识别数字（应用双数字组合逻辑）
            var 找到的数字 = [];
            var 答案相似度阈值 = 0.8; // 【答案区域阈值】四个答案区域的数字相似度阈值

            for (var 数字 in 数字模板) {
                var 模板信息 = 数字模板[数字];
                if (!模板信息.图片) continue;

                // 确保模板也是灰度图像
                var 灰度模板 = images.grayscale(模板信息.图片);
                if (!灰度模板) continue;

                var 匹配结果 = images.matchTemplate(灰度图像, 灰度模板, {
                    threshold: 答案相似度阈值, // 答案区域保持原阈值
                    max: 5,
                    level: -1
                });

                // 清理模板资源
                灰度模板.recycle();

                if (匹配结果 && 匹配结果.matches && 匹配结果.matches.length > 0) {
                    匹配结果.matches.forEach(function(match) {
                        var 最终相似度 = match.similarity;
                        var 验证详情 = "";

                        // 对数字5和6进行闭合区域检测验证（答案区域）
                        if (数字 === "5" || 数字 === "6") {
                            try {
                                // 提取匹配区域的图像
                                var 匹配区域图像 = images.clip(灰度图像, match.point.x, match.point.y, 模板信息.尺寸.宽, 模板信息.尺寸.高);
                                if (匹配区域图像) {
                                    // 答案区域使用固定阈值40（答案区闭合阈值）
                                    var 答案区阈值 = 40; // 答案区域闭合阈值，经过测试确定
                                    var 验证结果 = 数字5和6检测模块.数字5和6闭合区域检测(匹配区域图像, 数字, match.similarity, 答案区阈值);
                                    最终相似度 = 验证结果.置信度;
                                    验证详情 = 验证结果.检测详情;
                                    匹配区域图像.recycle();

                                    // 如果验证不通过且置信度被大幅降低，记录详情
                                    if (!验证结果.是否通过) {
                                        console.log("      ⚠️ 答案区数字" + 数字 + "验证失败: " + 验证详情);
                                    }
                                }
                            } catch (e) {
                                console.log("      ❌ 答案区数字" + 数字 + "验证异常: " + e.toString());
                            }
                        }

                        // 计算中心点坐标（需要加上区域偏移量）
                        var 中心x = match.point.x + Math.round(模板信息.尺寸.宽 / 2);
                        var 中心y = match.point.y + Math.round(模板信息.尺寸.高 / 2);

                        找到的数字.push({
                            数字: 数字,
                            x坐标: match.point.x,
                            y坐标: match.point.y,
                            中心点: [中心x, 中心y], // 这里是相对于区域的中心点
                            相似度: 最终相似度, // 使用验证后的相似度
                            原始相似度: match.similarity, // 保留原始相似度
                            模板尺寸: 模板信息.尺寸,
                            验证详情: 验证详情
                        });
                    });
                }
            }

            console.log("    📊 找到数字: 【" + 找到的数字.length + "】个");

            // 输出所有找到数字的相似度信息（包含验证结果）
            if (找到的数字.length > 0) {
                var 相似度信息 = [];
                找到的数字.forEach(function(数字) {
                    var 相似度显示 = 数字.相似度.toFixed(3);

                    // 如果有验证详情且相似度被调整，显示验证信息
                    if (数字.验证详情 && 数字.原始相似度 && Math.abs(数字.相似度 - 数字.原始相似度) > 0.01) {
                        相似度显示 += "(" + 数字.原始相似度.toFixed(3) + "→验证)";
                    }

                    相似度信息.push("【" + 数字.数字 + "】" + 相似度显示);
                });
                console.log("    📊 " + 区域.名称 + "相似度: " + 相似度信息.join(" | "));

                // 输出验证详情
                找到的数字.forEach(function(数字) {
                    if (数字.验证详情 && 数字.验证详情 !== "") {
                        console.log("      🔍 " + 数字.数字 + "验证: " + 数字.验证详情);
                    }
                });
            }


            if (找到的数字.length > 0) {
                // 按x坐标排序
                找到的数字.sort(function(a, b) { return a.x坐标 - b.x坐标; });

                // 应用双数字组合逻辑（简化版本）
                var 最佳组合 = null;
                var 最高总相似度 = 0;

                // 尝试单个数字
                for (var s = 0; s < 找到的数字.length; s++) {
                    var 单个数字 = 找到的数字[s];
                    if (单个数字.相似度 > 最高总相似度) {
                        最高总相似度 = 单个数字.相似度;
                        最佳组合 = [单个数字];
                    }
                }

                // 尝试两个数字的组合
                for (var i = 0; i < 找到的数字.length - 1; i++) {
                    for (var j = i + 1; j < 找到的数字.length; j++) {
                        var 第一个 = 找到的数字[i];
                        var 第二个 = 找到的数字[j];

                        // 计算间距
                        var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
                        var 真实间距 = 第二个.x坐标 - 第一个右边缘;

                        // 间距判断
                        var 间距合理 = false;
                        if (第一个.数字 === "1" || 第二个.数字 === "1") {
                            间距合理 = (真实间距 >= -5 && 真实间距 < 37);
                        } else {
                            间距合理 = (真实间距 >= -7 && 真实间距 <= 10);
                        }

                        if (间距合理) {
                            var 总相似度 = 第一个.相似度 + 第二个.相似度;
                            if (总相似度 > 最高总相似度) {
                                最高总相似度 = 总相似度;
                                最佳组合 = [第一个, 第二个];
                            }
                        }
                    }
                }

                // 尝试三个数字的组合
                for (var i = 0; i < 找到的数字.length - 2; i++) {
                    for (var j = i + 1; j < 找到的数字.length - 1; j++) {
                        for (var k = j + 1; k < 找到的数字.length; k++) {
                            var 第一个 = 找到的数字[i];
                            var 第二个 = 找到的数字[j];
                            var 第三个 = 找到的数字[k];

                            // 计算第一个到第二个的间距
                            var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
                            var 间距1 = 第二个.x坐标 - 第一个右边缘;

                            // 计算第二个到第三个的间距
                            var 第二个右边缘 = 第二个.x坐标 + 第二个.模板尺寸.宽;
                            var 间距2 = 第三个.x坐标 - 第二个右边缘;

                            // 判断两个间距是否都合理
                            var 间距1合理 = false;
                            var 间距2合理 = false;

                            if (第一个.数字 === "1" || 第二个.数字 === "1") {
                                间距1合理 = (间距1 >= -5 && 间距1 < 37);
                            } else {
                                间距1合理 = (间距1 >= -7 && 间距1 <= 10);
                            }

                            if (第二个.数字 === "1" || 第三个.数字 === "1") {
                                间距2合理 = (间距2 >= -5 && 间距2 < 37);
                            } else {
                                间距2合理 = (间距2 >= -7 && 间距2 <= 10);
                            }

                            if (间距1合理 && 间距2合理) {
                                var 总相似度 = 第一个.相似度 + 第二个.相似度 + 第三个.相似度;
                                if (总相似度 > 最高总相似度) {
                                    最高总相似度 = 总相似度;
                                    最佳组合 = [第一个, 第二个, 第三个];
                                }
                            }
                        }
                    }
                }

                // 构建最终数字
                var 完整数字 = "";
                if (最佳组合) {
                    最佳组合.forEach(function(数字) {
                        完整数字 += 数字.数字;
                    });

                    // 计算中心点坐标
                    var 最左x = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标; }));
                    var 最右x = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标 + 数字.模板尺寸.宽; }));
                    var 最上y = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标; }));
                    var 最下y = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标 + 数字.模板尺寸.高; }));

                    var 中心x = 区域.坐标[0] + (最左x + 最右x) / 2;
                    var 中心y = 区域.坐标[1] + (最上y + 最下y) / 2;

                    识别结果[区域.名称] = {
                        数字: 完整数字,
                        坐标: [区域.坐标[0] + 最左x, 区域.坐标[1] + 最上y, 最右x - 最左x, 最下y - 最上y],
                        中心点: [Math.round(中心x), Math.round(中心y)]
                    };

                    console.log("    ✅ " + 区域.名称 + "识别成功: 【" + 完整数字 + "】 坐标范围【" + (区域.坐标[0] + 最左x) + "," + (区域.坐标[1] + 最上y) + "," + (最右x - 最左x) + "," + (最下y - 最上y) + "】");
                }
            } else {
                console.log("    ❌ " + 区域.名称 + "未找到数字");
            }

            // 清理资源
            灰度图像.recycle();
        });

        return 识别结果;

    } catch (e) {
        console.log("❌ 答案识别失败: " + e);
        return {};
    }
}

// 主要计算函数 - 保持函数名不变，采用三区域识别
function 计算_公式() {
    var 循环次数 = 100; // 定义循环次数变量，方便统一更改

    for (var 循环 = 1; 循环 <= 循环次数; 循环++) {
        try {
            // 一次性截取全屏，避免重复截屏
            var 全屏截图 = captureScreen();
            if (!全屏截图) {
                console.log("❌ 全屏截图失败");
                continue; // 截图失败，跳过本次循环
            } else {
                console.log("------------------------截图功能--------------------------");
                console.log("✅ 全屏截图成功，开始三区域识别");
            }

            // 1. 三区域识别表达式
            console.log("------------------------三区域识别功能--------------------------");
            var 前数字结果 = 三区域识别引擎(全屏截图, "前数字");
            console.log("------------------------运算符区域--------------------------");
            var 运算符结果 = 三区域识别引擎(全屏截图, "运算符");
            console.log("------------------------后数字区域--------------------------");
            var 后数字结果 = 三区域识别引擎(全屏截图, "后数字");

            // 2. 识别结果汇总
            console.log("------------------------识别结果汇总--------------------------");
            console.log("🔍 当前识别结果:");
            console.log("  前数字结果: " + JSON.stringify(前数字结果));
            console.log("  运算符结果: " + JSON.stringify(运算符结果));
            console.log("  后数字结果: " + JSON.stringify(后数字结果));

            // 拼接表达式
            var 表达式 = null;
            if (前数字结果 && 运算符结果 && 后数字结果) {
                表达式 = 前数字结果 + 运算符结果 + 后数字结果 + "=";
                console.log("✅ 三区域识别成功: 【" + 表达式 + "】");
            } else {
                console.log("❌ 三区域识别失败:");
                console.log("  前数字: " + (前数字结果 || "未识别"));
                console.log("  运算符: " + (运算符结果 || "未识别"));
                console.log("  后数字: " + (后数字结果 || "未识别"));

                // 标注功能已移除
                console.log("⏭️ 识别失败，跳过本次循环");

                // 重新检查表达式是否完整
                if (前数字结果 && 运算符结果 && 后数字结果) {
                    表达式 = 前数字结果 + 运算符结果 + 后数字结果 + "=";
                    console.log("✅ 表达式识别成功: 【" + 表达式 + "】");
                } else {
                    console.log("❌ 表达式仍不完整，跳过本次循环");
                    // 全屏截图.recycle();
                    // continue; // 跳过答案识别和点击，直接进入下一次循环 - 测试期间注释掉
                }
            }

            var 公式结果 = 表达式 || "无";
            console.log("✅ 公式识别完成: 【" + 公式结果 + "】");

            // 2. 识别答案区域（调用移植的答案位置函数）
            console.log("------------------------答案识别功能--------------------------");
            var 答案结果 = 答案位置(全屏截图);

            // 3. 答案识别完成
            console.log("------------------------答案识别完成--------------------------");

            // 3. 统一清理全屏截图资源
            全屏截图.recycle();
            console.log("✅ 清理全屏截图资源");

            // 4. 计算表达式并找到正确答案
            var 计算结果 = null;
            var 正确答案区域 = null;

            // 解析并计算表达式
            if (公式结果 && 公式结果 !== "无" && 公式结果 !== "识别失败") {
                try {
                    // 移除等号，只保留表达式部分
                    var 计算表达式 = 公式结果.replace(/=/g, "");

                    // 替换运算符为JavaScript可识别的符号
                    计算表达式 = 计算表达式.replace(/×/g, "*");
                    计算表达式 = 计算表达式.replace(/÷/g, "/");

                    // 安全计算表达式
                    if (/^[\d+\-*/\s()]+$/.test(计算表达式)) {
                        计算结果 = eval(计算表达式);

                        // 在答案区域中查找正确答案
                        for (var 区域名 in 答案结果) {
                            var 区域数据 = 答案结果[区域名];
                            if (区域数据.数字 == 计算结果) {
                                正确答案区域 = {
                                    区域名: 区域名,
                                    数字: 区域数据.数字,
                                    坐标: 区域数据.坐标,
                                    中心点: 区域数据.中心点
                                };
                                break;
                            }
                        }
                    } else {
                        console.log("❌ 表达式包含非法字符: " + 计算表达式);
                    }
                } catch (e) {
                    console.log("❌ 计算表达式失败: " + e);
                }
            }

            console.log("------------------------计算匹配功能--------------------------");
            console.log("🧮 计算匹配:");
            console.log("  表达式: " + 公式结果);
            console.log("  计算结果: " + (计算结果 || "失败"));
            console.log("  正确答案: " + (正确答案区域 ? 正确答案区域.区域名 + "【" + 正确答案区域.数字 + "】 坐标范围【" + 正确答案区域.坐标[0] + "," + 正确答案区域.坐标[1] + "," + 正确答案区域.坐标[2] + "," + 正确答案区域.坐标[3] + "】" : "未找到"));

            // 5. 返回完整结果（包含计算结果和正确答案位置）
            var 完整结果 = {
                公式: 公式结果,
                答案: 答案结果,
                计算结果: 计算结果,
                正确答案: 正确答案区域
            };

            console.log("------------------------最终结果输出--------------------------");
            console.log("🎯 最终结果:");
            console.log("  " + 完整结果.公式 + " → 【" + (完整结果.计算结果 || "失败") + "】 → " + (完整结果.正确答案 ? 完整结果.正确答案.区域名 + "【" + 完整结果.正确答案.数字 + "】 坐标范围【" + 完整结果.正确答案.坐标[0] + "," + 完整结果.正确答案.坐标[1] + "," + 完整结果.正确答案.坐标[2] + "," + 完整结果.正确答案.坐标[3] + "】" : "未找到"));

            // 6. 点击正确答案
            if (完整结果.正确答案) {
                console.log("------------------------点击功能--------------------------");

                // 在坐标范围内随机生成点击位置
                var 答案坐标 = 完整结果.正确答案.坐标;
                var 随机x = 答案坐标[0] + Math.floor(Math.random() * 答案坐标[2]);
                var 随机y = 答案坐标[1] + Math.floor(Math.random() * 答案坐标[3]);

                console.log("🖱️ 准备点击正确答案: " + 完整结果.正确答案.区域名 + "【" + 完整结果.正确答案.数字 + "】 坐标范围【" + 答案坐标[0] + "," + 答案坐标[1] + "," + 答案坐标[2] + "," + 答案坐标[3] + "】 随机点击位置【" + 随机x + "," + 随机y + "】");

                // 随机延时150-200毫秒
                var 随机延时 = Math.floor(Math.random() * (200 - 100 + 1)) + 150;
                sleep(随机延时);

                click(随机x, 随机y);
                console.log("✅ 已点击正确答案，延时: " + 随机延时 + "毫秒");
            } else {
                console.log("❌ 未找到正确答案");


                console.log("⏭️ 跳过点击，继续下一次循环");
            }

            // 循环间隔
            if (循环 < 循环次数) {
                var 循环延时 = Math.floor(Math.random() * (300 - 100 + 1)) + 200;
                sleep(循环延时); // 每次循环间隔250-300毫秒
                console.log("⏱️ 循环间隔: " + 循环延时 + "毫秒");
            }

        } catch (e) {
            console.log("❌ 第" + 循环 + "次循环执行失败: " + e);
        }

        // 在循环结束时显示循环次数
        console.log("------------------------第" + 循环 + "次循环完成--------------------------");
    }

    console.log("-------------V：1.9.13-----------执行完成--------------------------");
}



// 自动执行主函数
计算_公式();
