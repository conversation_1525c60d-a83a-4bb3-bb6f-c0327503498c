Speedy Vision 图像处理功能
根据 Speedy Vision 的 README，该库提供了多种图像处理和检测功能。以下是主要的检测功能：
主要检测功能
1. 特征检测和描述
关键点检测：检测图像中的显著点（如角点、边缘等）
特征描述：生成关键点周围区域的描述符
支持的特征检测器：
ORB（Oriented FAST and Rotated BRIEF）
FAST（Features from Accelerated Segment Test）
Harris 角点检测
DoG（Difference of Gaussians）
2. 特征匹配
暴力匹配：比较所有特征点之间的距离
最近邻匹配：找到最相似的特征点
比率测试：过滤不可靠的匹配
3. 图像处理操作
滤波：包括高斯滤波、中值滤波等
边缘检测：提取图像中的边缘
二值化：将图像转换为黑白二值图像
形态学操作：如膨胀、腐蚀等
图像金字塔：多分辨率图像处理
计算机视觉任务
对象检测：识别图像中的物体
图像配准：对齐多个图像
姿态估计：确定物体在三维空间中的位置和方向
跟踪：在视频序列中跟踪目标对象
5. 高级特性
GPU 加速：使用 WebGL 进行加速计算
亚像素精度：提供比像素更精确的定位
多尺度处理：处理不同尺寸的特征
使用价值
Speedy Vision 的这些功能对于识别难度较高的图标（如 X 号、快进号等）非常有用，因为它提供了：
多种特征检测方法适应不同类型的图标
图像预处理能力，可以增强图标的可识别性
多尺度处理，可以处理不同大小的图标
GPU 加速，提高处理速度
这些功能使其成为 AutoX.js 项目中实现复杂图像识别任务的有力工具