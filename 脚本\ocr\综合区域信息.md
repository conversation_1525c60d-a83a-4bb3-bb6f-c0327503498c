15:25:14.744/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:25:14.988/D: ✅ 表达式识别屏幕捕获权限获取成功
15:25:14.991/D: 🧪 测试数学表达式识别...
15:25:14.993/D: 
🧮 ===== 开始识别数学表达式 =====
15:25:15.312/D: ✅ 表达式OCR初始化完成
15:25:15.314/D: 📐 表达式区域坐标: (51,436,426,113)
15:25:15.316/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:25:15.316/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:25:15.319/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:25:15.651/D:    ✅ 识别到: [1@+ 3 =]
15:25:15.658/D: 🔧 方法6：恢复成功配置（低阈值优化）
15:25:15.847/D:    ✅ 识别到: [13+ 3 =]
15:25:15.850/D: 🔧 方法8：白色字符三步处理法
15:25:16.039/D:    阈值80 | 二值化:80 | 中值滤波:3x3 | 白色提取:#afafaf到#FFFFFF | 识别:[1@+ 3 =]
15:25:16.233/D:    阈值82 | 二值化:82 | 中值滤波:3x3 | 白色提取:#adadad到#FFFFFF | 识别:[1@+ 3 =]
15:25:16.425/D:    阈值84 | 二值化:84 | 中值滤波:3x3 | 白色提取:#ababab到#FFFFFF | 识别:[1@+ 3 =]
15:25:16.622/D:    阈值86 | 二值化:86 | 中值滤波:3x3 | 白色提取:#a9a9a9到#FFFFFF | 识别:[10+ 3 =]
15:25:16.623/D:    🎯 发现数字0！阈值: 86
15:25:16.625/D:    ✅ 白色字符最佳识别: [10+ 3 =] (阈值:86)
15:25:16.626/D: 🔧 方法9：分割识别法
15:25:16.628/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:25:17.032/D:    前数字区域(150×100)识别: [1060]
15:25:17.087/D:    运算符区域(90×80)识别: [-+]
15:25:17.132/D:    后数字区域(120×80)识别: [3]
15:25:17.133/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:25:17.178/D:    等号区域(66×80)识别: [=]
15:25:17.179/D:    🔗 精确组合结果: [1060 -+ 3 =]
15:25:17.180/D:    ✅ 分割识别成功: [1060 -+ 3 =]
15:25:17.190/D: ✅ 选择包含运算符的结果: [1@+ 3 =] (300DPI缩放)
15:25:17.192/D: 🏆 最终选择结果: [1@+ 3 =] (方法:300DPI缩放, 总尝试:4次)
15:25:17.194/D: 🔍 OCR识别结果: [1@+ 3 =]
15:25:17.198/D: 🔄 字符转换后: [10+ 3 =]
15:25:17.199/D: 🧹 清理后文本: [10+ 3 =]
15:25:17.200/D: ✅ 表达式解析成功: 10 + 3 = 13
15:25:17.201/D: 🧮 计算结果: 13
15:25:17.203/D: 📊 测试结果: {
  "成功": true,
  "原始文本": "1@+ 3 =",
  "表达式": "10 + 3 = 13",
  "结果": 13,
  "运算符": "+",
  "操作数1": 10,
  "操作数2": 3
}
15:25:17.214/D: 🧹 表达式OCR资源已清理
15:25:17.216/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时2.470000秒
15:25:39.717/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:25:39.964/D: ✅ 表达式识别屏幕捕获权限获取成功
15:25:39.965/D: 🧪 测试数学表达式识别...
15:25:39.966/D: 
🧮 ===== 开始识别数学表达式 =====
15:25:40.292/D: ✅ 表达式OCR初始化完成
15:25:40.293/D: 📐 表达式区域坐标: (51,436,426,113)
15:25:40.294/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:25:40.294/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:25:40.295/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:25:40.629/D:    ✅ 识别到: [‘9-3=]
15:25:40.635/D: 🔧 方法6：恢复成功配置（低阈值优化）
15:25:40.650/D: 🔧 方法8：白色字符三步处理法
15:25:40.995/D:    阈值138 | 二值化:138 | 中值滤波:3x3 | 白色提取:#757575到#FFFFFF | 识别:[‘9-3=]
15:25:41.179/D:    阈值140 | 二值化:140 | 中值滤波:3x3 | 白色提取:#737373到#FFFFFF | 识别:[‘9-3=]
15:25:41.362/D:    阈值142 | 二值化:142 | 中值滤波:3x3 | 白色提取:#717171到#FFFFFF | 识别:[9-3=]
15:25:41.546/D:    阈值144 | 二值化:144 | 中值滤波:3x3 | 白色提取:#6f6f6f到#FFFFFF | 识别:[9-3=]
15:25:41.729/D:    阈值146 | 二值化:146 | 中值滤波:3x3 | 白色提取:#6d6d6d到#FFFFFF | 识别:[‘9-3=]
15:25:41.912/D:    阈值148 | 二值化:148 | 中值滤波:3x3 | 白色提取:#6b6b6b到#FFFFFF | 识别:[‘9-3=]
15:25:42.099/D:    阈值150 | 二值化:150 | 中值滤波:3x3 | 白色提取:#696969到#FFFFFF | 识别:[-3=]
15:25:42.283/D:    阈值152 | 二值化:152 | 中值滤波:3x3 | 白色提取:#676767到#FFFFFF | 识别:[-3=]
15:25:42.475/D:    阈值154 | 二值化:154 | 中值滤波:3x3 | 白色提取:#656565到#FFFFFF | 识别:[-3=]
15:25:42.658/D:    阈值156 | 二值化:156 | 中值滤波:3x3 | 白色提取:#636363到#FFFFFF | 识别:[-3=]
15:25:42.843/D:    阈值158 | 二值化:158 | 中值滤波:3x3 | 白色提取:#616161到#FFFFFF | 识别:[-3=]
15:25:43.027/D:    阈值160 | 二值化:160 | 中值滤波:3x3 | 白色提取:#5f5f5f到#FFFFFF | 识别:[-3=]
15:25:43.210/D:    阈值162 | 二值化:162 | 中值滤波:3x3 | 白色提取:#5d5d5d到#FFFFFF | 识别:[-3=]
15:25:43.393/D:    阈值164 | 二值化:164 | 中值滤波:3x3 | 白色提取:#5b5b5b到#FFFFFF | 识别:[3-3=]
15:25:43.575/D:    阈值166 | 二值化:166 | 中值滤波:3x3 | 白色提取:#595959到#FFFFFF | 识别:[3-3=]
15:25:43.758/D:    阈值168 | 二值化:168 | 中值滤波:3x3 | 白色提取:#575757到#FFFFFF | 识别:[3-3=]
15:25:43.940/D:    阈值170 | 二值化:170 | 中值滤波:3x3 | 白色提取:#555555到#FFFFFF | 识别:[3-3=]
15:25:44.122/D:    阈值172 | 二值化:172 | 中值滤波:3x3 | 白色提取:#535353到#FFFFFF | 识别:[3-3=]
15:25:44.306/D:    阈值174 | 二值化:174 | 中值滤波:3x3 | 白色提取:#515151到#FFFFFF | 识别:[3-3=]
15:25:44.492/D:    阈值176 | 二值化:176 | 中值滤波:3x3 | 白色提取:#4f4f4f到#FFFFFF | 识别:[3-3=]
15:25:44.676/D:    阈值178 | 二值化:178 | 中值滤波:3x3 | 白色提取:#4d4d4d到#FFFFFF | 识别:[3-3=]
15:25:44.859/D:    阈值180 | 二值化:180 | 中值滤波:3x3 | 白色提取:#4b4b4b到#FFFFFF | 识别:[9-3=]
15:25:44.860/D:    ✅ 白色字符最佳识别: [‘9-3=] (阈值:138)
15:25:44.860/D: 🔧 方法9：分割识别法
15:25:44.861/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:25:45.219/D:    前数字区域(150×100)识别: [.]
15:25:45.228/D:    运算符区域(90×80)识别: [:]
15:25:45.281/D:    后数字区域(120×80)识别: [=]
15:25:45.282/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:25:45.302/D:    等号区域(66×80)识别: [=]
15:25:45.302/D:    🔗 精确组合结果: [. : = =]
15:25:45.303/D:    ✅ 分割识别成功: [. : = =]
15:25:45.311/D: ✅ 选择包含运算符的结果: [‘9-3=] (300DPI缩放)
15:25:45.312/D: 🏆 最终选择结果: [‘9-3=] (方法:300DPI缩放, 总尝试:3次)
15:25:45.312/D: 🔍 OCR识别结果: [‘9-3=]
15:25:45.315/D: 🔄 字符转换后: [‘9-3=]
15:25:45.315/D: 🧹 清理后文本: [‘9-3=]
15:25:45.317/D: ✅ 表达式解析成功: 9 - 3 = 6
15:25:45.317/D: 🧮 计算结果: 6
15:25:45.321/D: 📊 测试结果: {
  "成功": true,
  "原始文本": "‘9-3=",
  "表达式": "9 - 3 = 6",
  "结果": 6,
  "运算符": "-",
  "操作数1": 9,
  "操作数2": 3
}
15:25:45.331/D: 🧹 表达式OCR资源已清理
15:25:45.331/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时5.614000秒
15:28:46.966/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:28:47.234/D: ✅ 表达式识别屏幕捕获权限获取成功
15:28:47.236/D: 🧪 测试数学表达式识别...
15:28:47.237/D: 
🧮 ===== 开始识别数学表达式 =====
15:28:47.548/D: ✅ 表达式OCR初始化完成
15:28:47.550/D: 📐 表达式区域坐标: (51,436,426,113)
15:28:47.551/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:28:47.552/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:28:47.553/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:28:47.628/D:    ✅ 识别到: [55]
15:28:47.635/D: 🔧 方法6：恢复成功配置（低阈值优化）
15:28:47.649/D:    ✅ 识别到: [55]
15:28:47.653/D: 🔧 方法8：白色字符三步处理法
15:28:47.668/D:    阈值80 | 二值化:80 | 中值滤波:3x3 | 白色提取:#afafaf到#FFFFFF | 识别:[55]
15:28:47.684/D:    阈值82 | 二值化:82 | 中值滤波:3x3 | 白色提取:#adadad到#FFFFFF | 识别:[55]
15:28:47.700/D:    阈值84 | 二值化:84 | 中值滤波:3x3 | 白色提取:#ababab到#FFFFFF | 识别:[55]
15:28:47.714/D:    阈值86 | 二值化:86 | 中值滤波:3x3 | 白色提取:#a9a9a9到#FFFFFF | 识别:[55]
15:28:47.729/D:    阈值88 | 二值化:88 | 中值滤波:3x3 | 白色提取:#a7a7a7到#FFFFFF | 识别:[55]
15:28:47.744/D:    阈值90 | 二值化:90 | 中值滤波:3x3 | 白色提取:#a5a5a5到#FFFFFF | 识别:[55]
15:28:47.759/D:    阈值92 | 二值化:92 | 中值滤波:3x3 | 白色提取:#a3a3a3到#FFFFFF | 识别:[55]
15:28:47.773/D:    阈值94 | 二值化:94 | 中值滤波:3x3 | 白色提取:#a1a1a1到#FFFFFF | 识别:[55]
15:28:47.789/D:    阈值96 | 二值化:96 | 中值滤波:3x3 | 白色提取:#9f9f9f到#FFFFFF | 识别:[55]
15:28:47.803/D:    阈值98 | 二值化:98 | 中值滤波:3x3 | 白色提取:#9d9d9d到#FFFFFF | 识别:[55]
15:28:47.816/D:    阈值100 | 二值化:100 | 中值滤波:3x3 | 白色提取:#9b9b9b到#FFFFFF | 识别:[55]
15:28:47.834/D:    阈值102 | 二值化:102 | 中值滤波:3x3 | 白色提取:#999999到#FFFFFF | 识别:[55]
15:28:47.849/D:    阈值104 | 二值化:104 | 中值滤波:3x3 | 白色提取:#979797到#FFFFFF | 识别:[55]
15:28:47.873/D:    阈值106 | 二值化:106 | 中值滤波:3x3 | 白色提取:#959595到#FFFFFF | 识别:[55]
15:28:47.889/D:    阈值108 | 二值化:108 | 中值滤波:3x3 | 白色提取:#939393到#FFFFFF | 识别:[55]
15:28:47.906/D:    阈值110 | 二值化:110 | 中值滤波:3x3 | 白色提取:#919191到#FFFFFF | 识别:[55]
15:28:47.920/D:    阈值112 | 二值化:112 | 中值滤波:3x3 | 白色提取:#8f8f8f到#FFFFFF | 识别:[55]
15:28:47.934/D:    阈值114 | 二值化:114 | 中值滤波:3x3 | 白色提取:#8d8d8d到#FFFFFF | 识别:[55]
15:28:47.950/D:    阈值116 | 二值化:116 | 中值滤波:3x3 | 白色提取:#8b8b8b到#FFFFFF | 识别:[55]
15:28:47.966/D:    阈值118 | 二值化:118 | 中值滤波:3x3 | 白色提取:#898989到#FFFFFF | 识别:[55]
15:28:47.980/D:    阈值120 | 二值化:120 | 中值滤波:3x3 | 白色提取:#878787到#FFFFFF | 识别:[55]
15:28:47.995/D:    阈值122 | 二值化:122 | 中值滤波:3x3 | 白色提取:#858585到#FFFFFF | 识别:[55]
15:28:48.009/D:    阈值124 | 二值化:124 | 中值滤波:3x3 | 白色提取:#838383到#FFFFFF | 识别:[55]
15:28:48.025/D:    阈值126 | 二值化:126 | 中值滤波:3x3 | 白色提取:#818181到#FFFFFF | 识别:[55]
15:28:48.040/D:    阈值128 | 二值化:128 | 中值滤波:3x3 | 白色提取:#7f7f7f到#FFFFFF | 识别:[55]
15:28:48.054/D:    阈值130 | 二值化:130 | 中值滤波:3x3 | 白色提取:#7d7d7d到#FFFFFF | 识别:[55]
15:28:48.069/D:    阈值132 | 二值化:132 | 中值滤波:3x3 | 白色提取:#7b7b7b到#FFFFFF | 识别:[55]
15:28:48.085/D:    阈值134 | 二值化:134 | 中值滤波:3x3 | 白色提取:#797979到#FFFFFF | 识别:[55]
15:28:48.100/D:    阈值136 | 二值化:136 | 中值滤波:3x3 | 白色提取:#777777到#FFFFFF | 识别:[55]
15:28:48.114/D:    阈值138 | 二值化:138 | 中值滤波:3x3 | 白色提取:#757575到#FFFFFF | 识别:[55]
15:28:48.127/D:    阈值140 | 二值化:140 | 中值滤波:3x3 | 白色提取:#737373到#FFFFFF | 识别:[55]
15:28:48.140/D:    阈值142 | 二值化:142 | 中值滤波:3x3 | 白色提取:#717171到#FFFFFF | 识别:[55]
15:28:48.154/D:    阈值144 | 二值化:144 | 中值滤波:3x3 | 白色提取:#6f6f6f到#FFFFFF | 识别:[55]
15:28:48.178/D:    阈值146 | 二值化:146 | 中值滤波:3x3 | 白色提取:#6d6d6d到#FFFFFF | 识别:[55]
15:28:48.194/D:    阈值148 | 二值化:148 | 中值滤波:3x3 | 白色提取:#6b6b6b到#FFFFFF | 识别:[55]
15:28:48.207/D:    阈值150 | 二值化:150 | 中值滤波:3x3 | 白色提取:#696969到#FFFFFF | 识别:[55]
15:28:48.225/D:    阈值152 | 二值化:152 | 中值滤波:3x3 | 白色提取:#676767到#FFFFFF | 识别:[55]
15:28:48.240/D:    阈值154 | 二值化:154 | 中值滤波:3x3 | 白色提取:#656565到#FFFFFF | 识别:[55]
15:28:48.254/D:    阈值156 | 二值化:156 | 中值滤波:3x3 | 白色提取:#636363到#FFFFFF | 识别:[55]
15:28:48.268/D:    阈值158 | 二值化:158 | 中值滤波:3x3 | 白色提取:#616161到#FFFFFF | 识别:[55]
15:28:48.287/D:    阈值160 | 二值化:160 | 中值滤波:3x3 | 白色提取:#5f5f5f到#FFFFFF | 识别:[55]
15:28:48.303/D:    阈值162 | 二值化:162 | 中值滤波:3x3 | 白色提取:#5d5d5d到#FFFFFF | 识别:[55]
15:28:48.318/D:    阈值164 | 二值化:164 | 中值滤波:3x3 | 白色提取:#5b5b5b到#FFFFFF | 识别:[55]
15:28:48.334/D:    阈值166 | 二值化:166 | 中值滤波:3x3 | 白色提取:#595959到#FFFFFF | 识别:[55]
15:28:48.349/D:    阈值168 | 二值化:168 | 中值滤波:3x3 | 白色提取:#575757到#FFFFFF | 识别:[55]
15:28:48.365/D:    阈值170 | 二值化:170 | 中值滤波:3x3 | 白色提取:#555555到#FFFFFF | 识别:[55]
15:28:48.380/D:    阈值172 | 二值化:172 | 中值滤波:3x3 | 白色提取:#535353到#FFFFFF | 识别:[55]
15:28:48.394/D:    阈值174 | 二值化:174 | 中值滤波:3x3 | 白色提取:#515151到#FFFFFF | 识别:[55]
15:28:48.409/D:    阈值176 | 二值化:176 | 中值滤波:3x3 | 白色提取:#4f4f4f到#FFFFFF | 识别:[55]
15:28:48.425/D:    阈值178 | 二值化:178 | 中值滤波:3x3 | 白色提取:#4d4d4d到#FFFFFF | 识别:[55]
15:28:48.441/D:    阈值180 | 二值化:180 | 中值滤波:3x3 | 白色提取:#4b4b4b到#FFFFFF | 识别:[55]
15:28:48.442/D: 🔧 方法9：分割识别法
15:28:48.443/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:28:48.947/D:    前数字区域(150×100)识别: [.]
15:28:49.030/D:    运算符区域(90×80)识别: [— 1]
15:28:49.035/D:    后数字区域(120×80)识别: [5]
15:28:49.037/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:28:49.057/D:    等号区域(66×80)识别: [~]
15:28:49.059/D:    🔗 精确组合结果: [. — 1 5 ~]
15:28:49.060/D:    ✅ 分割识别成功: [. — 1 5 ~]
15:28:49.075/D: 🏆 选择最长结果: [. — 1 5 ~] (分割识别法)
15:28:49.076/D: 🏆 最终选择结果: [. — 1 5 ~] (方法:分割识别法, 总尝试:3次)
15:28:49.077/D: 🔍 OCR识别结果: [. — 1 5 ~]
15:28:49.080/D: 🔄 字符转换后: [. — 1 5 ~]
15:28:49.081/D: 🧹 清理后文本: [. — 1 5 ~]
15:28:49.081/D: ❌ 表达式解析失败: 未找到有效运算符
15:28:49.083/D: 📊 测试结果: {
  "错误": "未找到有效运算符",
  "原始文本": ". — 1 5 ~"
}
15:28:49.093/D: 🧹 表达式OCR资源已清理
15:28:49.095/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时2.127000秒
发送项目耗时: 0.096 秒
发送项目耗时: 0.088 秒
15:31:14.486/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:31:14.831/D: ✅ 表达式识别屏幕捕获权限获取成功
15:31:14.834/D: 🧪 测试数学表达式识别...
15:31:14.835/D: 
🧮 ===== 开始识别数学表达式 =====
15:31:15.182/D: ✅ 表达式OCR初始化完成
15:31:15.184/D: 📐 表达式区域坐标: (51,436,426,113)
15:31:15.185/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:31:15.186/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:31:15.188/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:31:15.265/D:    ✅ 识别到: [55]
15:31:15.273/D: 🔧 方法8：白色字符三步处理法
15:31:15.287/D:    阈值80 | 二值化:80 | 中值滤波:3x3 | 白色提取:#afafaf到#FFFFFF | 识别:[55]
15:31:15.301/D:    阈值82 | 二值化:82 | 中值滤波:3x3 | 白色提取:#adadad到#FFFFFF | 识别:[55]
15:31:15.315/D:    阈值84 | 二值化:84 | 中值滤波:3x3 | 白色提取:#ababab到#FFFFFF | 识别:[55]
15:31:15.330/D:    阈值86 | 二值化:86 | 中值滤波:3x3 | 白色提取:#a9a9a9到#FFFFFF | 识别:[55]
15:31:15.344/D:    阈值88 | 二值化:88 | 中值滤波:3x3 | 白色提取:#a7a7a7到#FFFFFF | 识别:[55]
15:31:15.356/D:    阈值90 | 二值化:90 | 中值滤波:3x3 | 白色提取:#a5a5a5到#FFFFFF | 识别:[55]
15:31:15.370/D:    阈值92 | 二值化:92 | 中值滤波:3x3 | 白色提取:#a3a3a3到#FFFFFF | 识别:[55]
15:31:15.383/D:    阈值94 | 二值化:94 | 中值滤波:3x3 | 白色提取:#a1a1a1到#FFFFFF | 识别:[55]
15:31:15.398/D:    阈值96 | 二值化:96 | 中值滤波:3x3 | 白色提取:#9f9f9f到#FFFFFF | 识别:[55]
15:31:15.411/D:    阈值98 | 二值化:98 | 中值滤波:3x3 | 白色提取:#9d9d9d到#FFFFFF | 识别:[55]
15:31:15.425/D:    阈值100 | 二值化:100 | 中值滤波:3x3 | 白色提取:#9b9b9b到#FFFFFF | 识别:[55]
15:31:15.442/D:    阈值102 | 二值化:102 | 中值滤波:3x3 | 白色提取:#999999到#FFFFFF | 识别:[55]
15:31:15.470/D:    阈值104 | 二值化:104 | 中值滤波:3x3 | 白色提取:#979797到#FFFFFF | 识别:[55]
15:31:15.483/D:    阈值106 | 二值化:106 | 中值滤波:3x3 | 白色提取:#959595到#FFFFFF | 识别:[55]
15:31:15.496/D:    阈值108 | 二值化:108 | 中值滤波:3x3 | 白色提取:#939393到#FFFFFF | 识别:[55]
15:31:15.510/D:    阈值110 | 二值化:110 | 中值滤波:3x3 | 白色提取:#919191到#FFFFFF | 识别:[55]
15:31:15.523/D:    阈值112 | 二值化:112 | 中值滤波:3x3 | 白色提取:#8f8f8f到#FFFFFF | 识别:[55]
15:31:15.537/D:    阈值114 | 二值化:114 | 中值滤波:3x3 | 白色提取:#8d8d8d到#FFFFFF | 识别:[55]
15:31:15.551/D:    阈值116 | 二值化:116 | 中值滤波:3x3 | 白色提取:#8b8b8b到#FFFFFF | 识别:[55]
15:31:15.564/D:    阈值118 | 二值化:118 | 中值滤波:3x3 | 白色提取:#898989到#FFFFFF | 识别:[55]
15:31:15.577/D:    阈值120 | 二值化:120 | 中值滤波:3x3 | 白色提取:#878787到#FFFFFF | 识别:[55]
15:31:15.590/D:    阈值122 | 二值化:122 | 中值滤波:3x3 | 白色提取:#858585到#FFFFFF | 识别:[55]
15:31:15.604/D:    阈值124 | 二值化:124 | 中值滤波:3x3 | 白色提取:#838383到#FFFFFF | 识别:[55]
15:31:15.618/D:    阈值126 | 二值化:126 | 中值滤波:3x3 | 白色提取:#818181到#FFFFFF | 识别:[55]
15:31:15.631/D:    阈值128 | 二值化:128 | 中值滤波:3x3 | 白色提取:#7f7f7f到#FFFFFF | 识别:[55]
15:31:15.645/D:    阈值130 | 二值化:130 | 中值滤波:3x3 | 白色提取:#7d7d7d到#FFFFFF | 识别:[55]
15:31:15.659/D:    阈值132 | 二值化:132 | 中值滤波:3x3 | 白色提取:#7b7b7b到#FFFFFF | 识别:[55]
15:31:15.682/D:    阈值134 | 二值化:134 | 中值滤波:3x3 | 白色提取:#797979到#FFFFFF | 识别:[55]
15:31:15.695/D:    阈值136 | 二值化:136 | 中值滤波:3x3 | 白色提取:#777777到#FFFFFF | 识别:[55]
15:31:15.707/D:    阈值138 | 二值化:138 | 中值滤波:3x3 | 白色提取:#757575到#FFFFFF | 识别:[55]
15:31:15.719/D:    阈值140 | 二值化:140 | 中值滤波:3x3 | 白色提取:#737373到#FFFFFF | 识别:[55]
15:31:15.733/D:    阈值142 | 二值化:142 | 中值滤波:3x3 | 白色提取:#717171到#FFFFFF | 识别:[55]
15:31:15.745/D:    阈值144 | 二值化:144 | 中值滤波:3x3 | 白色提取:#6f6f6f到#FFFFFF | 识别:[55]
15:31:15.758/D:    阈值146 | 二值化:146 | 中值滤波:3x3 | 白色提取:#6d6d6d到#FFFFFF | 识别:[55]
15:31:15.772/D:    阈值148 | 二值化:148 | 中值滤波:3x3 | 白色提取:#6b6b6b到#FFFFFF | 识别:[55]
15:31:15.785/D:    阈值150 | 二值化:150 | 中值滤波:3x3 | 白色提取:#696969到#FFFFFF | 识别:[55]
15:31:15.800/D:    阈值152 | 二值化:152 | 中值滤波:3x3 | 白色提取:#676767到#FFFFFF | 识别:[55]
15:31:15.813/D:    阈值154 | 二值化:154 | 中值滤波:3x3 | 白色提取:#656565到#FFFFFF | 识别:[55]
15:31:15.827/D:    阈值156 | 二值化:156 | 中值滤波:3x3 | 白色提取:#636363到#FFFFFF | 识别:[55]
15:31:15.841/D:    阈值158 | 二值化:158 | 中值滤波:3x3 | 白色提取:#616161到#FFFFFF | 识别:[55]
15:31:15.854/D:    阈值160 | 二值化:160 | 中值滤波:3x3 | 白色提取:#5f5f5f到#FFFFFF | 识别:[55]
15:31:15.869/D:    阈值162 | 二值化:162 | 中值滤波:3x3 | 白色提取:#5d5d5d到#FFFFFF | 识别:[55]
15:31:15.882/D:    阈值164 | 二值化:164 | 中值滤波:3x3 | 白色提取:#5b5b5b到#FFFFFF | 识别:[55]
15:31:15.897/D:    阈值166 | 二值化:166 | 中值滤波:3x3 | 白色提取:#595959到#FFFFFF | 识别:[55]
15:31:15.911/D:    阈值168 | 二值化:168 | 中值滤波:3x3 | 白色提取:#575757到#FFFFFF | 识别:[55]
15:31:15.925/D:    阈值170 | 二值化:170 | 中值滤波:3x3 | 白色提取:#555555到#FFFFFF | 识别:[55]
15:31:15.939/D:    阈值172 | 二值化:172 | 中值滤波:3x3 | 白色提取:#535353到#FFFFFF | 识别:[55]
15:31:15.952/D:    阈值174 | 二值化:174 | 中值滤波:3x3 | 白色提取:#515151到#FFFFFF | 识别:[55]
15:31:15.966/D:    阈值176 | 二值化:176 | 中值滤波:3x3 | 白色提取:#4f4f4f到#FFFFFF | 识别:[55]
15:31:15.979/D:    阈值178 | 二值化:178 | 中值滤波:3x3 | 白色提取:#4d4d4d到#FFFFFF | 识别:[55]
15:31:15.994/D:    阈值180 | 二值化:180 | 中值滤波:3x3 | 白色提取:#4b4b4b到#FFFFFF | 识别:[55]
15:31:15.995/D: 🔧 方法9：分割识别法
15:31:15.996/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:31:16.323/D:    前数字区域(150×100)识别: []
15:31:16.330/D:    运算符区域(90×80)识别: []
15:31:16.331/D:    后数字区域(120×80)识别: []
15:31:16.332/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:31:16.334/D:    等号区域(66×80)识别: []
15:31:16.334/D:    🔗 精确组合结果: [   ]
15:31:16.344/D: 🏆 选择最长结果: [55] (300DPI缩放)
15:31:16.344/D: 🏆 最终选择结果: [55] (方法:300DPI缩放, 总尝试:1次)
15:31:16.345/D: 🔍 OCR识别结果: [55]
15:31:16.347/D: 🔄 字符转换后: [55]
15:31:16.348/D: 🧹 清理后文本: [55]
15:31:16.348/D: ❌ 表达式解析失败: 未找到有效运算符
15:31:16.350/D: 📊 测试结果: {
  "错误": "未找到有效运算符",
  "原始文本": "55"
}
15:31:16.359/D: 🧹 表达式OCR资源已清理
15:31:16.360/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时1.873000秒
15:33:02.052/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:33:02.300/D: ✅ 表达式识别屏幕捕获权限获取成功
15:33:02.302/D: 🧪 测试数学表达式识别...
15:33:02.302/D: 
🧮 ===== 开始识别数学表达式 =====
15:33:02.631/D: ✅ 表达式OCR初始化完成
15:33:02.632/D: 📐 表达式区域坐标: (51,436,426,113)
15:33:02.633/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:33:02.633/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:33:02.634/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:33:02.973/D:    ✅ 识别到: [1@+ 3 =]
15:33:02.979/D: 🔧 方法8：白色字符三步处理法
15:33:03.186/D:    阈值80 | 二值化:80 | 中值滤波:3x3 | 白色提取:#afafaf到#FFFFFF | 识别:[1@+ 3 =]
15:33:03.376/D:    阈值82 | 二值化:82 | 中值滤波:3x3 | 白色提取:#adadad到#FFFFFF | 识别:[1@+ 3 =]
15:33:03.568/D:    阈值84 | 二值化:84 | 中值滤波:3x3 | 白色提取:#ababab到#FFFFFF | 识别:[1@+ 3 =]
15:33:03.756/D:    阈值86 | 二值化:86 | 中值滤波:3x3 | 白色提取:#a9a9a9到#FFFFFF | 识别:[10+ 3 =]
15:33:03.757/D:    🎯 发现数字0！阈值: 86
15:33:03.758/D:    ✅ 白色字符最佳识别: [10+ 3 =] (阈值:86)
15:33:03.758/D: 🔧 方法9：分割识别法
15:33:03.759/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:33:04.149/D:    前数字区域(150×100)识别: [1060]
15:33:04.203/D:    运算符区域(90×80)识别: [-+]
15:33:04.247/D:    后数字区域(120×80)识别: [3]
15:33:04.248/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:33:04.250/D:    等号区域(66×80)识别: []
15:33:04.250/D:    🔗 精确组合结果: [1060 -+ 3 ]
15:33:04.251/D:    ✅ 分割识别成功: [1060 -+ 3 ]
15:33:04.260/D: ✅ 选择包含运算符的结果: [1@+ 3 =] (300DPI缩放)
15:33:04.260/D: 🏆 最终选择结果: [1@+ 3 =] (方法:300DPI缩放, 总尝试:3次)
15:33:04.261/D: 🔍 OCR识别结果: [1@+ 3 =]
15:33:04.263/D: 🔄 字符转换后: [10+ 3 =]
15:33:04.264/D: 🧹 清理后文本: [10+ 3 =]
15:33:04.265/D: ✅ 表达式解析成功: 10 + 3 = 13
15:33:04.266/D: 🧮 计算结果: 13
15:33:04.268/D: 📊 测试结果: {
  "成功": true,
  "原始文本": "1@+ 3 =",
  "表达式": "10 + 3 = 13",
  "结果": 13,
  "运算符": "+",
  "操作数1": 10,
  "操作数2": 3
}
15:33:04.277/D: 🧹 表达式OCR资源已清理
15:33:04.278/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时2.223000秒
15:33:44.496/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/公式.js]
15:33:44.884/D: ✅ 表达式识别屏幕捕获权限获取成功
15:33:44.886/D: 🧪 测试数学表达式识别...
15:33:44.887/D: 
🧮 ===== 开始识别数学表达式 =====
15:33:45.216/D: ✅ 表达式OCR初始化完成
15:33:45.217/D: 📐 表达式区域坐标: (51,436,426,113)
15:33:45.218/D: 🔍 使用Tesseract官方推荐的图像预处理方法...
15:33:45.219/D: 📊 关键优化：DPI≥300、去噪、适当缩放
15:33:45.219/D: 🔧 方法1：缩放到300+DPI（官方推荐）
15:33:45.557/D:    ✅ 识别到: [1| 9=]
15:33:45.563/D: 🔧 方法8：白色字符三步处理法
15:33:45.766/D:    阈值80 | 二值化:80 | 中值滤波:3x3 | 白色提取:#afafaf到#FFFFFF | 识别:[1| 9=]
15:33:45.962/D:    阈值82 | 二值化:82 | 中值滤波:3x3 | 白色提取:#adadad到#FFFFFF | 识别:[1| 9=]
15:33:46.152/D:    阈值84 | 二值化:84 | 中值滤波:3x3 | 白色提取:#ababab到#FFFFFF | 识别:[1| 9=]
15:33:46.350/D:    阈值86 | 二值化:86 | 中值滤波:3x3 | 白色提取:#a9a9a9到#FFFFFF | 识别:[1| 9=]
15:33:46.543/D:    阈值88 | 二值化:88 | 中值滤波:3x3 | 白色提取:#a7a7a7到#FFFFFF | 识别:[1| 9=]
15:33:46.740/D:    阈值90 | 二值化:90 | 中值滤波:3x3 | 白色提取:#a5a5a5到#FFFFFF | 识别:[1| 9=]
15:33:46.934/D:    阈值92 | 二值化:92 | 中值滤波:3x3 | 白色提取:#a3a3a3到#FFFFFF | 识别:[1| 9=]
15:33:47.143/D:    阈值94 | 二值化:94 | 中值滤波:3x3 | 白色提取:#a1a1a1到#FFFFFF | 识别:[1| 9=]
15:33:47.339/D:    阈值96 | 二值化:96 | 中值滤波:3x3 | 白色提取:#9f9f9f到#FFFFFF | 识别:[1| 9=]
15:33:47.534/D:    阈值98 | 二值化:98 | 中值滤波:3x3 | 白色提取:#9d9d9d到#FFFFFF | 识别:[1| 9=]
15:33:47.739/D:    阈值100 | 二值化:100 | 中值滤波:3x3 | 白色提取:#9b9b9b到#FFFFFF | 识别:[1| 9=]
15:33:47.935/D:    阈值102 | 二值化:102 | 中值滤波:3x3 | 白色提取:#999999到#FFFFFF | 识别:[1| 9=]
15:33:48.133/D:    阈值104 | 二值化:104 | 中值滤波:3x3 | 白色提取:#979797到#FFFFFF | 识别:[1| 9=]
15:33:48.323/D:    阈值106 | 二值化:106 | 中值滤波:3x3 | 白色提取:#959595到#FFFFFF | 识别:[1| 9=]
15:33:48.519/D:    阈值108 | 二值化:108 | 中值滤波:3x3 | 白色提取:#939393到#FFFFFF | 识别:[1| 9=]
15:33:48.707/D:    阈值110 | 二值化:110 | 中值滤波:3x3 | 白色提取:#919191到#FFFFFF | 识别:[1| 9=]
15:33:48.901/D:    阈值112 | 二值化:112 | 中值滤波:3x3 | 白色提取:#8f8f8f到#FFFFFF | 识别:[1| 9=]
15:33:49.095/D:    阈值114 | 二值化:114 | 中值滤波:3x3 | 白色提取:#8d8d8d到#FFFFFF | 识别:[1| 9=]
15:33:49.289/D:    阈值116 | 二值化:116 | 中值滤波:3x3 | 白色提取:#8b8b8b到#FFFFFF | 识别:[1| 9=]
15:33:49.482/D:    阈值118 | 二值化:118 | 中值滤波:3x3 | 白色提取:#898989到#FFFFFF | 识别:[1| 9=]
15:33:49.670/D:    阈值120 | 二值化:120 | 中值滤波:3x3 | 白色提取:#878787到#FFFFFF | 识别:[1| 9=]
15:33:49.859/D:    阈值122 | 二值化:122 | 中值滤波:3x3 | 白色提取:#858585到#FFFFFF | 识别:[1 9=]
15:33:50.053/D:    阈值124 | 二值化:124 | 中值滤波:3x3 | 白色提取:#838383到#FFFFFF | 识别:[1 9=]
15:33:50.244/D:    阈值126 | 二值化:126 | 中值滤波:3x3 | 白色提取:#818181到#FFFFFF | 识别:[19=]
15:33:50.434/D:    阈值128 | 二值化:128 | 中值滤波:3x3 | 白色提取:#7f7f7f到#FFFFFF | 识别:[19=]
15:33:50.633/D:    阈值130 | 二值化:130 | 中值滤波:3x3 | 白色提取:#7d7d7d到#FFFFFF | 识别:[19=]
15:33:50.824/D:    阈值132 | 二值化:132 | 中值滤波:3x3 | 白色提取:#7b7b7b到#FFFFFF | 识别:[1 9=]
15:33:51.013/D:    阈值134 | 二值化:134 | 中值滤波:3x3 | 白色提取:#797979到#FFFFFF | 识别:[1| 9=]
15:33:51.206/D:    阈值136 | 二值化:136 | 中值滤波:3x3 | 白色提取:#777777到#FFFFFF | 识别:[19=]
15:33:51.393/D:    阈值138 | 二值化:138 | 中值滤波:3x3 | 白色提取:#757575到#FFFFFF | 识别:[19=]
15:33:51.581/D:    阈值140 | 二值化:140 | 中值滤波:3x3 | 白色提取:#737373到#FFFFFF | 识别:[1| 9=]
15:33:51.769/D:    阈值142 | 二值化:142 | 中值滤波:3x3 | 白色提取:#717171到#FFFFFF | 识别:[19=]
15:33:51.960/D:    阈值144 | 二值化:144 | 中值滤波:3x3 | 白色提取:#6f6f6f到#FFFFFF | 识别:[1| 9=]
15:33:52.147/D:    阈值146 | 二值化:146 | 中值滤波:3x3 | 白色提取:#6d6d6d到#FFFFFF | 识别:[1| 9=]
15:33:52.342/D:    阈值148 | 二值化:148 | 中值滤波:3x3 | 白色提取:#6b6b6b到#FFFFFF | 识别:[1| 9=]
15:33:52.542/D:    阈值150 | 二值化:150 | 中值滤波:3x3 | 白色提取:#696969到#FFFFFF | 识别:[1| 9=]
15:33:52.734/D:    阈值152 | 二值化:152 | 中值滤波:3x3 | 白色提取:#676767到#FFFFFF | 识别:[1|9=]
15:33:52.923/D:    阈值154 | 二值化:154 | 中值滤波:3x3 | 白色提取:#656565到#FFFFFF | 识别:[1|9=]
15:33:53.111/D:    阈值156 | 二值化:156 | 中值滤波:3x3 | 白色提取:#636363到#FFFFFF | 识别:[1|9=]
15:33:53.297/D:    阈值158 | 二值化:158 | 中值滤波:3x3 | 白色提取:#616161到#FFFFFF | 识别:[1|9=]
15:33:53.487/D:    阈值160 | 二值化:160 | 中值滤波:3x3 | 白色提取:#5f5f5f到#FFFFFF | 识别:[1|9=]
15:33:53.674/D:    阈值162 | 二值化:162 | 中值滤波:3x3 | 白色提取:#5d5d5d到#FFFFFF | 识别:[1|9=]
15:33:53.864/D:    阈值164 | 二值化:164 | 中值滤波:3x3 | 白色提取:#5b5b5b到#FFFFFF | 识别:[1|9=]
15:33:54.051/D:    阈值166 | 二值化:166 | 中值滤波:3x3 | 白色提取:#595959到#FFFFFF | 识别:[1|9=]
15:33:54.240/D:    阈值168 | 二值化:168 | 中值滤波:3x3 | 白色提取:#575757到#FFFFFF | 识别:[1|9=]
15:33:54.430/D:    阈值170 | 二值化:170 | 中值滤波:3x3 | 白色提取:#555555到#FFFFFF | 识别:[1|9=]
15:33:54.622/D:    阈值172 | 二值化:172 | 中值滤波:3x3 | 白色提取:#535353到#FFFFFF | 识别:[19=]
15:33:54.818/D:    阈值174 | 二值化:174 | 中值滤波:3x3 | 白色提取:#515151到#FFFFFF | 识别:[1|9=]
15:33:55.007/D:    阈值176 | 二值化:176 | 中值滤波:3x3 | 白色提取:#4f4f4f到#FFFFFF | 识别:[1| 9=]
15:33:55.197/D:    阈值178 | 二值化:178 | 中值滤波:3x3 | 白色提取:#4d4d4d到#FFFFFF | 识别:[1|9=]
15:33:55.387/D:    阈值180 | 二值化:180 | 中值滤波:3x3 | 白色提取:#4b4b4b到#FFFFFF | 识别:[1|9=]
15:33:55.388/D: 🔧 方法9：分割识别法
15:33:55.388/D: 📐 精确分割: 前数字(150×100) + 运算符(90×80) + 后数字(120×80) + 等号(90×80)
15:33:55.781/D:    前数字区域(150×100)识别: [10]
15:33:55.834/D:    运算符区域(90×80)识别: [=]
15:33:55.837/D:    后数字区域(120×80)识别: []
15:33:55.837/D:    等号区域计算: 起始位置=360, 剩余宽度=66, 实际宽度=66
15:33:55.839/D:    等号区域(66×80)识别: []
15:33:55.839/D:    🔗 精确组合结果: [10 =  ]
15:33:55.841/D:    ✅ 分割识别成功: [10 =  ]
15:33:55.850/D: 🏆 选择最长结果: [10 =  ] (分割识别法)
15:33:55.850/D: 🏆 最终选择结果: [10 =  ] (方法:分割识别法, 总尝试:2次)
15:33:55.851/D: 🔍 OCR识别结果: [10 =  ]
15:33:55.855/D: 🔄 字符转换后: [10 =]
15:33:55.856/D: 🧹 清理后文本: [10 =]
15:33:55.857/D: ❌ 表达式解析失败: 未找到有效运算符
15:33:55.858/D: 📊 测试结果: {
  "错误": "未找到有效运算符",
  "原始文本": "10 =  "
}
15:33:55.868/D: 🧹 表达式OCR资源已清理
15:33:55.869/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/公式.js ]运行结束，用时11.360000秒
