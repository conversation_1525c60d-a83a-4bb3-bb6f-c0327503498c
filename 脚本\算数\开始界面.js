/**
 * 算数游戏开始界面模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

// 导入截图功能模块
var 截图模块 = require('../常用功能/截图.js');

function 点击开始() {
    try {
        console.log("🎮 开始界面点击流程");

        // 检查截图权限
        console.log("🔐 检查截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 截图权限申请失败");
            return false;
        }
        console.log("✅ 截图权限申请成功");

        // 等待权限生效
        sleep(1000);

        // 第一步：在屏幕范围139,61,260,156中判断点击开始界面.png
        console.log("📝 第一步：点击开始界面");

        var 开始界面图片 = "../../assets/算数游戏/新手教程图片/开始界面.png";
        console.log("🔍 图片路径: " + 开始界面图片);

        var 开始界面结果 = 截图模块.查找_图片(开始界面图片, 139, 61, 260, 156, 5, 1000, "判断点击", 1, 0.7);

        if (开始界面结果) {
            console.log("✅ 第一步完成：成功点击开始界面");
        } else {
            console.log("⚠️ 第一步未找到按钮：可能已经在开始界面了");
        }

        // 随机延时1-2秒
        var 随机延时 = random(1000, 2000);
        console.log("⏳ 随机延时 " + 随机延时 + " 毫秒...");
        sleep(随机延时);

        // 第二步：在屏幕范围129,715,273,123判断点击开始按钮.png
        console.log("📝 第二步：点击开始游戏按钮");

        var 开始按钮图片 = "../../assets/算数游戏/新手教程图片/开始按钮.png";
        console.log("🔍 图片路径: " + 开始按钮图片);

        var 开始按钮结果 = 截图模块.查找_图片(开始按钮图片, 129, 715, 273, 123, 5, 1000, "判断点击", 3, 0.8);

        if (开始按钮结果) {
            console.log("✅ 第二步完成：成功点击开始游戏按钮");
        } else {
            console.log("⚠️ 第二步未找到按钮：可能已经开始游戏了");
        }

        return true;

    } catch (error) {
        // 注意：这里的错误处理是正常的容错机制，不是程序bug
        // 作用：防止图片文件缺失、权限问题等导致整个脚本崩溃
        // 效果：开始界面失败时程序会继续执行后续功能，而不是停止运行
        console.error("❌ 正常的容错机制，不是程序bug: " + error);
        return false;
    }
}

function 每日领奖() {
    try {
        console.log("🎁 每日领奖流程");

        // 检查截图权限
        console.log("🔐 检查截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 截图权限申请失败");
            return false;
        }
        console.log("✅ 截图权限申请成功");

        // 等待权限生效
        sleep(1000);

        // 第一步：在屏幕范围69,618,400,280中判断点击bonus.png
        //第一步是必须要成功识别匹配到才能进入第二步
        console.log("📝 第一步：点击bonus按钮");

        var bonus图片 = "../../assets/算数游戏/新手教程图片/bonus.png";
        console.log("🔍 图片路径: " + bonus图片);

        var bonus结果 = 截图模块.查找_图片(bonus图片, 69, 618, 400, 280, 3, 1000, "判断点击", 2, 0.8);

        if (bonus结果) {
            console.log("✅ 第一步完成：成功点击bonus按钮");

            // 延时2-3秒
            var 随机延时 = random(2000, 3000);
            console.log("⏳ 随机延时 " + 随机延时 + " 毫秒...");
            sleep(随机延时);

            // 第二步：在相同屏幕范围判断点击确认.png
            console.log("📝 第二步：点击确认按钮");

            var 确认图片 = "../../assets/算数游戏/新手教程图片/确认.png";
            console.log("🔍 图片路径: " + 确认图片);

            var 确认结果 = 截图模块.查找_图片(确认图片, 69, 618, 400, 280, 3, 1000, "判断点击", 2, 0.8);

            if (确认结果) {
                console.log("✅ 第二步完成：成功点击确认按钮");
            } else {
                console.log("⚠️ 第二步未找到按钮：可能已经确认过了");
            }

            console.log("🎉 每日领奖流程执行完毕");
        } else {
            console.log("⚠️ 未找到按钮：可能已经领取过了，结束每日领奖");
            console.log("🎉 每日领奖程序结束");
        }
        return true;

    } catch (error) {
        // 注意：这里的错误处理是正常的容错机制，不是程序bug
        // 作用：防止图片文件缺失、权限问题等导致整个脚本崩溃
        // 效果：每日领奖失败时程序会继续执行后续功能，而不是停止运行
        console.error("❌ 正常的容错机制，不是程序bug: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    点击开始: 点击开始,
    每日领奖: 每日领奖
};