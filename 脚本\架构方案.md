# Magic游戏辅助脚本架构方案（重构版）

## 📋 项目概述
基于AutoXjs ozobiozobi v6.5.8.17的Magic游戏辅助脚本完整架构设计
采用智能前端控制 + 用户设置管理的新架构

## 🎯 核心游戏流程设计（重构版）

### 主流程架构（智能前端控制）
```
游戏_程序() {
    // 读取用户设置
    var 用户设置 = 用户设置管理.读取设置();
    console.log("📖 已读取用户前端设置");

    // 第1步：游戏启动检查（必执行）
    检查游戏是否已运行，未运行则启动BigTime游戏
    注意：屏幕捕获权限已在程序启动时全局获取

    // 第2步：随机等待时间（必执行）
    random(4000, 6000)随机等待，为下一个功能执行做准备

    // 第3步：帐号登陆（前端勾选框控制）
    if (用户设置.启用帐号登陆) {
        执行帐号登陆：8秒内查找跳过按钮，处理注册流程
    } else {
        跳过帐号登陆（用户未勾选）
    }

    // 第4步：随机等待时间（必执行）
    random(500, 2500)随机等待，为首次教程执行做准备

    // 第5步：首次教程（前端勾选框控制）
    if (用户设置.启用首次教程) {
        执行首次教程：9个完整步骤
        - 感兴趣按钮判断（重试2次，2秒查找）
        - 右箭头点击（重试2次，2秒查找）
        - 进入赛车（重试2次，2秒查找）
        - 游戏开始（重试2次，2秒查找）
        - 赛车开始界面左滑（随机坐标，800-1500毫秒）
        - 赛车开始界面2右滑（10秒内每5秒查找，1000-2000毫秒）
        - 上滑操作（重试2次，1000-2000毫秒）
        - 下滑操作（重试2次，800-1500毫秒）
        - 结束教程按钮（重试2次，点击验证）
    } else {
        跳过首次教程（用户未勾选）
    }

    // 第6步：检查分数（前端勾选框控制）
    if (用户设置.启用检查分数) {
        执行检查分数：首次提示，达到多少分可以提现
        - 在分数提示大图中查找分数确认键小图（图片匹配）
        - 查找游戏画面中的结束教程按钮
    } else {
        跳过检查分数（用户未勾选）
    }

    // 第7步：教程提现（前端勾选框控制）
    if (用户设置.启用教程提现) {
        执行教程提现：首次提现教程流程
    } else {
        跳过教程提现（用户未勾选）
    }

    // 第8-12步：游戏循环（必执行）
    while (继续游戏) {
        第8步：检查_登陆（包含每日提现检查12-15点）
        第9步：每日领奖
        第10步：游玩_裂球
        第11步：观看_广告（游戏后立即进入）
        第12步：检查_涨分（广告后出现）

        // 判断逻辑（所有情况都跳回第8步）
        if (需要重启应用) {
            重启应用();
            跳转到第8步();
        } else {
            跳转到第8步();
        }
    }
}
```

## 🎛️ 用户设置管理系统

### 前端勾选框配置
```javascript
// 前端勾选框配置（只控制第3、5、6、7步）
var 前端勾选框配置 = {
    可控制步骤: [
        {
            步骤: 3,
            名称: "帐号登陆",
            键名: "启用帐号登陆",
            描述: "执行帐号登陆和注册处理",
            默认状态: true
        },
        {
            步骤: 5,
            名称: "首次教程",
            键名: "启用首次教程",
            描述: "执行9步完整教程流程",
            默认状态: true
        },
        {
            步骤: 6,
            名称: "检查分数",
            键名: "启用检查分数",
            描述: "执行分数确认和结束教程",
            默认状态: true
        },
        {
            步骤: 7,
            名称: "教程提现",
            键名: "启用教程提现",
            描述: "执行首次提现教程",
            默认状态: false // 预留功能默认关闭
        }
    ]
};
```

### 用户设置管理模块
```javascript
var 用户设置管理 = {
    设置文件路径: "/storage/emulated/0/magic/data/用户设置.json",

    // 默认设置
    默认设置: {
        启用帐号登陆: true,
        启用首次教程: true,
        启用检查分数: true,
        启用教程提现: false,
        设置保存时间: null
    },

    // 读取用户设置
    读取设置: function() {
        try {
            var 文件内容 = files.read(this.设置文件路径);
            if (文件内容) {
                var 设置 = JSON.parse(文件内容);
                return Object.assign({}, this.默认设置, 设置);
            }
        } catch (e) {
            console.log("读取用户设置失败，使用默认设置");
        }
        return this.默认设置;
    },

    // 保存用户设置
    保存设置: function(设置) {
        try {
            设置.设置保存时间 = new Date().toISOString();
            files.ensureDir("/storage/emulated/0/magic/data/");
            files.write(this.设置文件路径, JSON.stringify(设置, null, 2));
            console.log("✅ 用户设置已保存");
        } catch (e) {
            console.error("❌ 保存用户设置失败:", e);
        }
    },

    // 更新单个设置
    更新设置: function(键名, 值) {
        var 设置 = this.读取设置();
        设置[键名] = 值;
        this.保存设置(设置);
        console.log("🎛️ 已更新设置: " + 键名 + " = " + 值);
    },

    // 检查步骤是否启用
    步骤已启用: function(键名) {
        var 设置 = this.读取设置();
        return 设置[键名] === true;
    }
};
```

### 用户设置文件示例
```json
{
  "启用帐号登陆": false,
  "启用首次教程": false,
  "启用检查分数": true,
  "启用教程提现": false,
  "设置保存时间": "2023-12-25T10:30:00.000Z"
}
```

## 🏗️ 模块设计

### 1. 主脚本.js（核心控制器）
```javascript
function 游戏_程序() {
    try {
        console.log("🚀 启动Magic游戏辅助程序");

        // 读取用户设置
        var 用户设置 = 用户设置管理.读取设置();
        console.log("📖 已读取用户前端设置");

        // 注意：屏幕捕获权限已在主脚本开头全局获取

        // 第1步：游戏启动检查（必执行）
        var 当前包名 = currentPackage();
        if (当前包名 !== "com.winrgames.bigtime") {
            var 登陆成功 = 登陆模块.登陆_游戏("com.winrgames.bigtime", "BigTime", 5000, 200);
        }

        // 第2步：随机等待时间（必执行）
        var 随机等待时间 = random(4000, 6000);
        console.log("⏳ 随机等待 " + 随机等待时间 + " 毫秒");
        sleep(随机等待时间);

        // 第3步：帐号登陆（前端勾选框控制）
        if (用户设置管理.步骤已启用("启用帐号登陆")) {
            console.log("📋 第3步: 执行帐号登陆");
            var 帐号登陆成功 = 帐号模块.登陆_帐号();
        } else {
            console.log("⏭️ 第3步: 跳过帐号登陆（用户未勾选）");
        }

        // 第4步：随机等待时间（必执行）
        var 随机等待时间2 = random(500, 2500);
        console.log("⏳ 随机等待 " + 随机等待时间2 + " 毫秒");
        sleep(随机等待时间2);

        // 第5步：首次教程（前端勾选框控制）
        if (用户设置管理.步骤已启用("启用首次教程")) {
            console.log("📋 第5步: 执行首次教程");
            var 首次教程成功 = 首次教程模块.首次_教程();
        } else {
            console.log("⏭️ 第5步: 跳过首次教程（用户未勾选）");
        }

        // 第6步：检查分数（前端勾选框控制）
        if (用户设置管理.步骤已启用("启用检查分数")) {
            console.log("📋 第6步: 执行检查分数");
            var 检查分数成功 = 检查模块.检查_分数();
        } else {
            console.log("⏭️ 第6步: 跳过检查分数（用户未勾选）");
        }
        
        // 第7步：教程提现（前端勾选框控制）
        if (用户设置管理.步骤已启用("启用教程提现")) {
            console.log("📋 第7步: 执行教程提现");
            var 教程提现成功 = 教程提现模块.教程_提现();
        } else {
            console.log("⏭️ 第7步: 跳过教程提现（用户未勾选）");
        }
        
        // 第8步：检查登陆状态
        检查_登陆();
        
        // 第9步：每日领奖
        每日领奖();
        
        // 核心游戏循环
        var 游戏统计 = {
            总局数: 0,
            总分数: 0,
            连续无涨分局数: 0
        };
        
        while (true) {
            // 第10步：游玩裂球
            var 游戏结果 = 游玩_裂球();
            游戏统计.总局数++;

            // 第11步：观看广告（游戏后立即进入）
            var 广告结果 = 观看_广告();

            // 第12步：检查涨分（广告后出现）
            var 涨分结果 = 检查_涨分(游戏统计);

            // 更新统计数据
            更新游戏数据(游戏统计, 涨分结果);

            // 判断逻辑（所有情况都跳回第8步）
            if (涨分结果.需要重启) {
                // 条件：2局游戏没涨分20分
                console.log("🔄 检测到需要重启应用");
                重启应用并跳转();
                break; // 跳出循环，重启后重新从第8步开始
            } else {
                // 无论是否涨分，都跳回第8步防止掉线和检查每日领奖
                console.log("🔄 游戏循环完成，跳转到第8步检查登陆状态和每日领奖");
                break; // 跳出循环，重新从第8步开始
            }
        }
        
        // 注意：第13步每日提现已集成在第8步检查_登陆中执行
        
    } catch (error) {
        console.error("❌ 游戏程序执行错误: " + error);
        return false;
    }
}
```

### 2. 检查登陆.js（第8步 + 第13步集成）
```javascript
function 检查_登陆() {
    // 原有功能：检查是否掉线
    var 登陆状态 = 检查登陆状态();
    if (!登陆状态) {
        重新登陆();
    }

    // 新增功能：集成每日提现检查
    检查并执行每日提现();

    return 登陆状态;
}

function 检查并执行每日提现() {
    var 当前时间 = new Date();
    var 当前小时 = 当前时间.getHours();

    // 检查是否在提现时间窗口内（12-15点）
    if (当前小时 >= 12 && 当前小时 < 15) {
        // 检查今日是否已提现
        if (!提现记录管理.今日已提现()) {
            console.log("🕐 检测到提现时间窗口，执行每日提现");
            var 提现成功 = 每日提现();
            if (提现成功) {
                提现记录管理.记录今日已提现();
            }
        } else {
            console.log("✅ 今日已完成提现");
        }
    } else {
        console.log("⏰ 当前不在提现时间窗口内（12-15点）");
    }
}
```

### 3. 每日领奖.js（第9步）
```javascript
function 每日领奖() {
    // 查找每日奖励界面
    // 点击领取奖励
    // 统计奖励数据
}
```

### 4. 游玩裂球.js（第10步）
```javascript
function 游玩_裂球() {
    return {
        // 游戏核心逻辑
        统计局数: function() {},
        自动躲避: function() {},
        按键控制: function() {
            // 左键右键控制
        },
        计算障碍物: function() {
            // 原点与障碍物距离计算
        },
        游戏时间统计: function() {},
        
        // 返回游戏结果
        局数: 1,
        得分: 分数,
        游戏时长: 时长
    };
}
```

### 5. 观看广告.js（第11步）
```javascript
function 观看_广告() {
    // 游戏结束后立即进入广告
    // 自动观看广告
    // 处理广告完成
    return {
        观看成功: true/false,
        广告时长: 时长
    };
}
```

### 6. 检查涨分.js（第12步）
```javascript
function 检查_涨分(游戏统计) {
    return {
        // 功能1：统计总分与局分
        统计分数: function() {
            // 计算总分变化
            // 计算局分变化
        },
        
        // 功能2：判断是否需要重启
        判断重启: function() {
            // 如果玩2局游戏没涨分20则重启
            if (游戏统计.连续无涨分局数 >= 2 && 涨分 < 20) {
                return true;
            }
            return false;
        },
        
        当前总分: 分数,
        本局涨分: 涨分,
        需要重启: true/false
    };
}
```

### 7. 每日提现.js（第13步 - 集成在第8步中）
```javascript
// 提现记录管理模块
var 提现记录管理 = {
    文件路径: "/storage/emulated/0/magic/data/提现记录.json",

    // 检查今日是否已提现
    今日已提现: function() {
        try {
            var 今日 = new Date().toDateString();
            var 记录 = this.读取记录();
            return 记录[今日] === true;
        } catch (e) {
            return false;
        }
    },

    // 记录今日已提现
    记录今日已提现: function() {
        try {
            var 今日 = new Date().toDateString();
            var 记录 = this.读取记录();
            记录[今日] = true;
            this.保存记录(记录);
            console.log("✅ 已记录今日提现状态");
        } catch (e) {
            console.error("❌ 记录提现状态失败:", e);
        }
    },

    // 读取记录文件
    读取记录: function() {
        try {
            var 文件内容 = files.read(this.文件路径);
            return 文件内容 ? JSON.parse(文件内容) : {};
        } catch (e) {
            return {};
        }
    },

    // 保存记录文件
    保存记录: function(记录) {
        try {
            files.ensureDir("/storage/emulated/0/magic/data/");
            files.write(this.文件路径, JSON.stringify(记录, null, 2));
        } catch (e) {
            console.error("保存记录失败:", e);
        }
    }
};

function 每日提现() {
    try {
        console.log("💰 开始执行每日提现");

        // 提现收益计算
        var 当前收益 = 计算当前收益();

        // 汇率换算
        var 提现金额 = 汇率换算(当前收益);

        // 执行提现操作
        var 提现结果 = 执行提现操作(提现金额);

        if (提现结果.成功) {
            console.log("✅ 每日提现成功，金额: " + 提现金额);
            return true;
        } else {
            console.log("❌ 每日提现失败: " + 提现结果.错误信息);
            return false;
        }
    } catch (e) {
        console.error("❌ 每日提现过程中发生错误:", e);
        return false;
    }
}

function 计算当前收益() {
    // 计算当前可提现收益
    // 返回收益数值
}

function 汇率换算(收益) {
    // 根据当前汇率换算提现金额
    // 返回换算后的金额
}

function 执行提现操作(金额) {
    // 执行具体的提现操作
    // 返回操作结果
}
```

## 🔄 重启应用机制

### 重启流程设计
```javascript
function 重启应用并跳转() {
    // 1. 保存当前游戏数据
    保存游戏数据();
    
    // 2. 关闭当前应用
    关闭应用();
    
    // 3. 等待并重新启动
    sleep(3000);
    启动应用();
    
    // 4. 跳转到第8步继续执行
    检查_登陆();
    每日领奖();
    // 继续游戏循环...
}
```

## 📊 数据统计系统

### 游戏数据结构
```javascript
var 游戏数据 = {
    // 基础统计
    总局数: 0,
    总分数: 0,
    总游戏时长: 0,
    
    // 涨分统计
    连续无涨分局数: 0,
    最高单局得分: 0,
    平均单局得分: 0,
    
    // 时间统计
    今日游戏时长: 0,
    今日局数: 0,
    
    // 收益统计
    今日收益: 0,
    总收益: 0
};
```

## 🎨 前端勾选框UI设计

### 前端勾选框界面
```javascript
// 前端勾选框UI设计
var 前端勾选框UI = {
    // 创建设置界面
    创建设置界面: function() {
        var 设置界面 = {
            标题: "Magic游戏辅助 - 功能设置",
            布局: "vertical",
            控件: [
                {
                    类型: "text",
                    内容: "选择要执行的功能步骤：",
                    样式: "标题"
                },

                // 第3步勾选框
                {
                    类型: "checkbox",
                    id: "checkbox_帐号登陆",
                    文本: "第3步：帐号登陆",
                    描述: "执行帐号登陆和注册处理",
                    默认值: true
                },

                // 第5步勾选框
                {
                    类型: "checkbox",
                    id: "checkbox_首次教程",
                    文本: "第5步：首次教程",
                    描述: "执行9步完整教程流程",
                    默认值: true
                },

                // 第6步勾选框
                {
                    类型: "checkbox",
                    id: "checkbox_检查分数",
                    文本: "第6步：检查分数",
                    描述: "执行分数确认和结束教程",
                    默认值: true
                },

                // 第7步勾选框
                {
                    类型: "checkbox",
                    id: "checkbox_教程提现",
                    文本: "第7步：教程提现",
                    描述: "执行首次提现教程（预留功能）",
                    默认值: false
                },

                // 按钮组
                {
                    类型: "horizontal",
                    控件: [
                        {
                            类型: "button",
                            id: "btn_保存设置",
                            文本: "保存设置"
                        },
                        {
                            类型: "button",
                            id: "btn_重置设置",
                            文本: "重置设置"
                        },
                        {
                            类型: "button",
                            id: "btn_开始程序",
                            文本: "开始程序"
                        }
                    ]
                }
            ]
        };

        return 设置界面;
    },

    // 绑定事件处理
    绑定事件: function() {
        // 保存设置按钮
        ui.btn_保存设置.on("click", function() {
            var 新设置 = {
                启用帐号登陆: ui.checkbox_帐号登陆.checked,
                启用首次教程: ui.checkbox_首次教程.checked,
                启用检查分数: ui.checkbox_检查分数.checked,
                启用教程提现: ui.checkbox_教程提现.checked
            };

            用户设置管理.保存设置(新设置);
            toast("✅ 设置已保存");
        });

        // 重置设置按钮
        ui.btn_重置设置.on("click", function() {
            ui.checkbox_帐号登陆.checked = true;
            ui.checkbox_首次教程.checked = true;
            ui.checkbox_检查分数.checked = true;
            ui.checkbox_教程提现.checked = false;
            toast("🔄 设置已重置");
        });

        // 开始程序按钮
        ui.btn_开始程序.on("click", function() {
            // 先保存当前设置
            this.保存当前设置();

            // 启动游戏程序
            游戏_程序();
        });
    },

    // 加载用户设置到界面
    加载用户设置: function() {
        var 设置 = 用户设置管理.读取设置();
        ui.checkbox_帐号登陆.checked = 设置.启用帐号登陆;
        ui.checkbox_首次教程.checked = 设置.启用首次教程;
        ui.checkbox_检查分数.checked = 设置.启用检查分数;
        ui.checkbox_教程提现.checked = 设置.启用教程提现;
    }
};
```

### 执行流程图
```
程序启动
    ↓
显示前端勾选框界面
    ↓
用户选择要执行的步骤（第3、5、6、7步）
    ↓
保存用户设置到文件
    ↓
开始执行游戏_程序()
    ↓
第1-2步：必执行（游戏启动、随机等待）
    ↓
第3步：帐号登陆（勾选框控制）
    ↓
第4步：必执行（随机等待）
    ↓
第5步：首次教程（勾选框控制）
    ↓
第6步：检查分数（勾选框控制）
    ↓
第7步：教程提现（勾选框控制）
    ↓
第8-12步：游戏循环（必执行）
```

## 🎨 前端UI集成点

### 数据卡片显示内容
- 实时游戏局数
- 当前总分数
- 今日涨分情况
- 游戏时长统计
- 收益统计

### UI更新时机
- 每局游戏结束后
- 涨分检查完成后
- 提现操作完成后

## ⚡ 性能优化策略

### 1. 内存管理
- 及时释放图片资源
- 定期清理日志
- 控制变量作用域

### 2. 执行效率
- 减少不必要的截图操作
- 优化图片查找算法
- 合理设置重试次数

### 3. 稳定性保证
- 完善的异常处理
- 自动重启机制
- 状态恢复功能

## 🚀 开发计划

### 第一阶段（立即开发）
- [ ] 第8步：检查_登陆.js
- [ ] 第9步：每日领奖.js
- [ ] 第10步：游玩_裂球.js

### 第二阶段（核心功能）
- [ ] 第11步：观看_广告.js
- [ ] 第12步：检查_涨分.js
- [ ] 重启应用机制

### 第三阶段（完善功能）
- [ ] 第13步：每日提现功能（集成在第8步中）
- [ ] 提现记录管理系统
- [ ] 数据统计系统
- [ ] 前端UI集成

## 📝 注意事项（重构版）

1. **前端控制**：第3、5、6、7步通过前端勾选框控制执行
2. **权限管理**：屏幕捕获权限在程序启动时全局获取，不重复申请
3. **用户设置**：用户选择自动保存到文件，程序启动时自动读取
4. **线性执行**：第8-12步游戏循环必须按顺序执行，不能并行
5. **状态保持**：重启应用后需要恢复游戏状态
6. **数据持久化**：用户设置、提现记录等重要数据保存到文件
7. **错误恢复**：每个步骤都要有容错机制
8. **性能监控**：监控内存使用和执行时间

## 🎯 重构版特点

### ✅ **智能控制**
- 用户可以灵活选择执行哪些步骤
- 避免重复执行不需要的功能
- 提高程序执行效率

### ✅ **用户友好**
- 简洁的前端勾选框界面
- 自动保存和读取用户设置
- 清晰的执行流程提示

### ✅ **架构优化**
- 移除了权限重复申请
- 优化了步骤编号和执行逻辑
- 保持了游戏循环的稳定性

---

**最后更新时间**: 2025年1月
**文档版本**: v2.0 (重构版)
**基于技术**: AutoXjs ozobiozobi v6.5.8.17
**新增特性**: 智能前端控制 + 用户设置管理
