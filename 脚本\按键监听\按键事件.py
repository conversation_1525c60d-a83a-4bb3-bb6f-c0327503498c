# 电脑键盘按键监听功能 - A键和D键监听
# 用于雷电模拟器的自动化程序调试

import keyboard
from datetime import datetime
import sys
import os

class 按键监听器:
    def __init__(self):
        self.运行状态 = False
        self.按键统计 = {
            'A键': {'完整周期次数': 0, '当前状态': False, '按下时间': None, '持续时间': []},
            'D键': {'完整周期次数': 0, '当前状态': False, '按下时间': None, '持续时间': []},
            'AD组合键': {'完整周期次数': 0, '当前状态': False, '按下时间': None, '持续时间': []}
        }
        self.调试模式 = True
        self.日志输出 = True
        self.按键锁 = {'A键': False, 'D键': False, 'AD组合键': False}  # 防止重复触发

        # 日志文件配置
        self.日志文件路径 = "操作日志.log"
        self.初始化日志文件()

    def 初始化日志文件(self):
        """初始化日志文件"""
        try:
            启动时间 = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            with open(self.日志文件路径, 'w', encoding='utf-8') as f:
                f.write(f"=== 按键监听日志 - 启动时间: {启动时间} ===\n")
                f.write("监听目标: A键、D键和AD组合键\n")
                f.write("功能: 按下和弹起事件监听\n")
                f.write("组合键: 同时按下A和D键触发组合键功能\n")
                f.write("日志格式: [时间戳] 事件类型 - 详细信息\n")
                f.write("=" * 60 + "\n\n")

            if self.调试模式:
                print(f"📝 日志文件已初始化: {self.日志文件路径}")
        except Exception as e:
            print(f"⚠️ 日志文件初始化失败: {e}")

    def 写入日志(self, 消息, 事件类型="INFO"):
        """
        写入日志到文件和控制台

        Args:
            消息 (str): 要记录的消息
            事件类型 (str): 事件类型 (INFO, KEY_EVENT, STATS, ERROR)
        """
        try:
            时间戳 = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            日志条目 = f"[{时间戳}] {事件类型} - {消息}"

            # 输出到控制台
            print(消息)

            # 写入到日志文件
            with open(self.日志文件路径, 'a', encoding='utf-8') as f:
                f.write(日志条目 + "\n")
                f.flush()  # 立即刷新到文件
        except Exception as e:
            print(f"⚠️ 日志写入失败: {e}")
            print(消息)  # 至少保证控制台输出

    def 检查_组合键状态(self):
        """检查是否同时按下A和D键"""
        return self.按键统计['A键']['当前状态'] and self.按键统计['D键']['当前状态']

    def 处理_AD组合键按下(self):
        """处理AD组合键按下事件"""
        if not self.按键锁['AD组合键'] and not self.按键统计['AD组合键']['当前状态']:
            self.按键锁['AD组合键'] = True
            self.按键统计['AD组合键']['当前状态'] = True
            self.按键统计['AD组合键']['按下时间'] = datetime.now()

            if self.调试模式:
                组合键信息 = "🔥 检测到AD组合键按下！"
                self.写入日志(组合键信息, "COMBO_KEY")

    def 处理_AD组合键弹起(self):
        """处理AD组合键弹起事件"""
        if self.按键锁['AD组合键'] and self.按键统计['AD组合键']['当前状态']:
            弹起时间 = datetime.now()
            按下时间 = self.按键统计['AD组合键']['按下时间']

            if 按下时间:
                # 计算持续时间
                持续时间 = (弹起时间 - 按下时间).total_seconds()
                self.按键统计['AD组合键']['持续时间'].append(持续时间)

                # 完整周期计数
                self.按键统计['AD组合键']['完整周期次数'] += 1

                # 输出完整周期信息
                时间戳 = 弹起时间.strftime("%H:%M:%S.%f")[:-3]

                if self.日志输出:
                    操作信息 = f"🔥 AD组合键完整操作 - 第{self.按键统计['AD组合键']['完整周期次数']}次 - {时间戳} - 持续{持续时间:.3f}秒"
                    self.写入日志(操作信息, "COMBO_KEY")

                # 您的AD组合键完整操作逻辑
                自动化信息 = "🚀 执行AD组合键完整操作的自动化逻辑"
                self.写入日志(自动化信息, "COMBO_ACTION")

            # 重置所有状态
            self.按键统计['AD组合键']['当前状态'] = False
            self.按键统计['AD组合键']['按下时间'] = None
            self.按键锁['AD组合键'] = False

    def 处理_A键按下(self):
        """A键按下事件处理 - 静默记录，不干扰输入"""
        # 使用锁机制防止重复触发
        if not self.按键锁['A键'] and not self.按键统计['A键']['当前状态']:
            self.按键锁['A键'] = True
            self.按键统计['A键']['当前状态'] = True
            self.按键统计['A键']['按下时间'] = datetime.now()

            # 检查是否形成AD组合键
            if self.检查_组合键状态():
                self.处理_AD组合键按下()

            # 静默记录，不输出任何信息，避免干扰键盘输入

    def 处理_A键弹起(self):
        """A键弹起事件处理 - 记录完整周期并输出"""
        # 只有在真正按下状态时才处理弹起
        if self.按键锁['A键'] and self.按键统计['A键']['当前状态']:
            弹起时间 = datetime.now()
            按下时间 = self.按键统计['A键']['按下时间']

            if 按下时间:
                # 计算持续时间
                持续时间 = (弹起时间 - 按下时间).total_seconds()
                self.按键统计['A键']['持续时间'].append(持续时间)

                # 完整周期计数
                self.按键统计['A键']['完整周期次数'] += 1

                # 输出完整周期信息
                时间戳 = 弹起时间.strftime("%H:%M:%S.%f")[:-3]

                if self.日志输出:
                    操作信息 = f"✅ A键完整操作 - 第{self.按键统计['A键']['完整周期次数']}次 - {时间戳} - 持续{持续时间:.3f}秒"
                    self.写入日志(操作信息, "KEY_EVENT")

                # 您的A键完整操作逻辑
                自动化信息 = "🎮 执行A键完整操作的自动化逻辑"
                self.写入日志(自动化信息, "ACTION")

            # 检查组合键是否需要结束
            if self.按键统计['AD组合键']['当前状态'] and not self.检查_组合键状态():
                self.处理_AD组合键弹起()

            # 重置所有状态
            self.按键统计['A键']['当前状态'] = False
            self.按键统计['A键']['按下时间'] = None
            self.按键锁['A键'] = False

    def 处理_D键按下(self):
        """D键按下事件处理 - 静默记录，不干扰输入"""
        # 使用锁机制防止重复触发
        if not self.按键锁['D键'] and not self.按键统计['D键']['当前状态']:
            self.按键锁['D键'] = True
            self.按键统计['D键']['当前状态'] = True
            self.按键统计['D键']['按下时间'] = datetime.now()

            # 检查是否形成AD组合键
            if self.检查_组合键状态():
                self.处理_AD组合键按下()

            # 静默记录，不输出任何信息，避免干扰键盘输入

    def 处理_D键弹起(self):
        """D键弹起事件处理 - 记录完整周期并输出"""
        # 只有在真正按下状态时才处理弹起
        if self.按键锁['D键'] and self.按键统计['D键']['当前状态']:
            弹起时间 = datetime.now()
            按下时间 = self.按键统计['D键']['按下时间']

            if 按下时间:
                # 计算持续时间
                持续时间 = (弹起时间 - 按下时间).total_seconds()
                self.按键统计['D键']['持续时间'].append(持续时间)

                # 完整周期计数
                self.按键统计['D键']['完整周期次数'] += 1

                # 输出完整周期信息
                时间戳 = 弹起时间.strftime("%H:%M:%S.%f")[:-3]

                if self.日志输出:
                    操作信息 = f"✅ D键完整操作 - 第{self.按键统计['D键']['完整周期次数']}次 - {时间戳} - 持续{持续时间:.3f}秒"
                    self.写入日志(操作信息, "KEY_EVENT")

                # 您的D键完整操作逻辑
                自动化信息 = "🎮 执行D键完整操作的自动化逻辑"
                self.写入日志(自动化信息, "ACTION")

            # 检查组合键是否需要结束
            if self.按键统计['AD组合键']['当前状态'] and not self.检查_组合键状态():
                self.处理_AD组合键弹起()

            # 重置所有状态
            self.按键统计['D键']['当前状态'] = False
            self.按键统计['D键']['按下时间'] = None
            self.按键锁['D键'] = False

    def 显示统计信息(self):
        """显示按键统计信息"""
        统计信息 = []
        统计信息.append("--- 📊 按键统计 ---")

        for 按键名, 数据 in self.按键统计.items():
            状态 = "按下中" if 数据['当前状态'] else "未按下"
            完整次数 = 数据['完整周期次数']

            # 计算平均持续时间
            if 数据['持续时间']:
                平均持续时间 = sum(数据['持续时间']) / len(数据['持续时间'])
                最短时间 = min(数据['持续时间'])
                最长时间 = max(数据['持续时间'])
                统计信息.append(f"{按键名}: 完整操作{完整次数}次, 当前状态: {状态}")
                统计信息.append(f"  ⏱️ 平均持续: {平均持续时间:.3f}秒, 最短: {最短时间:.3f}秒, 最长: {最长时间:.3f}秒")
            else:
                统计信息.append(f"{按键名}: 完整操作{完整次数}次, 当前状态: {状态}")

        统计信息.append("--- 统计结束 ---")

        # 输出到控制台
        for 行 in 统计信息:
            print(行)

        # 写入到日志文件
        完整统计 = "\n".join(统计信息)
        self.写入日志(完整统计, "STATS")
        print()  # 额外的空行

    # 已移除定时统计功能，只在退出时显示最终统计

    def 监听_按键(self):
        """
        启动按键监听功能

        Returns:
            bool: 启动是否成功
        """
        try:
            启动信息 = [
                "=== 🎮 电脑键盘按键监听启动 ===",
                "监听目标: A键、D键和AD组合键",
                "功能: 按下和弹起事件监听",
                "组合键: 同时按下A和D键触发组合键功能",
                "按 Ctrl+C 或 ESC 键退出程序",
                "=" * 40
            ]

            for 信息 in 启动信息:
                print(信息)

            # 记录启动信息到日志
            self.写入日志("按键监听系统启动", "SYSTEM")

            self.运行状态 = True

            # 注册A键事件
            keyboard.on_press_key('a', lambda _: self.处理_A键按下())
            keyboard.on_release_key('a', lambda _: self.处理_A键弹起())

            # 注册D键事件
            keyboard.on_press_key('d', lambda _: self.处理_D键按下())
            keyboard.on_release_key('d', lambda _: self.处理_D键弹起())

            # 注册ESC键退出
            keyboard.on_press_key('esc', lambda _: self.停止监听())

            # 不再启动定时统计线程，只在退出时显示统计

            成功信息 = [
                "✅ 按键监听启动成功！",
                "现在可以按A键、D键或同时按AD键进行测试",
                "🔥 同时按下A和D键可触发组合键功能",
                "按ESC键退出程序并查看最终统计",
                f"📝 日志文件: {self.日志文件路径}"
            ]

            for 信息 in 成功信息:
                print(信息)

            # 记录启动成功到日志
            self.写入日志("按键监听启动成功，开始监听A键、D键和AD组合键", "SYSTEM")

            # 保持程序运行
            keyboard.wait('esc')

            return True

        except Exception as e:
            print(f"❌ 按键监听启动失败: {e}")
            return False
        finally:
            self.停止监听()

    def 停止监听(self):
        """停止按键监听"""
        self.运行状态 = False

        停止信息 = "=== 正在停止按键监听 ==="
        print(f"\n{停止信息}")
        self.写入日志("用户请求停止按键监听", "SYSTEM")

        # 显示最终统计
        self.显示统计信息()

        # 清除所有键盘钩子
        keyboard.unhook_all()

        结束信息 = [
            "按键监听已停止",
            f"📝 完整日志已保存到: {self.日志文件路径}",
            "=== 程序已退出 ==="
        ]

        for 信息 in 结束信息:
            print(信息)

        # 记录程序结束
        self.写入日志("按键监听程序正常退出", "SYSTEM")

        # 退出程序
        sys.exit(0)

def main():
    """主函数"""
    try:
        # 创建按键监听器实例
        监听器 = 按键监听器()

        # 启动监听（不显示定时统计，只在退出时显示）
        监听器.监听_按键()

    except KeyboardInterrupt:
        print("\n检测到 Ctrl+C，正在退出...")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()