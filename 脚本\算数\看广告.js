/**
 * 看广告颜色检测模块 v2.1.0
 * 功能：基于OpenCV轮廓检测和背景颜色分析的广告识别
 * 特性：多目录模板支持、背景颜色检测、详细颜色分析
 *
 * 核心技术：
 * - 背景颜色分析 (HSV): 自动分析背景特征和颜色类型
 * - 详细颜色识别: 识别黑白灰、红橙黄绿青蓝紫等颜色
 * - 多目录模板支持: 支持白色模板和黑色模板目录
 * - 轮廓检测 (findContours): 查找图像轮廓
 * - 形状匹配 (matchShapes): 轮廓相似度比较
 * - 最小外接矩形 (minAreaRect): 获取轮廓边界框
 * - 颜色调试可视化: 显示详细颜色信息和检测建议
 *
 * 版本：v2.1.0 - 颜色检测版
 * 更新：2025-01-07
 * 作者：Magic项目组
 */

// 初始化OpenCV
runtime.images.initOpenCvIfNeeded();

// 导入OpenCV相关类
importClass(org.opencv.core.Mat);
importClass(org.opencv.core.MatOfPoint);
importClass(org.opencv.core.MatOfPoint2f);
importClass(org.opencv.core.Scalar);
importClass(org.opencv.core.Point);
importClass(org.opencv.core.Size);
importClass(org.opencv.core.Rect);
importClass(org.opencv.core.RotatedRect);
importClass(org.opencv.core.CvType);  // 添加CvType支持
importClass(org.opencv.imgproc.Imgproc);
importClass(org.opencv.imgcodecs.Imgcodecs);
importClass(org.opencv.core.Core);
importClass(java.util.ArrayList);
importClass(java.util.List);

// 定义检测区域配置 - 支持多目录智能检测
var 检测区域配置 = {
    close区域: {
        区域: [24, 866, 243, 80],  // x, y, width, height
        目录列表: ["../../assets/算数游戏/广告/close区域"],
        名称: "close区域"
    },
    右广告: {
        区域: [425, 2, 110, 176],
        目录列表: [
            "../../assets/算数游戏/广告/右广告",      // 白色模板
            "../../assets/算数游戏/广告/右广告_黑色"   // 黑色模板
        ],
        名称: "右广告"
    },
    左广告: {
        区域: [3, 4, 124, 151],
        目录列表: ["../../assets/算数游戏/广告/左广告"],
        名称: "左广告"
    }
};



// 相似度阈值 (越小越相似，0.03为高精度匹配)
var 相似度阈值 = 0.25;

/**
 * 背景颜色分析函数
 * 分析区域图像的背景特征，并提供背景转黑色功能
 * @param {Mat} 区域图像 - 输入的区域图像
 * @param {Boolean} 转换背景为黑色 - 是否将背景色转换为黑色，默认false
 * @param {String} 转换模式 - "颜色检测"或"轮廓保护"，默认"颜色检测"
 * @param {Array} 区域坐标 - 区域在全屏中的坐标偏移 [x, y]，用于全屏坐标转换
 * @returns {Object} - 背景分析结果和处理后的图像
 */
function 分析背景特征(区域图像, 转换背景为黑色, 转换模式, 区域坐标) {
    try {
        // 分析主要色调（转换为HSV）
        var hsv图像 = new Mat();
        Imgproc.cvtColor(区域图像, hsv图像, Imgproc.COLOR_BGR2HSV);
        var hsv平均值 = Core.mean(hsv图像);

        // 分析颜色特征
        var 颜色分析 = 分析颜色特征(hsv平均值.val[0], hsv平均值.val[1], hsv平均值.val[2]);

        // 计算RGB颜色值和十六进制表示
        var rgb颜色 = hsv转rgb(hsv平均值.val[0], hsv平均值.val[1], hsv平均值.val[2]);
        var 十六进制颜色 = rgb转十六进制(rgb颜色.r, rgb颜色.g, rgb颜色.b);

        // 输出详细的颜色检测信息 - 已注释
        // console.log("🎨 详细颜色分析:");
        // console.log("  🌈 HSV色调: " + Math.round(hsv平均值.val[0]) + "° (0-180)");
        // console.log("  💧 HSV饱和度: " + Math.round(hsv平均值.val[1]) + " (0-255)");
        // console.log("  ☀️ HSV明度: " + Math.round(hsv平均值.val[2]) + " (0-255)");
        // console.log("  🎨 RGB颜色值: R" + rgb颜色.r + " G" + rgb颜色.g + " B" + rgb颜色.b);
        // console.log("  🔢 十六进制颜色: " + 十六进制颜色);
        // console.log("  🎯 颜色类型: " + 颜色分析.颜色名称);
        // console.log("  🔍 检测建议: " + 颜色分析.检测建议);

        var 处理后图像 = null;

        // 将检测到的背景色转换为黑色，保留前景内容
        if (转换背景为黑色 === true) {
            // console.log("🔄 将背景色(" + 颜色分析.颜色名称 + ")转换为黑色...");
            处理后图像 = 区域图像.clone();

            // 根据转换模式选择不同策略
            转换模式 = 转换模式 || "颜色检测";

            if (转换模式 === "轮廓保护") {
                // 轮廓保护模式：先检测轮廓，然后保护轮廓区域
                console.log("  📝 使用轮廓保护模式");
                // 传递区域坐标给轮廓保护转换函数，用于全屏坐标转换
                var 轮廓保护结果 = 轮廓保护转换(区域图像, 区域坐标);
                处理后图像 = 轮廓保护结果.处理图像;
                var 轮廓信息 = 轮廓保护结果.轮廓信息;
                var 边缘轮廓信息 = 轮廓保护结果.边缘轮廓信息 || [];  // 获取边缘检测轮廓信息
            } else {
                // 颜色检测模式：基于颜色相似度检测背景
                console.log("  📝 使用颜色检测模式");

                // 创建背景色掩码
                var 背景掩码 = new Mat();

            // 根据检测到的背景色创建HSV范围
            var 色调 = Math.round(hsv平均值.val[0]);
            var 饱和度 = Math.round(hsv平均值.val[1]);
            var 明度 = Math.round(hsv平均值.val[2]);

            // 优化的背景色检测策略
            if (颜色分析.颜色类型 === "暗色" || 颜色分析.颜色类型 === "亮色") {
                // 黑白灰色系：使用明度范围检测
                console.log("  📝 使用明度范围检测黑白灰背景");
                var 明度下限 = Math.max(0, 明度 - 60);
                var 明度上限 = Math.min(255, 明度 + 60);

                var 下限 = new Scalar(0, 0, 明度下限);
                var 上限 = new Scalar(180, 50, 明度上限);  // 低饱和度

                Core.inRange(hsv图像, 下限, 上限, 背景掩码);
            } else {
                // 彩色背景：使用多层次检测
                console.log("  📝 使用多层次检测彩色背景");

                // 第一层：精确匹配（小容差）
                var 精确下限 = new Scalar(
                    Math.max(0, 色调 - 15),
                    Math.max(0, 饱和度 - 40),
                    Math.max(0, 明度 - 40)
                );
                var 精确上限 = new Scalar(
                    Math.min(180, 色调 + 15),
                    Math.min(255, 饱和度 + 40),
                    Math.min(255, 明度 + 40)
                );

                // 第二层：宽松匹配（大容差）
                var 宽松下限 = new Scalar(
                    Math.max(0, 色调 - 30),
                    Math.max(0, 饱和度 - 80),
                    Math.max(0, 明度 - 80)
                );
                var 宽松上限 = new Scalar(
                    Math.min(180, 色调 + 30),
                    Math.min(255, 饱和度 + 80),
                    Math.min(255, 明度 + 80)
                );

                // 创建两个掩码并合并
                var 精确掩码 = new Mat();
                var 宽松掩码 = new Mat();

                Core.inRange(hsv图像, 精确下限, 精确上限, 精确掩码);
                Core.inRange(hsv图像, 宽松下限, 宽松上限, 宽松掩码);

                // 合并掩码（或运算）
                Core.bitwise_or(精确掩码, 宽松掩码, 背景掩码);

                // 释放临时掩码
                精确掩码.release();
                宽松掩码.release();
            }

                // 🎨 智能前景轮廓增强：将暗色轮廓反转为白色
                console.log("  🎨 开始前景轮廓颜色增强（颜色检测模式）...");

                // 创建前景掩码（非背景区域）
                var 前景掩码 = new Mat();
                Core.bitwise_not(背景掩码, 前景掩码);

                // 对前景区域进行颜色增强
                var 前景增强图像 = 增强前景轮廓颜色(处理后图像, 前景掩码);

                // 将背景色区域设置为黑色，前景使用增强后的颜色
                前景增强图像.setTo(new Scalar(0, 0, 0), 背景掩码);

                // 更新处理图像
                处理后图像.release();
                处理后图像 = 前景增强图像;

                // 释放资源
                背景掩码.release();
                前景掩码.release();
            }

            console.log("✅ 背景色已转换为黑色，前景内容已保留");
        }

        // 释放临时资源
        hsv图像.release();

        return {
            色调: Math.round(hsv平均值.val[0]),
            饱和度: Math.round(hsv平均值.val[1]),
            明度: Math.round(hsv平均值.val[2]),
            颜色名称: 颜色分析.颜色名称,
            颜色类型: 颜色分析.颜色类型,
            检测建议: 颜色分析.检测建议,
            处理后图像: 处理后图像,  // 新增：处理后的图像
            轮廓信息: (转换模式 === "轮廓保护" && typeof 轮廓信息 !== 'undefined') ? 轮廓信息 : null,  // 新增：轮廓信息
            边缘轮廓信息: (转换模式 === "轮廓保护" && typeof 边缘轮廓信息 !== 'undefined') ? 边缘轮廓信息 : []  // 新增：边缘轮廓信息
        };
    } catch (e) {
        console.error("背景特征分析失败: " + e);
        return {
            色调: 0,
            饱和度: 0,
            明度: 128,
            颜色名称: "分析失败",
            颜色类型: "未知",
            检测建议: "使用默认检测方法",
            处理后图像: null
        };
    }
}

/**
 * HSV转RGB颜色空间转换
 * @param {Number} h - 色调 (0-180)
 * @param {Number} s - 饱和度 (0-255)
 * @param {Number} v - 明度 (0-255)
 * @returns {Object} - RGB颜色值 {r, g, b}
 */
function hsv转rgb(h, s, v) {
    try {
        // 将HSV值转换为标准范围
        h = h * 2;        // OpenCV的H范围是0-180，标准HSV是0-360
        s = s / 255.0;    // 转换为0-1范围
        v = v / 255.0;    // 转换为0-1范围

        var c = v * s;    // 色度
        var x = c * (1 - Math.abs(((h / 60) % 2) - 1));
        var m = v - c;

        var r, g, b;

        if (h >= 0 && h < 60) {
            r = c; g = x; b = 0;
        } else if (h >= 60 && h < 120) {
            r = x; g = c; b = 0;
        } else if (h >= 120 && h < 180) {
            r = 0; g = c; b = x;
        } else if (h >= 180 && h < 240) {
            r = 0; g = x; b = c;
        } else if (h >= 240 && h < 300) {
            r = x; g = 0; b = c;
        } else {
            r = c; g = 0; b = x;
        }

        // 转换为0-255范围
        return {
            r: Math.round((r + m) * 255),
            g: Math.round((g + m) * 255),
            b: Math.round((b + m) * 255)
        };
    } catch (e) {
        console.error("HSV转RGB失败: " + e);
        return {r: 128, g: 128, b: 128};  // 返回灰色作为默认值
    }
}

/**
 * RGB转十六进制颜色值
 * @param {Number} r - 红色分量 (0-255)
 * @param {Number} g - 绿色分量 (0-255)
 * @param {Number} b - 蓝色分量 (0-255)
 * @returns {String} - 十六进制颜色值 (如: #FFFFFF)
 */
function rgb转十六进制(r, g, b) {
    try {
        // 确保RGB值在0-255范围内
        r = Math.max(0, Math.min(255, Math.round(r)));
        g = Math.max(0, Math.min(255, Math.round(g)));
        b = Math.max(0, Math.min(255, Math.round(b)));

        // 转换为十六进制
        var 十六进制R = r.toString(16).padStart(2, '0').toUpperCase();
        var 十六进制G = g.toString(16).padStart(2, '0').toUpperCase();
        var 十六进制B = b.toString(16).padStart(2, '0').toUpperCase();

        return "#" + 十六进制R + 十六进制G + 十六进制B;
    } catch (e) {
        console.error("RGB转十六进制失败: " + e);
        return "#808080";  // 返回灰色作为默认值
    }
}

/**
 * 增强前景轮廓颜色
 * 将暗色轮廓（如#3C3C3C）增强为白色，确保在黑色背景上可见
 * @param {Mat} 输入图像 - 输入的图像
 * @param {Mat} 前景掩码 - 前景区域掩码
 * @returns {Mat} - 颜色增强后的图像
 */
function 增强前景轮廓颜色(输入图像, 前景掩码) {
    try {
        var 增强图像 = 输入图像.clone();

        // 🎨 创建#3C3C3C颜色保护掩码
        var 目标颜色掩码 = 创建前景色保护掩码(输入图像);

        // 将前景掩码与目标颜色掩码结合
        var 需要增强掩码 = new Mat();
        Core.bitwise_and(前景掩码, 目标颜色掩码, 需要增强掩码);

        // 统计需要增强的像素数量
        var 需要增强像素数 = Core.countNonZero(需要增强掩码);
        console.log("    🎨 检测到需要增强的前景像素：" + 需要增强像素数 + " 个");

        if (需要增强像素数 > 0) {
            // 🔄 将暗色前景轮廓反转为白色
            if (输入图像.channels() === 3) {
                // 彩色图像：将目标颜色区域设置为白色
                增强图像.setTo(new Scalar(255, 255, 255), 需要增强掩码);
                console.log("    ✅ 已将 " + 需要增强像素数 + " 个暗色像素增强为白色");
            } else {
                // 灰度图像：将目标区域设置为白色
                增强图像.setTo(new Scalar(255), 需要增强掩码);
                console.log("    ✅ 已将 " + 需要增强像素数 + " 个暗色像素增强为白色（灰度）");
            }
        } else {
            console.log("    ℹ️ 未检测到需要增强的暗色前景像素");
        }

        // 🌟 额外处理：对其他前景区域进行适度增强
        var 其他前景掩码 = new Mat();
        Core.bitwise_and(前景掩码, 目标颜色掩码, 其他前景掩码);  // 前景但非目标颜色
        Core.bitwise_not(其他前景掩码, 其他前景掩码);  // 反转
        Core.bitwise_and(前景掩码, 其他前景掩码, 其他前景掩码);  // 与前景掩码相交

        var 其他前景像素数 = Core.countNonZero(其他前景掩码);
        if (其他前景像素数 > 0) {
            // 对其他前景区域进行适度亮度增强
            var 增强区域 = new Mat();
            增强图像.copyTo(增强区域, 其他前景掩码);

            var 亮度增强图像 = new Mat();
            增强区域.convertTo(亮度增强图像, -1, 1.5, 50);  // 增强亮度和对比度

            亮度增强图像.copyTo(增强图像, 其他前景掩码);

            增强区域.release();
            亮度增强图像.release();

            console.log("    ✨ 已对 " + 其他前景像素数 + " 个其他前景像素进行亮度增强");
        }

        // 💾 保存前景颜色增强处理图
        try {
            var 时间戳 = new Date().getTime();
            var 保存路径 = "/storage/emulated/0/Pictures/右广告_前景颜色增强_" + 时间戳 + "_处理图.jpg";
            Imgcodecs.imwrite(保存路径, 增强图像);
            console.log("    💾 前景颜色增强处理图已保存: " + 保存路径);
        } catch (saveError) {
            console.error("    ❌ 保存前景颜色增强处理图失败: " + saveError);
        }

        // 释放资源
        目标颜色掩码.release();
        需要增强掩码.release();
        其他前景掩码.release();

        return 增强图像;

    } catch (e) {
        console.error("    ❌ 前景轮廓颜色增强失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 轮廓保护转换函数
 * 先检测轮廓，保护轮廓区域，然后将其他区域转换为黑色
 * @param {Mat} 原图像 - 输入的原始图像
 * @param {Array} 区域坐标 - 区域在全屏中的坐标偏移 [x, y]，用于全屏坐标转换
 * @returns {Mat} - 处理后的图像
 */
function 轮廓保护转换(原图像, 区域坐标) {
    try {
        var 处理图像 = 原图像.clone();

        // 1. 转换为灰度图进行轮廓检测
        var 灰度图 = new Mat();
        Imgproc.cvtColor(原图像, 灰度图, Imgproc.COLOR_BGR2GRAY);

        // 2. 高斯模糊减少噪声
        var 模糊图 = new Mat();
        Imgproc.GaussianBlur(灰度图, 模糊图, new Size(3, 3), 0);

        // 3. 🔍 低阈值边缘检测 - 专门检测低对比度轮廓（如#3C3C3C）
        var 边缘图 = new Mat();
        console.log("  🔍 使用低阈值Canny边缘检测，适应暗色轮廓...");
        Imgproc.Canny(模糊图, 边缘图, 20, 60);  // 降低阈值：50→20, 150→60

        // 🔍 在边缘检测后立即检测轮廓坐标信息
        console.log("  🔍 开始检测边缘图中的轮廓坐标...");
        var 边缘轮廓列表 = new ArrayList();
        var 边缘层次结构 = new Mat();
        Imgproc.findContours(边缘图, 边缘轮廓列表, 边缘层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        console.log("  📊 边缘检测发现 " + 边缘轮廓列表.size() + " 个轮廓");

        // 🎯 收集边缘检测的轮廓信息，用于后续推荐点击坐标
        var 边缘轮廓信息列表 = [];

        if (边缘轮廓列表.size() > 0) {
            console.log("  📍 边缘检测全屏轮廓坐标信息:");

            for (var i = 0; i < 边缘轮廓列表.size(); i++) {
                var 边缘轮廓 = 边缘轮廓列表.get(i);
                var 边缘面积 = Imgproc.contourArea(边缘轮廓);

                // 计算轮廓的外接矩形（相对于检测区域）
                var 边缘外接矩形 = Imgproc.boundingRect(边缘轮廓);
                var 区域内中心X = Math.round(边缘外接矩形.x + 边缘外接矩形.width / 2);
                var 区域内中心Y = Math.round(边缘外接矩形.y + 边缘外接矩形.height / 2);

                // 🌍 转换为全屏坐标
                var 全屏中心X, 全屏中心Y;
                if (区域坐标 && 区域坐标.length >= 2) {
                    全屏中心X = 区域坐标[0] + 区域内中心X;
                    全屏中心Y = 区域坐标[1] + 区域内中心Y;
                } else {
                    // 如果没有区域坐标信息，使用区域内坐标
                    全屏中心X = 区域内中心X;
                    全屏中心Y = 区域内中心Y;
                }

                // 🎯 收集边缘轮廓信息
                边缘轮廓信息列表.push({
                    索引: "边缘" + (i + 1),
                    中心点: {
                        x: 区域内中心X,
                        y: 区域内中心Y
                    },
                    全屏坐标: {
                        x: 全屏中心X,
                        y: 全屏中心Y
                    },
                    尺寸: {
                        宽度: 边缘外接矩形.width,
                        高度: 边缘外接矩形.height
                    },
                    面积: Math.round(边缘面积),
                    形状: "边缘检测轮廓",
                    来源: "边缘检测"
                });

                console.log("    - 边缘轮廓" + (i + 1) + ": (" + 全屏中心X + ", " + 全屏中心Y + ") 尺寸: " +
                           边缘外接矩形.width + "×" + 边缘外接矩形.height + " 面积: " + Math.round(边缘面积) +
                           " [区域内坐标: (" + 区域内中心X + ", " + 区域内中心Y + ")]");
            }
        } else {
            console.log("  ⚠️ 边缘检测未发现任何轮廓");
        }

        // 释放边缘检测的临时资源
        边缘层次结构.release();
        for (var j = 0; j < 边缘轮廓列表.size(); j++) {
            边缘轮廓列表.get(j).release();
        }

        // 💾 保存边缘检测处理图
        try {
            var 时间戳 = new Date().getTime();
            var 边缘保存路径 = "/storage/emulated/0/Pictures/右广告_边缘检测_" + 时间戳 + "_处理图.jpg";
            Imgcodecs.imwrite(边缘保存路径, 边缘图);
            console.log("  💾 边缘检测处理图已保存: " + 边缘保存路径);

            // 💾 同时保存原始灰度图，便于对比
            var 灰度保存路径 = "/storage/emulated/0/Pictures/右广告_原始灰度_" + 时间戳 + "_处理图.jpg";
            Imgcodecs.imwrite(灰度保存路径, 灰度图);
            console.log("  💾 原始灰度处理图已保存: " + 灰度保存路径);
        } catch (saveError) {
            console.error("  ❌ 保存边缘检测处理图失败: " + saveError);
        }

        // 4. 形态学操作连接断开的边缘
        var 连接图 = new Mat();
        var 核心 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(3, 3));
        Imgproc.morphologyEx(边缘图, 连接图, Imgproc.MORPH_CLOSE, 核心);

        // 5. 膨胀操作扩大保护区域
        var 保护掩码 = new Mat();
        var 膨胀核心 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(5, 5));
        Imgproc.dilate(连接图, 保护掩码, 膨胀核心);

        // 6. 反转掩码：保护区域为0，背景区域为255
        var 背景掩码 = new Mat();
        Core.bitwise_not(保护掩码, 背景掩码);

        // 7. 🎨 智能前景轮廓增强：将暗色轮廓反转为白色，保证在黑色背景上可见
        console.log("  🎨 开始前景轮廓颜色增强...");

        // 创建前景轮廓掩码（保护区域）
        var 前景掩码 = new Mat();
        Core.bitwise_not(背景掩码, 前景掩码);  // 前景区域为255，背景为0

        // 对前景区域进行颜色分析和增强
        var 前景增强图像 = 增强前景轮廓颜色(处理图像, 前景掩码);

        // 8. 将背景区域设置为黑色，前景使用增强后的颜色
        前景增强图像.setTo(new Scalar(0, 0, 0), 背景掩码);

        // 更新处理图像
        处理图像.release();
        处理图像 = 前景增强图像;

        // 释放临时掩码
        前景掩码.release();

        // 8. 检测并输出保护的轮廓信息（包含颜色分析）
        var 轮廓信息 = 检测保护轮廓信息(保护掩码, 原图像);
        输出轮廓保护信息(轮廓信息);

        // 9. 🎨 杂乱背景优化：专门处理背景杂乱导致的检测不准问题
        var 杂乱优化结果 = 杂乱背景优化处理(处理图像, 原图像, 轮廓信息);
        if (杂乱优化结果.处理图像) {
            处理图像.release();  // 释放原处理图像
            处理图像 = 杂乱优化结果.处理图像;  // 使用优化后的图像
            轮廓信息 = 杂乱优化结果.轮廓信息;  // 更新轮廓信息
            console.log("  ✨ 杂乱背景优化处理完成");
        }

        // 9. 释放临时资源
        灰度图.release();
        模糊图.release();
        边缘图.release();
        连接图.release();
        核心.release();
        保护掩码.release();
        膨胀核心.release();
        背景掩码.release();

        // console.log("  🛡️ 轮廓保护转换完成");

        // 10. 返回处理图像和轮廓信息（包含边缘检测轮廓）
        return {
            处理图像: 处理图像,
            轮廓信息: 轮廓信息,
            边缘轮廓信息: 边缘轮廓信息列表  // 新增：边缘检测的轮廓信息
        };
    } catch (e) {
        console.error("轮廓保护转换失败: " + e);
        return 原图像.clone();
    }
}

/**
 * 检测保护轮廓信息
 * 分析保护掩码中的轮廓，获取中心点、尺寸信息和颜色特征
 * @param {Mat} 保护掩码 - 轮廓保护掩码
 * @param {Mat} 原图像 - 原始彩色图像，用于颜色分析
 * @returns {Array} - 轮廓信息数组
 */
function 检测保护轮廓信息(保护掩码, 原图像) {
    try {
        var 轮廓信息列表 = [];

        // 查找保护掩码中的轮廓
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();

        Imgproc.findContours(保护掩码, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        // console.log("  📊 检测到 " + 轮廓列表.size() + " 个保护轮廓");

        // 分析每个轮廓
        for (var i = 0; i < 轮廓列表.size(); i++) {
            var 轮廓 = 轮廓列表.get(i);

            // 计算轮廓面积
            var 面积 = Imgproc.contourArea(轮廓);

            // 🛡️ 保留所有轮廓，不进行面积过滤
            // 注释掉面积过滤，保留所有检测到的轮廓
            // if (面积 < 10) {
            //     continue;
            // }

            // 转换轮廓为MatOfPoint2f类型
            var 轮廓数组 = 轮廓.toArray();
            var 轮廓2f = new MatOfPoint2f();
            轮廓2f.fromArray(轮廓数组);

            // 计算最小外接矩形
            var 外接矩形 = Imgproc.minAreaRect(轮廓2f);
            var 中心点 = 外接矩形.center;
            var 尺寸 = 外接矩形.size;

            // 计算轮廓周长（使用MatOfPoint2f）
            var 周长 = Imgproc.arcLength(轮廓2f, true);

            // 释放临时资源
            轮廓2f.release();

            // 🎨 检测轮廓区域的颜色特征
            var 轮廓颜色信息 = null;
            if (原图像) {
                轮廓颜色信息 = 分析轮廓颜色(原图像, 轮廓, 中心点, 尺寸);
            }

            // 🎯 生成轮廓形状描述
            var 轮廓信息对象 = {
                索引: i + 1,
                中心点: {
                    x: Math.round(中心点.x),
                    y: Math.round(中心点.y)
                },
                尺寸: {
                    宽度: Math.round(尺寸.width),
                    高度: Math.round(尺寸.height)
                },
                面积: Math.round(面积),
                周长: Math.round(周长),
                角度: Math.round(外接矩形.angle),
                形状比例: Math.round((尺寸.width / Math.max(尺寸.height, 1)) * 100) / 100,
                颜色信息: 轮廓颜色信息  // 新增：轮廓颜色信息
            };

            // 🎨 生成形状描述，用于目标轮廓识别
            轮廓信息对象.形状 = 分析轮廓形状(轮廓信息对象);

            // 存储轮廓信息
            轮廓信息列表.push(轮廓信息对象);
        }

        // 释放资源
        层次结构.release();
        for (var j = 0; j < 轮廓列表.size(); j++) {
            轮廓列表.get(j).release();
        }

        return 轮廓信息列表;
    } catch (e) {
        console.error("检测保护轮廓信息失败: " + e);
        return [];
    }
}

/**
 * 输出轮廓保护信息
 * 格式化输出轮廓的详细信息
 * @param {Array} 轮廓信息列表 - 轮廓信息数组
 */
function 输出轮廓保护信息(轮廓信息列表) {
    try {
        if (轮廓信息列表.length === 0) {
            // console.log("  ⚠️ 未检测到有效的保护轮廓");
            return;
        }

        // 轮廓详细信息输出已注释 - 减少日志输出
        /*
        console.log("  🎯 保护轮廓详细信息:");

        for (var i = 0; i < 轮廓信息列表.length; i++) {
            var 轮廓 = 轮廓信息列表[i];

            console.log("    轮廓 " + 轮廓.索引 + ":");
            console.log("      📍 中心点: (" + 轮廓.中心点.x + ", " + 轮廓.中心点.y + ")");
            console.log("      📏 尺寸: " + 轮廓.尺寸.宽度 + "×" + 轮廓.尺寸.高度);
            console.log("      📐 面积: " + 轮廓.面积 + " 像素");
            console.log("      📏 周长: " + 轮廓.周长 + " 像素");
            console.log("      🔄 角度: " + 轮廓.角度 + "°");
            console.log("      📊 宽高比: " + 轮廓.形状比例);

            // 🎨 输出颜色信息
            if (轮廓.颜色信息) {
                var 颜色 = 轮廓.颜色信息;
                console.log("      🎨 颜色分析:");
                console.log("        🌈 HSV: H" + 颜色.hsv.色调 + "° S" + 颜色.hsv.饱和度 + " V" + 颜色.hsv.明度);
                console.log("        🎨 RGB: R" + 颜色.rgb.r + " G" + 颜色.rgb.g + " B" + 颜色.rgb.b);
                console.log("        🔢 十六进制: " + 颜色.十六进制);
                console.log("        🏷️ 颜色名称: " + 颜色.颜色名称);
                console.log("        📊 颜色类型: " + 颜色.颜色类型);
                console.log("        🎯 置信度: " + 颜色.置信度 + "%");

                // 根据置信度给出颜色可靠性提示
                if (颜色.置信度 >= 80) {
                    console.log("        ✅ 颜色识别: 高可信度");
                } else if (颜色.置信度 >= 60) {
                    console.log("        ⚠️ 颜色识别: 中等可信度");
                } else {
                    console.log("        ❌ 颜色识别: 低可信度，可能受光照影响");
                }
            }

            // 根据形状特征给出分析
            var 形状分析 = 分析轮廓形状(轮廓);
            console.log("      🔍 形状分析: " + 形状分析);
        }

        // 输出统计信息
        var 总面积 = 轮廓信息列表.reduce(function(sum, 轮廓) { return sum + 轮廓.面积; }, 0);
        var 平均面积 = Math.round(总面积 / 轮廓信息列表.length);

        console.log("  📈 统计信息:");
        console.log("    🔢 保护轮廓总数: " + 轮廓信息列表.length);
        console.log("    📐 总保护面积: " + 总面积 + " 像素");
        console.log("    📊 平均轮廓面积: " + 平均面积 + " 像素");
        */

    } catch (e) {
        console.error("输出轮廓保护信息失败: " + e);
    }
}

/**
 * 分析轮廓形状特征
 * @param {Object} 轮廓信息 - 轮廓信息对象
 * @returns {String} - 形状分析结果
 */
function 分析轮廓形状(轮廓信息) {
    try {
        var 宽高比 = 轮廓信息.形状比例;
        var 面积 = 轮廓信息.面积;

        var 形状描述 = "";

        // 🎯 智能形状识别 - 根据宽高比和面积判断可能的轮廓类型
        if (Math.abs(宽高比 - 1.0) < 0.3) {
            // 近似正方形 - 可能是播放号、X号等
            if (面积 < 200) {
                形状描述 += "播放号";  // 小正方形很可能是播放号
            } else if (面积 < 800) {
                形状描述 += "X号";     // 中等正方形可能是X号
            } else {
                形状描述 += "近似正方形";
            }
        } else if (宽高比 > 2.5) {
            // 横向长条形 - 可能是>>号、箭头等
            形状描述 += ">>号";
        } else if (宽高比 < 0.4) {
            // 纵向长条形 - 可能是竖线、边框等
            if (面积 < 500) {
                形状描述 += "纵向长条形";
            } else {
                形状描述 += "纵向长条形";
            }
        } else if (宽高比 > 1.5) {
            // 横向矩形 - 可能是>>号
            形状描述 += ">>号";
        } else if (宽高比 < 0.7) {
            // 纵向矩形 - 可能是播放号
            形状描述 += "播放号";
        } else {
            // 一般矩形 - 根据面积判断
            if (面积 < 300) {
                形状描述 += "播放号";  // 小矩形可能是播放号
            } else {
                形状描述 += "矩形";
            }
        }

        // 根据面积判断大小
        if (面积 < 100) {
            形状描述 += "，小型轮廓";
        } else if (面积 < 500) {
            形状描述 += "，中型轮廓";
        } else {
            形状描述 += "，大型轮廓";
        }

        // 根据角度判断方向
        if (Math.abs(轮廓信息.角度) < 15) {
            形状描述 += "，水平方向";
        } else if (Math.abs(轮廓信息.角度 - 90) < 15 || Math.abs(轮廓信息.角度 + 90) < 15) {
            形状描述 += "，垂直方向";
        } else {
            形状描述 += "，倾斜 " + 轮廓信息.角度 + "°";
        }

        return 形状描述;
    } catch (e) {
        return "分析失败";
    }
}

/**
 * 分析轮廓颜色
 * 分析轮廓区域内的主要颜色特征
 * @param {Mat} 原图像 - 原始彩色图像
 * @param {MatOfPoint} 轮廓 - 轮廓对象
 * @param {Point} 中心点 - 轮廓中心点
 * @param {Size} 尺寸 - 轮廓尺寸
 * @returns {Object} - 颜色分析结果
 */
function 分析轮廓颜色(原图像, 轮廓, 中心点, 尺寸) {
    try {
        // 创建轮廓区域的采样矩形
        var 采样半径 = Math.min(尺寸.width, 尺寸.height) / 4;  // 使用轮廓尺寸的1/4作为采样半径
        采样半径 = Math.max(3, Math.min(采样半径, 10));  // 限制在3-10像素范围内

        var 采样区域 = new Rect(
            Math.max(0, Math.round(中心点.x - 采样半径)),
            Math.max(0, Math.round(中心点.y - 采样半径)),
            Math.round(采样半径 * 2),
            Math.round(采样半径 * 2)
        );

        // 确保采样区域不超出图像边界
        采样区域.x = Math.min(采样区域.x, 原图像.cols() - 采样区域.width);
        采样区域.y = Math.min(采样区域.y, 原图像.rows() - 采样区域.height);

        // 提取采样区域
        var 采样图像 = new Mat(原图像, 采样区域);

        // 转换为HSV色彩空间
        var hsv图像 = new Mat();
        Imgproc.cvtColor(采样图像, hsv图像, Imgproc.COLOR_BGR2HSV);

        // 计算平均HSV值
        var hsv平均值 = Core.mean(hsv图像);

        // 计算RGB值
        var rgb颜色 = hsv转rgb(hsv平均值.val[0], hsv平均值.val[1], hsv平均值.val[2]);
        var 十六进制颜色 = rgb转十六进制(rgb颜色.r, rgb颜色.g, rgb颜色.b);

        // 分析颜色特征
        var 颜色特征 = 分析颜色特征(hsv平均值.val[0], hsv平均值.val[1], hsv平均值.val[2]);

        // 释放资源
        采样图像.release();
        hsv图像.release();

        return {
            hsv: {
                色调: Math.round(hsv平均值.val[0]),
                饱和度: Math.round(hsv平均值.val[1]),
                明度: Math.round(hsv平均值.val[2])
            },
            rgb: rgb颜色,
            十六进制: 十六进制颜色,
            颜色名称: 颜色特征.颜色名称,
            颜色类型: 颜色特征.颜色类型,
            采样区域: {
                x: 采样区域.x,
                y: 采样区域.y,
                宽度: 采样区域.width,
                高度: 采样区域.height
            },
            置信度: 计算颜色置信度(hsv平均值.val[1], hsv平均值.val[2])
        };

    } catch (e) {
        console.error("分析轮廓颜色失败: " + e);
        return {
            hsv: { 色调: 0, 饱和度: 0, 明度: 128 },
            rgb: { r: 128, g: 128, b: 128 },
            十六进制: "#808080",
            颜色名称: "分析失败",
            颜色类型: "未知",
            采样区域: null,
            置信度: 0
        };
    }
}

/**
 * 计算颜色置信度
 * 根据饱和度和明度计算颜色识别的置信度
 * @param {Number} 饱和度 - HSV饱和度 (0-255)
 * @param {Number} 明度 - HSV明度 (0-255)
 * @returns {Number} - 置信度 (0-100)
 */
function 计算颜色置信度(饱和度, 明度) {
    try {
        var 置信度 = 0;

        // 饱和度贡献（饱和度越高，颜色越明确）
        if (饱和度 > 150) {
            置信度 += 50;  // 高饱和度
        } else if (饱和度 > 80) {
            置信度 += 30;  // 中饱和度
        } else if (饱和度 > 30) {
            置信度 += 15;  // 低饱和度
        }

        // 明度贡献（避免过暗或过亮）
        if (明度 > 50 && 明度 < 200) {
            置信度 += 30;  // 适中明度
        } else if (明度 > 30 && 明度 < 220) {
            置信度 += 20;  // 可接受明度
        } else {
            置信度 += 10;  // 极端明度
        }

        // 综合评估
        if (饱和度 > 100 && 明度 > 60 && 明度 < 180) {
            置信度 += 20;  // 理想条件奖励
        }

        return Math.min(100, 置信度);
    } catch (e) {
        return 0;
    }
}

/**
 * 轮廓颜色增强处理
 * 将保护轮廓中的暗色系、冷色系转换为白色系，增强对比度
 * @param {Mat} 处理图像 - 已处理的图像（背景为黑色）
 * @param {Mat} 保护掩码 - 轮廓保护掩码
 * @param {Array} 轮廓信息 - 轮廓信息数组
 * @param {Mat} 原图像 - 原始彩色图像
 * @returns {Mat} - 颜色增强后的图像
 */
function 轮廓颜色增强处理(处理图像, 保护掩码, 轮廓信息, 原图像) {
    try {
        if (!轮廓信息 || 轮廓信息.length === 0) {
            console.log("  ⚠️ 无轮廓信息，跳过颜色增强处理");
            return null;
        }

        var 增强图像 = 处理图像.clone();
        var 需要增强 = false;

        console.log("  🎨 开始轮廓颜色增强分析...");

        // 遍历每个轮廓，检查是否需要颜色增强
        for (var i = 0; i < 轮廓信息.length; i++) {
            var 轮廓 = 轮廓信息[i];

            if (!轮廓.颜色信息) {
                continue;
            }

            var 颜色 = 轮廓.颜色信息;
            var 需要转换 = 判断是否需要颜色转换(颜色);

            if (需要转换.需要转换) {
                console.log("    🔄 轮廓 " + 轮廓.索引 + " 需要颜色转换: " + 颜色.颜色名称 + " → 白色系");
                console.log("      📝 转换原因: " + 需要转换.原因);

                // 执行颜色转换
                执行轮廓颜色转换(增强图像, 轮廓, 颜色, 原图像);
                需要增强 = true;
            } else {
                console.log("    ✅ 轮廓 " + 轮廓.索引 + " 无需转换: " + 颜色.颜色名称);
            }
        }

        if (需要增强) {
            console.log("  ✨ 已完成 " + 轮廓信息.length + " 个轮廓的颜色增强处理");
            return 增强图像;
        } else {
            console.log("  ℹ️ 所有轮廓颜色均无需转换");
            增强图像.release();
            return null;
        }

    } catch (e) {
        console.error("轮廓颜色增强处理失败: " + e);
        return null;
    }
}

/**
 * 判断是否需要颜色转换
 * 检查轮廓颜色是否为暗色系或冷色系，需要转换为白色系
 * @param {Object} 颜色信息 - 轮廓颜色信息
 * @returns {Object} - 转换判断结果
 */
function 判断是否需要颜色转换(颜色信息) {
    try {
        var hsv = 颜色信息.hsv;
        var 色调 = hsv.色调;
        var 饱和度 = hsv.饱和度;
        var 明度 = hsv.明度;

        var 需要转换 = false;
        var 原因 = "";

        // 1. 检查是否为暗色系（明度过低）
        if (明度 < 100) {
            需要转换 = true;
            原因 += "暗色系(明度" + 明度 + "<100) ";
        }

        // 2. 检查是否为冷色系（蓝色、青色、紫色系）
        if (饱和度 > 50) {  // 只对有一定饱和度的颜色进行色调判断
            if ((色调 >= 100 && 色调 <= 140) ||  // 青色系
                (色调 >= 140 && 色调 <= 170) ||  // 蓝色系
                (色调 >= 170 && 色调 <= 180)) {  // 紫色系
                需要转换 = true;
                原因 += "冷色系(色调" + 色调 + "°) ";
            }
        }

        // 3. 检查是否为深灰色系
        if (饱和度 < 30 && 明度 < 120) {
            需要转换 = true;
            原因 += "深灰色系(饱和度" + 饱和度 + "<30且明度" + 明度 + "<120) ";
        }

        // 4. 特殊情况：黑色系
        if (明度 < 50) {
            需要转换 = true;
            原因 += "黑色系(明度" + 明度 + "<50) ";
        }

        return {
            需要转换: 需要转换,
            原因: 原因.trim() || "无需转换"
        };

    } catch (e) {
        console.error("判断颜色转换失败: " + e);
        return { 需要转换: false, 原因: "判断失败" };
    }
}

/**
 * 执行轮廓颜色转换
 * 将指定轮廓区域的颜色转换为白色系
 * @param {Mat} 增强图像 - 要处理的图像
 * @param {Object} 轮廓信息 - 轮廓信息
 * @param {Object} 颜色信息 - 颜色信息
 * @param {Mat} 原图像 - 原始图像
 */
function 执行轮廓颜色转换(增强图像, 轮廓信息, 颜色信息, 原图像) {
    try {
        // 创建轮廓区域矩形
        var 轮廓矩形 = 创建轮廓区域掩码(轮廓信息, 增强图像.size());

        if (!轮廓矩形) {
            console.log("      ❌ 创建轮廓区域失败");
            return;
        }

        // 根据原始颜色选择转换策略
        var 目标颜色 = 选择目标白色(颜色信息);

        // 获取轮廓区域的子图像并设置为目标白色
        var 轮廓子图像 = new Mat(增强图像, 轮廓矩形);
        轮廓子图像.setTo(目标颜色);

        console.log("      ✅ 已转换为: " + 目标颜色.val[2] + "," + 目标颜色.val[1] + "," + 目标颜色.val[0] + " (BGR)");
        console.log("      📍 转换区域: (" + 轮廓矩形.x + "," + 轮廓矩形.y + ") " + 轮廓矩形.width + "×" + 轮廓矩形.height);

        // 释放资源
        轮廓子图像.release();

    } catch (e) {
        console.error("执行轮廓颜色转换失败: " + e);
    }
}

/**
 * 创建轮廓区域掩码
 * 根据轮廓信息创建对应的区域掩码
 * @param {Object} 轮廓信息 - 轮廓信息
 * @param {Size} 图像尺寸 - 图像尺寸
 * @returns {Mat} - 轮廓区域掩码
 */
function 创建轮廓区域掩码(轮廓信息, 图像尺寸) {
    try {
        // 简化方法：直接返回轮廓区域的Rect，不使用复杂的掩码
        var 中心 = 轮廓信息.中心点;
        var 尺寸 = 轮廓信息.尺寸;

        // 计算矩形区域
        var 半宽 = Math.round(尺寸.宽度 / 2);
        var 半高 = Math.round(尺寸.高度 / 2);

        var 矩形区域 = new Rect(
            Math.max(0, 中心.x - 半宽),
            Math.max(0, 中心.y - 半高),
            Math.min(尺寸.宽度, 图像尺寸.width - Math.max(0, 中心.x - 半宽)),
            Math.min(尺寸.高度, 图像尺寸.height - Math.max(0, 中心.y - 半高))
        );

        return 矩形区域;  // 直接返回矩形区域

    } catch (e) {
        console.error("创建轮廓区域掩码失败: " + e);
        return null;
    }
}

/**
 * 选择目标白色
 * 根据原始颜色特征选择合适的白色调
 * @param {Object} 颜色信息 - 原始颜色信息
 * @returns {Scalar} - 目标颜色值 (BGR格式)
 */
function 选择目标白色(颜色信息) {
    try {
        var hsv = 颜色信息.hsv;
        var 明度 = hsv.明度;

        // 根据原始明度选择不同的白色强度
        if (明度 < 50) {
            // 极暗色 → 纯白色
            return new Scalar(255, 255, 255);  // BGR: 纯白
        } else if (明度 < 100) {
            // 暗色 → 亮白色
            return new Scalar(240, 240, 240);  // BGR: 亮白
        } else {
            // 中等暗色 → 浅白色
            return new Scalar(220, 220, 220);  // BGR: 浅白
        }

    } catch (e) {
        console.error("选择目标白色失败: " + e);
        return new Scalar(255, 255, 255);  // 默认纯白
    }
}

/**
 * 黑色前景优化处理
 * 专门处理黑色X等在黑色背景上难以检测的对象
 * @param {Mat} 处理图像 - 当前处理的图像
 * @param {Mat} 原图像 - 原始彩色图像
 * @param {Array} 轮廓信息 - 轮廓信息数组
 * @returns {Object} - 优化结果 {处理图像, 轮廓信息}
 */
function 黑色前景优化处理(处理图像, 原图像, 轮廓信息) {
    try {
        console.log("  🖤 开始黑色前景优化分析...");

        // 检查是否有黑色前景需要优化
        var 需要优化 = false;
        var 黑色轮廓列表 = [];

        for (var i = 0; i < 轮廓信息.length; i++) {
            var 轮廓 = 轮廓信息[i];
            if (轮廓.颜色信息) {
                var 颜色 = 轮廓.颜色信息;

                // 检测黑色系前景
                if (是黑色系前景(颜色)) {
                    console.log("    🔍 发现黑色前景: 轮廓 " + 轮廓.索引 + " - " + 颜色.颜色名称);
                    黑色轮廓列表.push(轮廓);
                    需要优化 = true;
                }
            }
        }

        if (!需要优化) {
            console.log("  ℹ️ 无黑色前景，跳过优化");
            return { 处理图像: null, 轮廓信息: null };
        }

        console.log("  🎯 检测到 " + 黑色轮廓列表.length + " 个黑色前景，开始优化...");

        // 执行黑色前景优化
        var 优化图像 = 执行黑色前景优化(处理图像, 原图像, 黑色轮廓列表);

        if (优化图像) {
            console.log("  ✨ 黑色前景优化完成，重新检测轮廓...");

            // 重新检测优化后的轮廓
            var 新轮廓信息 = 重新检测轮廓信息(优化图像, 原图像);

            return {
                处理图像: 优化图像,
                轮廓信息: 新轮廓信息
            };
        }

        return { 处理图像: null, 轮廓信息: null };

    } catch (e) {
        console.error("黑色前景优化处理失败: " + e);
        return { 处理图像: null, 轮廓信息: null };
    }
}

/**
 * 判断是否为黑色系前景
 * @param {Object} 颜色信息 - 颜色信息对象
 * @returns {Boolean} - 是否为黑色系前景
 */
function 是黑色系前景(颜色信息) {
    try {
        var hsv = 颜色信息.hsv;
        var 明度 = hsv.明度;
        var 饱和度 = hsv.饱和度;

        // 黑色系判断条件
        if (明度 < 80) {  // 明度很低
            return true;
        }

        // 深灰色系
        if (饱和度 < 30 && 明度 < 100) {
            return true;
        }

        // 颜色名称包含黑色相关词汇
        if (颜色信息.颜色名称.indexOf("黑色") !== -1 ||
            颜色信息.颜色名称.indexOf("暗色") !== -1) {
            return true;
        }

        return false;
    } catch (e) {
        return false;
    }
}

/**
 * 执行黑色前景优化
 * 使用多种策略增强黑色前景的可见性
 * @param {Mat} 处理图像 - 当前图像
 * @param {Mat} 原图像 - 原始图像
 * @param {Array} 黑色轮廓列表 - 黑色轮廓列表
 * @returns {Mat} - 优化后的图像
 */
function 执行黑色前景优化(处理图像, 原图像, 黑色轮廓列表) {
    try {
        var 优化图像 = 处理图像.clone();

        console.log("  🔧 策略1: 反转优化 - 将黑色区域变为白色");

        // 策略1: 反转优化 - 针对黑色前景区域进行反转处理
        for (var i = 0; i < 黑色轮廓列表.length; i++) {
            var 轮廓 = 黑色轮廓列表[i];

            // 创建轮廓区域
            var 中心 = 轮廓.中心点;
            var 尺寸 = 轮廓.尺寸;

            // 扩大检测区域，确保包含完整的黑色前景
            var 扩展半径 = Math.max(尺寸.宽度, 尺寸.高度) / 2 + 5;

            var 优化区域 = new Rect(
                Math.max(0, 中心.x - 扩展半径),
                Math.max(0, 中心.y - 扩展半径),
                Math.min(扩展半径 * 2, 优化图像.cols() - Math.max(0, 中心.x - 扩展半径)),
                Math.min(扩展半径 * 2, 优化图像.rows() - Math.max(0, 中心.y - 扩展半径))
            );

            // 提取区域并进行反转处理
            var 区域图像 = new Mat(优化图像, 优化区域);
            var 反转图像 = new Mat();

            // 反转颜色：黑色变白色，白色变黑色
            Core.bitwise_not(区域图像, 反转图像);

            // 将反转结果复制回原图
            反转图像.copyTo(区域图像);

            console.log("    ✅ 轮廓 " + 轮廓.索引 + " 反转优化完成，区域: " +
                       优化区域.x + "," + 优化区域.y + " " + 优化区域.width + "×" + 优化区域.height);

            // 释放资源
            区域图像.release();
            反转图像.release();
        }

        console.log("  🎯 策略2: 对比度增强");

        // 策略2: 整体对比度增强
        var 增强图像 = new Mat();
        优化图像.convertTo(增强图像, -1, 1.5, 30);  // 增加对比度和亮度

        优化图像.release();
        return 增强图像;

    } catch (e) {
        console.error("执行黑色前景优化失败: " + e);
        return null;
    }
}

/**
 * 重新检测轮廓信息
 * 在优化后的图像上重新检测轮廓
 * @param {Mat} 优化图像 - 优化后的图像
 * @param {Mat} 原图像 - 原始图像
 * @returns {Array} - 新的轮廓信息
 */
function 重新检测轮廓信息(优化图像, 原图像) {
    try {
        console.log("  🔄 重新检测优化后的轮廓...");

        // 转换为灰度图
        var 灰度图 = new Mat();
        if (优化图像.channels() === 3) {
            Imgproc.cvtColor(优化图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 优化图像.clone();
        }

        // 边缘检测
        var 边缘图 = new Mat();
        Imgproc.Canny(灰度图, 边缘图, 50, 150);

        // 查找轮廓
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();

        Imgproc.findContours(边缘图, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        console.log("  📊 重新检测到 " + 轮廓列表.size() + " 个轮廓");

        // 生成新的轮廓信息
        var 新轮廓信息 = [];

        for (var i = 0; i < 轮廓列表.size(); i++) {
            var 轮廓 = 轮廓列表.get(i);
            var 面积 = Imgproc.contourArea(轮廓);

            if (面积 < 10) continue;  // 过滤小轮廓

            // 计算轮廓特征
            var 轮廓数组 = 轮廓.toArray();
            var 轮廓2f = new MatOfPoint2f();
            轮廓2f.fromArray(轮廓数组);

            var 外接矩形 = Imgproc.minAreaRect(轮廓2f);
            var 中心点 = 外接矩形.center;
            var 尺寸 = 外接矩形.size;

            新轮廓信息.push({
                索引: i + 1,
                中心点: {
                    x: Math.round(中心点.x),
                    y: Math.round(中心点.y)
                },
                尺寸: {
                    宽度: Math.round(尺寸.width),
                    高度: Math.round(尺寸.height)
                },
                面积: Math.round(面积),
                角度: Math.round(外接矩形.angle),
                形状比例: Math.round((尺寸.width / Math.max(尺寸.height, 1)) * 100) / 100,
                颜色信息: null,  // 优化后的轮廓暂不重新分析颜色
                优化类型: "黑色前景反转"
            });

            轮廓2f.release();
        }

        // 释放资源
        灰度图.release();
        边缘图.release();
        层次结构.release();
        for (var j = 0; j < 轮廓列表.size(); j++) {
            轮廓列表.get(j).release();
        }

        console.log("  ✅ 生成了 " + 新轮廓信息.length + " 个优化后的轮廓信息");

        return 新轮廓信息;

    } catch (e) {
        console.error("重新检测轮廓信息失败: " + e);
        return [];
    }
}

/**
 * 分析图像对比度特征
 * 检测是否为高对比度白色系图像
 * @param {Mat} 处理图像 - 输入图像
 * @returns {Object} - 图像特征分析结果
 */
function 分析图像对比度特征(处理图像) {
    try {
        // 转换为灰度图进行分析，确保格式正确
        var 灰度图 = new Mat();
        if (处理图像.channels() === 3) {
            Imgproc.cvtColor(处理图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else if (处理图像.channels() === 1) {
            // 确保单通道图像也是CV_8UC1格式
            if (处理图像.type() === CvType.CV_8UC1) {
                灰度图 = 处理图像.clone();
            } else {
                // 转换为CV_8UC1格式
                处理图像.convertTo(灰度图, CvType.CV_8UC1);
            }
        } else {
            // 其他格式，先转换为BGR再转灰度
            var 临时图像 = new Mat();
            Imgproc.cvtColor(处理图像, 临时图像, Imgproc.COLOR_RGBA2BGR);
            Imgproc.cvtColor(临时图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
            临时图像.release();
        }

        // 计算图像统计信息
        var 平均值 = Core.mean(灰度图);
        var 亮度均值 = 平均值.val[0];

        // 计算对比度（标准差）
        var 对比度 = 计算图像对比度(灰度图, 亮度均值);

        // 创建白色和黑色掩码进行精确的像素占比计算
        var 白色掩码 = new Mat();
        var 黑色掩码 = new Mat();

        // 白色阈值：亮度 > 200 认为是白色
        Imgproc.threshold(灰度图, 白色掩码, 200, 255, Imgproc.THRESH_BINARY);

        // 黑色阈值：亮度 < 50 认为是黑色
        Imgproc.threshold(灰度图, 黑色掩码, 50, 255, Imgproc.THRESH_BINARY_INV);

        // 计算实际的像素占比
        var 总像素数 = 灰度图.rows() * 灰度图.cols();
        var 白色像素数 = Core.countNonZero(白色掩码);
        var 黑色像素数 = Core.countNonZero(黑色掩码);

        var 白色占比 = 白色像素数 / 总像素数;
        var 黑色占比 = 黑色像素数 / 总像素数;

        // 判断是否为高对比度白色系
        var 是高对比度 = 对比度 > 80;  // 对比度高
        var 是白色系 = 白色占比 > 0.15;  // 白色占比超过15%
        var 是黑白分明 = (白色占比 + 黑色占比) > 0.6;  // 黑白像素占比超过60%

        var 是高对比度白色系 = 是高对比度 && 是白色系 && 是黑白分明;

        // 释放资源
        灰度图.release();
        白色掩码.release();
        黑色掩码.release();

        return {
            亮度均值: 亮度均值,
            对比度: 对比度,
            白色占比: 白色占比,
            黑色占比: 黑色占比,
            是高对比度: 是高对比度,
            是白色系: 是白色系,
            是黑白分明: 是黑白分明,
            是高对比度白色系: 是高对比度白色系
        };

    } catch (e) {
        console.error("分析图像对比度特征失败: " + e);

        // 安全释放资源（检查变量是否已创建）
        try {
            if (typeof 灰度图 !== 'undefined' && 灰度图) {
                灰度图.release();
            }
            if (typeof 白色掩码 !== 'undefined' && 白色掩码) {
                白色掩码.release();
            }
            if (typeof 黑色掩码 !== 'undefined' && 黑色掩码) {
                黑色掩码.release();
            }
        } catch (releaseError) {
            console.error("释放资源失败: " + releaseError);
        }

        return {
            亮度均值: 128,
            对比度: 0,
            白色占比: 0,
            黑色占比: 0,
            是高对比度: false,
            是白色系: false,
            是黑白分明: false,
            是高对比度白色系: false
        };
    }
}

/**
 * 计算图像对比度
 * @param {Mat} 灰度图 - 灰度图像
 * @param {Number} 亮度均值 - 亮度平均值
 * @returns {Number} - 对比度值
 */
function 计算图像对比度(灰度图, 亮度均值) {
    try {
        // 计算每个像素与均值的差值的平方
        var 差值图 = new Mat();
        var 均值标量 = new Scalar(亮度均值);

        Core.subtract(灰度图, 均值标量, 差值图);
        Core.multiply(差值图, 差值图, 差值图);

        // 计算方差
        var 方差 = Core.mean(差值图);
        var 标准差 = Math.sqrt(方差.val[0]);

        差值图.release();

        return 标准差;

    } catch (e) {
        console.error("计算图像对比度失败: " + e);
        return 0;
    }
}

/**
 * 杂乱背景优化处理
 * 专门处理背景杂乱导致的轮廓检测不准问题
 * @param {Mat} 处理图像 - 当前处理的图像
 * @param {Mat} 原图像 - 原始彩色图像
 * @param {Array} 轮廓信息 - 轮廓信息数组
 * @returns {Object} - 优化结果 {处理图像, 轮廓信息}
 */
function 杂乱背景优化处理(处理图像, 原图像, 轮廓信息) {
    try {
        console.log("  🌪️ 开始杂乱背景优化分析...");

        // 检测背景杂乱程度和图像特征
        var 杂乱程度 = 检测背景杂乱程度(处理图像, 轮廓信息);
        var 图像特征 = 分析图像对比度特征(处理图像);

        // 白色系高对比度图像跳过优化
        if (图像特征.是高对比度白色系) {
            console.log("  ✨ 检测到高对比度白色系图像，跳过杂乱背景优化");
            console.log("  📊 对比度: " + Math.round(图像特征.对比度) + ", 白色占比: " + Math.round(图像特征.白色占比 * 100) + "%");
            return { 处理图像: null, 轮廓信息: null };
        }

        if (杂乱程度.杂乱等级 < 1) {
            console.log("  ℹ️ 背景杂乱程度较低(" + 杂乱程度.杂乱等级 + "/5)，跳过优化");
            return { 处理图像: null, 轮廓信息: null };
        }

        console.log("  ⚠️ 检测到杂乱背景，杂乱等级: " + 杂乱程度.杂乱等级 + "/5");
        console.log("  📊 杂乱特征: " + 杂乱程度.杂乱特征.join(", "));

        // 执行多重优化策略
        var 优化图像 = 执行杂乱背景优化(处理图像, 原图像, 轮廓信息, 杂乱程度);

        if (优化图像) {
            console.log("  🔄 重新检测优化后的轮廓...");

            // 重新检测优化后的轮廓
            var 新轮廓信息 = 重新检测优化轮廓(优化图像, 原图像, 轮廓信息);

            return {
                处理图像: 优化图像,
                轮廓信息: 新轮廓信息
            };
        }

        return { 处理图像: null, 轮廓信息: null };

    } catch (e) {
        console.error("杂乱背景优化处理失败: " + e);
        return { 处理图像: null, 轮廓信息: null };
    }
}

/**
 * 检测背景杂乱程度
 * 分析图像的复杂度和噪声水平
 * @param {Mat} 处理图像 - 当前图像
 * @param {Array} 轮廓信息 - 轮廓信息
 * @returns {Object} - 杂乱程度分析结果
 */
function 检测背景杂乱程度(处理图像, 轮廓信息) {
    try {
        var 杂乱特征 = [];
        var 杂乱分数 = 0;

        // 转换为灰度图进行分析
        var 灰度图 = new Mat();
        if (处理图像.channels() === 3) {
            Imgproc.cvtColor(处理图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 处理图像.clone();
        }

        // 1. 简化的边缘密度检测，避免countNonZero错误
        var 总像素数 = 灰度图.rows() * 灰度图.cols();

        // 基于轮廓数量估算边缘密度
        var 轮廓数量 = 轮廓信息 ? 轮廓信息.length : 0;
        var 边缘密度;
        if (轮廓数量 > 20) {
            边缘密度 = 0.15; // 高密度
        } else if (轮廓数量 > 10) {
            边缘密度 = 0.10; // 中等密度
        } else {
            边缘密度 = 0.05; // 低密度
        }

        if (边缘密度 > 0.08) {  // 降低阈值：15% → 8%
            杂乱分数 += 2;
            杂乱特征.push("高边缘密度(" + Math.round(边缘密度 * 100) + "%)");
        } else if (边缘密度 > 0.05) {  // 新增中等边缘密度检测
            杂乱分数 += 1;
            杂乱特征.push("中等边缘密度(" + Math.round(边缘密度 * 100) + "%)");
        }

        // 2. 创建边缘图并检测轮廓数量
        var 边缘图 = new Mat();
        Imgproc.Canny(灰度图, 边缘图, 50, 150);

        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();
        Imgproc.findContours(边缘图, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        var 轮廓数量 = 轮廓列表.size();
        if (轮廓数量 > 5) {  // 降低阈值：10 → 5
            杂乱分数 += 1;
            杂乱特征.push("轮廓较多(" + 轮廓数量 + "个)");
        }

        // 3. 检测颜色复杂度（如果是彩色图像）
        if (处理图像.channels() === 3) {
            var 颜色复杂度 = 计算颜色复杂度(处理图像);
            if (颜色复杂度 > 0.4) {  // 降低阈值：0.7 → 0.4
                杂乱分数 += 1;
                杂乱特征.push("颜色复杂(" + Math.round(颜色复杂度 * 100) + "%)");
            }
        }

        // 4. 检测噪声水平
        var 噪声水平 = 计算噪声水平(灰度图);
        if (噪声水平 > 8) {  // 降低阈值：15 → 8
            杂乱分数 += 1;
            杂乱特征.push("噪声干扰(" + Math.round(噪声水平) + ")");
        }

        // 5. 检测目标轮廓占比
        if (轮廓信息 && 轮廓信息.length > 0) {
            var 目标面积 = 轮廓信息.reduce(function(sum, 轮廓) { return sum + 轮廓.面积; }, 0);
            var 目标占比 = 目标面积 / 总像素数;

            if (目标占比 < 0.1) {  // 降低阈值：0.05 → 0.1
                杂乱分数 += 1;
                杂乱特征.push("目标较小(" + Math.round(目标占比 * 100) + "%)");
            }
        }

        // 6. 新增：检测是否有多个分散的轮廓
        if (轮廓信息 && 轮廓信息.length > 1) {
            杂乱分数 += 1;
            杂乱特征.push("多轮廓分散(" + 轮廓信息.length + "个)");
        }

        // 释放资源
        灰度图.release();
        边缘图.release();  // 现在需要释放边缘图
        层次结构.release();
        for (var i = 0; i < 轮廓列表.size(); i++) {
            轮廓列表.get(i).release();
        }

        return {
            杂乱等级: Math.min(5, 杂乱分数),
            杂乱特征: 杂乱特征,
            边缘密度: 边缘密度,
            轮廓数量: 轮廓数量
        };

    } catch (e) {
        console.error("检测背景杂乱程度失败: " + e);
        return { 杂乱等级: 0, 杂乱特征: [], 边缘密度: 0, 轮廓数量: 0 };
    }
}

/**
 * 计算颜色复杂度
 * @param {Mat} 彩色图像 - 输入的彩色图像
 * @returns {Number} - 颜色复杂度 (0-1)
 */
function 计算颜色复杂度(彩色图像) {
    try {
        // 转换为HSV色彩空间
        var hsv图像 = new Mat();
        Imgproc.cvtColor(彩色图像, hsv图像, Imgproc.COLOR_BGR2HSV);

        // 计算色调的标准差
        var 色调通道 = new Mat();
        Core.extractChannel(hsv图像, 色调通道, 0);

        var 平均值 = Core.mean(色调通道);
        var 标准差 = 计算标准差(色调通道, 平均值.val[0]);

        // 释放资源
        hsv图像.release();
        色调通道.release();

        // 标准差越大，颜色越复杂
        return Math.min(1.0, 标准差 / 50.0);

    } catch (e) {
        return 0;
    }
}

/**
 * 计算噪声水平
 * @param {Mat} 灰度图 - 灰度图像
 * @returns {Number} - 噪声水平
 */
function 计算噪声水平(灰度图) {
    try {
        // 使用拉普拉斯算子检测噪声
        var 拉普拉斯图 = new Mat();
        Imgproc.Laplacian(灰度图, 拉普拉斯图, -1);

        var 平均值 = Core.mean(拉普拉斯图);

        拉普拉斯图.release();

        return Math.abs(平均值.val[0]);

    } catch (e) {
        return 0;
    }
}

/**
 * 计算标准差
 * @param {Mat} 图像 - 单通道图像
 * @param {Number} 平均值 - 平均值
 * @returns {Number} - 标准差
 */
function 计算标准差(图像, 平均值) {
    try {
        var 差值图 = new Mat();
        var 平均标量 = new Scalar(平均值);

        Core.subtract(图像, 平均标量, 差值图);
        Core.multiply(差值图, 差值图, 差值图);

        var 方差 = Core.mean(差值图);

        差值图.release();

        return Math.sqrt(方差.val[0]);

    } catch (e) {
        return 0;
    }
}

/**
 * 执行杂乱背景优化
 * 使用多种策略清理杂乱背景，突出目标轮廓
 * @param {Mat} 处理图像 - 当前图像
 * @param {Mat} 原图像 - 原始图像
 * @param {Array} 轮廓信息 - 轮廓信息
 * @param {Object} 杂乱程度 - 杂乱程度分析结果
 * @returns {Mat} - 优化后的图像
 */
function 执行杂乱背景优化(处理图像, 原图像, 轮廓信息, 杂乱程度) {
    try {
        var 优化图像 = 处理图像.clone();

        console.log("  🧹 策略1: 形态学清理 - 去除小噪声");

        // 策略1: 形态学操作清理噪声
        var 清理图像 = 形态学清理(优化图像);
        优化图像.release();
        优化图像 = 清理图像;

        console.log("  🎯 策略2: 目标区域保护 - 突出主要轮廓");

        // 策略2: 目标区域保护和增强
        var 保护图像 = 目标区域保护增强(优化图像, 轮廓信息);
        优化图像.release();
        优化图像 = 保护图像;

        console.log("  📊 策略3: 自适应阈值 - 增强对比度");

        // 策略3: 自适应阈值处理
        var 阈值图像 = 自适应阈值处理(优化图像);
        优化图像.release();
        优化图像 = 阈值图像;

        console.log("  🔍 策略4: 轮廓精炼 - 保留主要轮廓");

        // 策略4: 轮廓精炼
        var 精炼图像 = 轮廓精炼处理(优化图像, 轮廓信息);
        优化图像.release();
        优化图像 = 精炼图像;

        return 优化图像;

    } catch (e) {
        console.error("执行杂乱背景优化失败: " + e);
        return null;
    }
}

/**
 * 形态学清理
 * 使用形态学操作去除小噪声和断开的线条
 * @param {Mat} 输入图像 - 输入图像
 * @returns {Mat} - 清理后的图像
 */
function 形态学清理(输入图像) {
    try {
        var 清理图像 = 输入图像.clone();

        // 开运算：先腐蚀后膨胀，去除小噪声
        var 开运算核心 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(3, 3));
        var 开运算图像 = new Mat();
        Imgproc.morphologyEx(清理图像, 开运算图像, Imgproc.MORPH_OPEN, 开运算核心);

        // 闭运算：先膨胀后腐蚀，连接断开的线条
        var 闭运算核心 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(2, 2));
        var 闭运算图像 = new Mat();
        Imgproc.morphologyEx(开运算图像, 闭运算图像, Imgproc.MORPH_CLOSE, 闭运算核心);

        // 释放资源
        清理图像.release();
        开运算图像.release();
        开运算核心.release();
        闭运算核心.release();

        return 闭运算图像;

    } catch (e) {
        console.error("形态学清理失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 目标区域保护增强
 * 保护和增强目标轮廓区域，弱化背景干扰
 * @param {Mat} 输入图像 - 输入图像
 * @param {Array} 轮廓信息 - 轮廓信息
 * @returns {Mat} - 增强后的图像
 */
function 目标区域保护增强(输入图像, 轮廓信息) {
    try {
        var 增强图像 = 输入图像.clone();

        if (!轮廓信息 || 轮廓信息.length === 0) {
            return 增强图像;
        }

        // 创建目标区域掩码
        var 目标掩码 = new Mat(输入图像.rows(), 输入图像.cols(), CvType.CV_8UC1, new Scalar(0));  // 黑色背景

        // 在目标区域绘制白色
        for (var i = 0; i < 轮廓信息.length; i++) {
            var 轮廓 = 轮廓信息[i];
            var 中心 = 轮廓.中心点;
            var 尺寸 = 轮廓.尺寸;

            // 扩大保护区域
            var 扩展系数 = 1.5;
            var 保护区域 = new Rect(
                Math.max(0, 中心.x - 尺寸.宽度 * 扩展系数 / 2),
                Math.max(0, 中心.y - 尺寸.高度 * 扩展系数 / 2),
                Math.min(尺寸.宽度 * 扩展系数, 输入图像.cols() - Math.max(0, 中心.x - 尺寸.宽度 * 扩展系数 / 2)),
                Math.min(尺寸.高度 * 扩展系数, 输入图像.rows() - Math.max(0, 中心.y - 尺寸.高度 * 扩展系数 / 2))
            );

            var 保护子图像 = new Mat(目标掩码, 保护区域);
            保护子图像.setTo(new Scalar(255));  // 白色表示保护区域
            保护子图像.release();
        }

        // 对非目标区域进行弱化处理
        var 弱化图像 = new Mat();
        增强图像.convertTo(弱化图像, -1, 0.3, 0);  // 降低亮度到30%

        // 使用掩码合并：目标区域保持原样，非目标区域弱化
        var 最终图像 = new Mat();
        var 反向掩码 = new Mat();
        Core.bitwise_not(目标掩码, 反向掩码);  // 创建反向掩码

        增强图像.copyTo(最终图像, 目标掩码);  // 目标区域
        弱化图像.copyTo(最终图像, 反向掩码);  // 非目标区域

        反向掩码.release();  // 释放反向掩码

        // 释放资源
        增强图像.release();
        目标掩码.release();
        弱化图像.release();

        return 最终图像;

    } catch (e) {
        console.error("目标区域保护增强失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 自适应阈值处理
 * 使用自适应阈值增强局部对比度
 * @param {Mat} 输入图像 - 输入图像
 * @returns {Mat} - 阈值处理后的图像
 */
function 自适应阈值处理(输入图像) {
    try {
        // 转换为灰度图
        var 灰度图 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 输入图像.clone();
        }

        // 确保是8位单通道图像 - 使用更安全的检查方式
        console.log("    🔍 灰度图类型检查: " + 灰度图.type() + " (目标: " + CvType.CV_8UC1 + ")");

        if (灰度图.type() != CvType.CV_8UC1) {
            try {
                var 转换图像 = new Mat();
                // 使用更安全的转换方式
                if (灰度图.depth() != CvType.CV_8U) {
                    // 如果不是8位，先转换为8位
                    灰度图.convertTo(转换图像, CvType.CV_8U);
                    灰度图.release();
                    灰度图 = 转换图像;
                    转换图像 = new Mat();
                }

                // 如果不是单通道，转换为单通道
                if (灰度图.channels() != 1) {
                    if (灰度图.channels() == 3) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGR2GRAY);
                    } else if (灰度图.channels() == 4) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGRA2GRAY);
                    } else {
                        // 其他情况，直接复制第一个通道
                        Core.extractChannel(灰度图, 转换图像, 0);
                    }
                    灰度图.release();
                    灰度图 = 转换图像;
                }

                console.log("    ✅ 图像类型转换完成: " + 灰度图.type());
            } catch (convertError) {
                console.error("    ❌ 图像类型转换失败: " + convertError);
                // 如果转换失败，跳过自适应阈值处理
                return 输入图像.clone();
            }
        }

        // 自适应阈值
        var 阈值图 = new Mat();
        Imgproc.adaptiveThreshold(灰度图, 阈值图, 255, Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C, Imgproc.THRESH_BINARY, 11, 2);

        // 如果原图是彩色的，转换回彩色
        var 结果图像;
        if (输入图像.channels() === 3) {
            结果图像 = new Mat();
            Imgproc.cvtColor(阈值图, 结果图像, Imgproc.COLOR_GRAY2BGR);
        } else {
            结果图像 = 阈值图.clone();
        }

        // 释放资源
        灰度图.release();
        阈值图.release();

        return 结果图像;

    } catch (e) {
        console.error("自适应阈值处理失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 轮廓精炼处理
 * 保留主要轮廓，去除干扰轮廓
 * @param {Mat} 输入图像 - 输入图像
 * @param {Array} 原轮廓信息 - 原始轮廓信息
 * @returns {Mat} - 精炼后的图像
 */
function 轮廓精炼处理(输入图像, 原轮廓信息) {
    try {
        // 转换为灰度图进行轮廓检测
        var 灰度图 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 输入图像.clone();
        }

        // 确保是8位单通道图像 - 使用更安全的检查方式
        console.log("    🔍 轮廓检测图像类型: " + 灰度图.type() + " (目标: " + CvType.CV_8UC1 + ")");

        if (灰度图.type() != CvType.CV_8UC1) {
            try {
                var 转换图像 = new Mat();
                // 使用更安全的转换方式
                if (灰度图.depth() != CvType.CV_8U) {
                    灰度图.convertTo(转换图像, CvType.CV_8U);
                    灰度图.release();
                    灰度图 = 转换图像;
                    转换图像 = new Mat();
                }

                if (灰度图.channels() != 1) {
                    if (灰度图.channels() == 3) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGR2GRAY);
                    } else if (灰度图.channels() == 4) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGRA2GRAY);
                    } else {
                        Core.extractChannel(灰度图, 转换图像, 0);
                    }
                    灰度图.release();
                    灰度图 = 转换图像;
                }

                console.log("    ✅ 轮廓检测图像转换完成: " + 灰度图.type());
            } catch (convertError) {
                console.error("    ❌ 轮廓检测图像转换失败: " + convertError);
                return 输入图像.clone();
            }
        }

        // 查找所有轮廓
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();
        Imgproc.findContours(灰度图, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        // 创建精炼图像（黑色背景）
        var 精炼图像 = new Mat(输入图像.rows(), 输入图像.cols(), 输入图像.type(), new Scalar(0, 0, 0));

        // 🎯 保留所有轮廓（包括精准度差的轮廓）
        console.log("    📊 检测到 " + 轮廓列表.size() + " 个原始轮廓");
        var 保留轮廓列表 = new ArrayList();

        for (var i = 0; i < 轮廓列表.size(); i++) {
            var 轮廓 = 轮廓列表.get(i);
            var 面积 = Imgproc.contourArea(轮廓);

            // 🛡️ 保留所有轮廓，不进行面积过滤
            保留轮廓列表.add(轮廓);
            console.log("      ✅ 保留轮廓 " + (i + 1) + "：面积 " + Math.round(面积) + " 像素（无过滤）");
        }

        console.log("    📊 最终保留 " + 保留轮廓列表.size() + " 个轮廓");

        // 绘制保留的轮廓
        for (var j = 0; j < 保留轮廓列表.size(); j++) {
            var 保留轮廓 = 保留轮廓列表.get(j);
            var 单轮廓列表 = new ArrayList();
            单轮廓列表.add(保留轮廓);

            // 填充轮廓为白色
            Imgproc.drawContours(精炼图像, 单轮廓列表, -1, new Scalar(255, 255, 255), -1);
            单轮廓列表.clear();
        }

        // 💾 保存轮廓精炼处理图
        try {
            var 时间戳 = new Date().getTime();
            var 精炼保存路径 = "/storage/emulated/0/Pictures/右广告_轮廓精炼_" + 时间戳 + "_处理图.jpg";
            Imgcodecs.imwrite(精炼保存路径, 精炼图像);
            console.log("    💾 轮廓精炼处理图已保存: " + 精炼保存路径);
        } catch (saveError) {
            console.error("    ❌ 保存轮廓精炼处理图失败: " + saveError);
        }

        console.log("    📊 轮廓精炼: " + 轮廓列表.size() + " → " + 保留轮廓列表.size() + " 个轮廓");

        // 释放资源
        灰度图.release();
        层次结构.release();
        for (var k = 0; k < 轮廓列表.size(); k++) {
            轮廓列表.get(k).release();
        }
        保留轮廓列表.clear();

        return 精炼图像;

    } catch (e) {
        console.error("轮廓精炼处理失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 重新检测优化轮廓
 * 在优化后的图像上重新检测轮廓，提高精准度
 * @param {Mat} 优化图像 - 优化后的图像
 * @param {Mat} 原图像 - 原始图像
 * @param {Array} 原轮廓信息 - 原始轮廓信息
 * @returns {Array} - 新的轮廓信息
 */
function 重新检测优化轮廓(优化图像, 原图像, 原轮廓信息) {
    try {
        console.log("  🔄 在优化图像上重新检测轮廓...");

        // 转换为灰度图
        var 灰度图 = new Mat();
        if (优化图像.channels() === 3) {
            Imgproc.cvtColor(优化图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 优化图像.clone();
        }

        // 确保是8位单通道图像 - 使用更安全的检查方式
        console.log("    🔍 优化轮廓检测图像类型: " + 灰度图.type() + " (目标: " + CvType.CV_8UC1 + ")");

        if (灰度图.type() != CvType.CV_8UC1) {
            try {
                var 转换图像 = new Mat();
                if (灰度图.depth() != CvType.CV_8U) {
                    灰度图.convertTo(转换图像, CvType.CV_8U);
                    灰度图.release();
                    灰度图 = 转换图像;
                    转换图像 = new Mat();
                }

                if (灰度图.channels() != 1) {
                    if (灰度图.channels() == 3) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGR2GRAY);
                    } else if (灰度图.channels() == 4) {
                        Imgproc.cvtColor(灰度图, 转换图像, Imgproc.COLOR_BGRA2GRAY);
                    } else {
                        Core.extractChannel(灰度图, 转换图像, 0);
                    }
                    灰度图.release();
                    灰度图 = 转换图像;
                }

                console.log("    ✅ 优化轮廓检测图像转换完成: " + 灰度图.type());
            } catch (convertError) {
                console.error("    ❌ 优化轮廓检测图像转换失败: " + convertError);
                return [];
            }
        }

        // 查找轮廓
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();
        Imgproc.findContours(灰度图, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        console.log("  📊 优化后检测到 " + 轮廓列表.size() + " 个轮廓");

        // 生成新的轮廓信息，优先选择最佳轮廓
        var 新轮廓信息 = [];
        var 轮廓评分列表 = [];

        // 计算每个轮廓的评分
        for (var i = 0; i < 轮廓列表.size(); i++) {
            var 轮廓 = 轮廓列表.get(i);
            var 面积 = Imgproc.contourArea(轮廓);

            if (面积 < 20) continue;  // 过滤太小的轮廓

            // 计算轮廓特征
            var 轮廓数组 = 轮廓.toArray();
            var 轮廓2f = new MatOfPoint2f();
            轮廓2f.fromArray(轮廓数组);

            var 外接矩形 = Imgproc.minAreaRect(轮廓2f);
            var 中心点 = 外接矩形.center;
            var 尺寸 = 外接矩形.size;

            // 计算轮廓评分（面积 + 形状规整度 + 位置合理性）
            var 面积评分 = Math.min(100, 面积 / 10);  // 面积评分
            var 形状评分 = 计算形状规整度(尺寸);  // 形状评分
            var 位置评分 = 计算位置合理性(中心点, 优化图像.size(), 原轮廓信息);  // 位置评分

            var 总评分 = 面积评分 + 形状评分 + 位置评分;

            轮廓评分列表.push({
                轮廓: 轮廓,
                中心点: 中心点,
                尺寸: 尺寸,
                面积: 面积,
                角度: 外接矩形.angle,
                评分: 总评分,
                索引: i
            });

            轮廓2f.release();
        }

        // 按评分排序，选择最佳轮廓
        轮廓评分列表.sort(function(a, b) { return b.评分 - a.评分; });

        // 生成最终轮廓信息（最多保留7个最佳轮廓）
        var 保留数量 = Math.min(7, 轮廓评分列表.length);

        for (var j = 0; j < 保留数量; j++) {
            var 最佳轮廓 = 轮廓评分列表[j];

            新轮廓信息.push({
                索引: j + 1,
                中心点: {
                    x: Math.round(最佳轮廓.中心点.x),
                    y: Math.round(最佳轮廓.中心点.y)
                },
                尺寸: {
                    宽度: Math.round(最佳轮廓.尺寸.width),
                    高度: Math.round(最佳轮廓.尺寸.height)
                },
                面积: Math.round(最佳轮廓.面积),
                角度: Math.round(最佳轮廓.角度),
                形状比例: Math.round((最佳轮廓.尺寸.width / Math.max(最佳轮廓.尺寸.height, 1)) * 100) / 100,
                评分: Math.round(最佳轮廓.评分),
                优化类型: "杂乱背景优化",
                颜色信息: null
            });

            console.log("    ✅ 轮廓 " + (j + 1) + " 评分: " + Math.round(最佳轮廓.评分) +
                       " 中心: (" + Math.round(最佳轮廓.中心点.x) + "," + Math.round(最佳轮廓.中心点.y) + ")");
        }

        // 释放资源
        灰度图.release();
        层次结构.release();
        for (var k = 0; k < 轮廓列表.size(); k++) {
            轮廓列表.get(k).release();
        }

        console.log("  ✅ 生成了 " + 新轮廓信息.length + " 个优化后的高质量轮廓");

        return 新轮廓信息;

    } catch (e) {
        console.error("重新检测优化轮廓失败: " + e);
        return [];
    }
}

/**
 * 计算形状规整度
 * @param {Size} 尺寸 - 轮廓尺寸
 * @returns {Number} - 形状规整度评分
 */
function 计算形状规整度(尺寸) {
    try {
        var 宽高比 = 尺寸.width / Math.max(尺寸.height, 1);

        // 接近正方形或标准矩形的形状得分更高
        if (Math.abs(宽高比 - 1.0) < 0.3) {
            return 30;  // 接近正方形
        } else if (宽高比 > 0.5 && 宽高比 < 2.0) {
            return 20;  // 合理的矩形
        } else {
            return 10;  // 过于细长
        }
    } catch (e) {
        return 0;
    }
}

/**
 * 计算位置合理性
 * @param {Point} 中心点 - 轮廓中心点
 * @param {Size} 图像尺寸 - 图像尺寸
 * @param {Array} 原轮廓信息 - 原始轮廓信息
 * @returns {Number} - 位置合理性评分
 */
function 计算位置合理性(中心点, 图像尺寸, 原轮廓信息) {
    try {
        var 位置评分 = 0;

        // 1. 避免边缘位置
        var 边缘距离 = Math.min(
            中心点.x,
            中心点.y,
            图像尺寸.width - 中心点.x,
            图像尺寸.height - 中心点.y
        );

        if (边缘距离 > 20) {
            位置评分 += 20;
        } else if (边缘距离 > 10) {
            位置评分 += 10;
        }

        // 2. 与原轮廓位置的相关性
        if (原轮廓信息 && 原轮廓信息.length > 0) {
            var 最小距离 = Infinity;

            for (var i = 0; i < 原轮廓信息.length; i++) {
                var 原中心 = 原轮廓信息[i].中心点;
                var 距离 = Math.sqrt(
                    Math.pow(中心点.x - 原中心.x, 2) +
                    Math.pow(中心点.y - 原中心.y, 2)
                );

                最小距离 = Math.min(最小距离, 距离);
            }

            // 距离原轮廓越近，评分越高
            if (最小距离 < 20) {
                位置评分 += 30;
            } else if (最小距离 < 50) {
                位置评分 += 15;
            }
        }

        return 位置评分;

    } catch (e) {
        return 0;
    }
}

/**
 * 轮廓颜色增强处理V2
 * 使用真实轮廓填充，而不是矩形覆盖
 * @param {Mat} 处理图像 - 已处理的图像（背景为黑色）
 * @param {Mat} 边缘图 - 边缘检测结果，用于重新查找轮廓
 * @param {Array} 轮廓信息 - 轮廓信息数组
 * @param {Mat} 原图像 - 原始彩色图像
 * @returns {Mat} - 颜色增强后的图像
 */
function 轮廓颜色增强处理V2(处理图像, 边缘图, 轮廓信息, 原图像) {
    try {
        if (!轮廓信息 || 轮廓信息.length === 0) {
            console.log("  ⚠️ 无轮廓信息，跳过颜色增强处理");
            return null;
        }

        var 增强图像 = 处理图像.clone();
        var 需要增强 = false;

        console.log("  🎨 开始轮廓颜色增强分析V2...");

        // 重新查找轮廓（从边缘图中）
        var 实际轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();

        Imgproc.findContours(边缘图, 实际轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        console.log("  📊 重新检测到 " + 实际轮廓列表.size() + " 个实际轮廓");

        // 遍历每个轮廓信息，找到对应的实际轮廓并处理
        for (var i = 0; i < 轮廓信息.length && i < 实际轮廓列表.size(); i++) {
            var 轮廓信息项 = 轮廓信息[i];

            if (!轮廓信息项.颜色信息) {
                continue;
            }

            var 颜色 = 轮廓信息项.颜色信息;
            var 需要转换 = 判断是否需要颜色转换(颜色);

            if (需要转换.需要转换) {
                console.log("    🔄 轮廓 " + 轮廓信息项.索引 + " 需要颜色转换: " + 颜色.颜色名称 + " → 白色系");
                console.log("      📝 转换原因: " + 需要转换.原因);

                // 获取对应的实际轮廓
                var 实际轮廓 = 实际轮廓列表.get(i);

                // 执行真实轮廓填充
                执行真实轮廓填充(增强图像, 实际轮廓, 颜色);
                需要增强 = true;
            } else {
                console.log("    ✅ 轮廓 " + 轮廓信息项.索引 + " 无需转换: " + 颜色.颜色名称);
            }
        }

        // 释放资源
        层次结构.release();
        for (var j = 0; j < 实际轮廓列表.size(); j++) {
            实际轮廓列表.get(j).release();
        }

        if (需要增强) {
            console.log("  ✨ 已完成 " + 轮廓信息.length + " 个轮廓的真实填充处理");
            return 增强图像;
        } else {
            console.log("  ℹ️ 所有轮廓颜色均无需转换");
            增强图像.release();
            return null;
        }

    } catch (e) {
        console.error("轮廓颜色增强处理V2失败: " + e);
        return null;
    }
}

/**
 * 执行真实轮廓填充
 * 使用drawContours填充轮廓内部，而不是矩形覆盖
 * @param {Mat} 增强图像 - 要处理的图像
 * @param {MatOfPoint} 实际轮廓 - 真实的轮廓对象
 * @param {Object} 颜色信息 - 颜色信息
 */
function 执行真实轮廓填充(增强图像, 实际轮廓, 颜色信息) {
    try {
        // 根据原始颜色选择转换策略
        var 目标颜色 = 选择目标白色(颜色信息);

        // 创建轮廓列表（drawContours需要列表格式）
        var 轮廓列表 = new ArrayList();
        轮廓列表.add(实际轮廓);

        // 使用drawContours填充轮廓内部
        // 参数：图像, 轮廓列表, 轮廓索引(-1表示所有), 颜色, 厚度(-1表示填充)
        Imgproc.drawContours(增强图像, 轮廓列表, -1, 目标颜色, -1);

        console.log("      ✅ 已填充轮廓为: " + 目标颜色.val[2] + "," + 目标颜色.val[1] + "," + 目标颜色.val[0] + " (BGR)");

        // 释放临时列表
        轮廓列表.clear();

    } catch (e) {
        console.error("执行真实轮廓填充失败: " + e);
    }
}

/**
 * 输出全屏轮廓信息
 * 将区域内的轮廓坐标转换为全屏坐标并输出
 * @param {Array} 轮廓信息列表 - 区域内的轮廓信息
 * @param {Array} 区域坐标 - 区域在全屏中的位置 [x, y, width, height]
 * @param {String} 区域名称 - 区域名称
 * @param {Array} 边缘轮廓信息列表 - 边缘检测的轮廓信息（可选）
 */
function 输出全屏轮廓信息(轮廓信息列表, 区域坐标, 区域名称, 边缘轮廓信息列表) {
    try {
        if (!轮廓信息列表 || 轮廓信息列表.length === 0) {
            return;
        }

        console.log("🌍 " + 区域名称 + " 全屏轮廓坐标信息:");

        for (var i = 0; i < 轮廓信息列表.length; i++) {
            var 轮廓 = 轮廓信息列表[i];

            // 转换为全屏坐标
            var 全屏中心点 = {
                x: 区域坐标[0] + 轮廓.中心点.x,
                y: 区域坐标[1] + 轮廓.中心点.y
            };

            console.log("  🎯 轮廓 " + 轮廓.索引 + " 全屏位置:");
            console.log("    📍 区域内坐标: (" + 轮廓.中心点.x + ", " + 轮廓.中心点.y + ")");
            console.log("    🌍 全屏坐标: (" + 全屏中心点.x + ", " + 全屏中心点.y + ")");
            console.log("    📏 尺寸: " + 轮廓.尺寸.宽度 + "×" + 轮廓.尺寸.高度);
            console.log("    📐 面积: " + 轮廓.面积 + " 像素");
            console.log("    🔍 形状: " + 分析轮廓形状(轮廓));

            // 🎨 显示轮廓颜色信息
            if (轮廓.颜色信息) {
                var 颜色 = 轮廓.颜色信息;
                console.log("    🎨 物体颜色: " + 颜色.颜色名称 + " (" + 颜色.十六进制 + ")");
                console.log("    📊 颜色置信度: " + 颜色.置信度 + "%");
            }

            // 输出区域信息
            console.log("    📋 区域信息:");
            console.log("      🏷️ 区域名称: " + 区域名称);
            console.log("      📦 区域范围: (" + 区域坐标[0] + ", " + 区域坐标[1] + ") 到 (" +
                       (区域坐标[0] + 区域坐标[2]) + ", " + (区域坐标[1] + 区域坐标[3]) + ")");
            console.log("      📏 区域尺寸: " + 区域坐标[2] + "×" + 区域坐标[3]);
        }

        // 🌟 显示边缘检测轮廓
        if (边缘轮廓信息列表 && 边缘轮廓信息列表.length > 0) {
            console.log("  🌟 边缘检测轮廓:");
            for (var j = 0; j < 边缘轮廓信息列表.length; j++) {
                var 边缘轮廓 = 边缘轮廓信息列表[j];
                console.log("  🎯 " + 边缘轮廓.索引 + " 全屏位置:");
                console.log("    📍 区域内坐标: (" + 边缘轮廓.中心点.x + ", " + 边缘轮廓.中心点.y + ")");
                console.log("    🌍 全屏坐标: (" + 边缘轮廓.全屏坐标.x + ", " + 边缘轮廓.全屏坐标.y + ")");
                console.log("    📏 尺寸: " + 边缘轮廓.尺寸.宽度 + "×" + 边缘轮廓.尺寸.高度);
                console.log("    📐 面积: " + 边缘轮廓.面积 + " 像素");
                console.log("    🔍 形状: " + 边缘轮廓.形状);
                console.log("    📋 区域信息:");
                console.log("      🏷️ 区域名称: " + 区域名称);
                console.log("      📦 区域范围: (" + 区域坐标[0] + ", " + 区域坐标[1] + ") 到 (" +
                           (区域坐标[0] + 区域坐标[2]) + ", " + (区域坐标[1] + 区域坐标[3]) + ")");
                console.log("      📏 区域尺寸: " + 区域坐标[2] + "×" + 区域坐标[3]);
            }
        }

        // 🎯 合并所有轮廓信息，生成综合点击建议
        var 所有轮廓信息 = [];

        // 添加主要轮廓信息
        if (轮廓信息列表 && 轮廓信息列表.length > 0) {
            for (var i = 0; i < 轮廓信息列表.length; i++) {
                var 轮廓 = 轮廓信息列表[i];
                所有轮廓信息.push({
                    来源: "主要检测",
                    索引: i + 1,
                    全屏坐标: {
                        x: 区域坐标[0] + 轮廓.中心点.x,
                        y: 区域坐标[1] + 轮廓.中心点.y
                    },
                    面积: 轮廓.面积,
                    形状: 轮廓.形状 || "未知形状",
                    优先级: 轮廓.面积  // 使用面积作为优先级
                });
            }
        }

        // 🌟 添加边缘检测轮廓信息
        if (边缘轮廓信息列表 && 边缘轮廓信息列表.length > 0) {
            console.log("  🌟 发现边缘检测轮廓，纳入点击建议:");
            for (var j = 0; j < 边缘轮廓信息列表.length; j++) {
                var 边缘轮廓 = 边缘轮廓信息列表[j];
                所有轮廓信息.push({
                    来源: "边缘检测",
                    索引: 边缘轮廓.索引,
                    全屏坐标: 边缘轮廓.全屏坐标,
                    面积: 边缘轮廓.面积,
                    形状: 边缘轮廓.形状,
                    优先级: 边缘轮廓.面积 * 0.8  // 边缘检测轮廓优先级稍低
                });
                console.log("    - " + 边缘轮廓.索引 + ": (" + 边缘轮廓.全屏坐标.x + ", " + 边缘轮廓.全屏坐标.y + ") " +
                           "面积:" + 边缘轮廓.面积 + " 形状:" + 边缘轮廓.形状);
            }
        }

        // 输出综合点击建议
        if (所有轮廓信息.length > 0) {
            // 按优先级排序（面积大的优先）
            所有轮廓信息.sort(function(a, b) { return b.优先级 - a.优先级; });

            var 主要轮廓 = 所有轮廓信息[0];  // 优先级最高的轮廓

            console.log("  💡 点击建议:");
            console.log("    🖱️ 推荐点击坐标: (" + 主要轮廓.全屏坐标.x + ", " + 主要轮廓.全屏坐标.y + ")");
            console.log("    📝 AutoXjs点击代码: click(" + 主要轮廓.全屏坐标.x + ", " + 主要轮廓.全屏坐标.y + ");");
            console.log("    🎯 选择依据: " + 主要轮廓.来源 + " - " + 主要轮廓.形状 + " (面积:" + Math.round(主要轮廓.优先级) + ")");

            // 如果有多个候选坐标，显示备选方案
            if (所有轮廓信息.length > 1) {
                console.log("    📋 备选坐标:");
                for (var k = 1; k < Math.min(所有轮廓信息.length, 3); k++) {  // 最多显示3个备选
                    var 备选轮廓 = 所有轮廓信息[k];
                    console.log("      " + (k + 1) + ". (" + 备选轮廓.全屏坐标.x + ", " + 备选轮廓.全屏坐标.y + ") - " +
                               备选轮廓.来源 + " " + 备选轮廓.形状);
                }
            }
        }

    } catch (e) {
        console.error("输出全屏轮廓信息失败: " + e);
    }
}

/**
 * 智能颜色增强预处理函数
 * 根据背景色特征选择最佳的颜色通道或增强策略
 * @param {Mat} 输入图像 - 输入的图像
 * @param {Object} 背景特征 - 背景颜色分析结果
 * @returns {Mat} - 增强处理后的灰度图像
 */
function 智能颜色增强预处理(输入图像, 背景特征) {
    try {
        console.log("🎯 根据背景色(" + 背景特征.颜色名称 + ")选择最佳增强策略...");

        var 最终图像;

        // 根据背景颜色类型选择对比增强策略
        if (背景特征.颜色名称.indexOf("红色") !== -1) {
            // 红色背景：使用青色通道增强
            console.log("  📝 红色背景 → 使用青色通道增强");
            最终图像 = 青色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("绿色") !== -1) {
            // 绿色背景：使用红色通道增强
            console.log("  📝 绿色背景 → 使用红色通道增强");
            最终图像 = 红色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("蓝色") !== -1) {
            // 蓝色背景：使用黄色通道增强
            console.log("  📝 蓝色背景 → 使用黄色通道增强");
            最终图像 = 黄色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("黄色") !== -1) {
            // 黄色背景：使用蓝色通道增强
            console.log("  📝 黄色背景 → 使用蓝色通道增强");
            最终图像 = 蓝色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("紫色") !== -1) {
            // 紫色背景：使用绿色通道增强
            console.log("  📝 紫色背景 → 使用绿色通道增强");
            最终图像 = 绿色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("橙色") !== -1) {
            // 橙色背景：使用蓝色通道增强
            console.log("  📝 橙色背景 → 使用蓝色通道增强");
            最终图像 = 蓝色通道增强(输入图像);

        } else if (背景特征.颜色名称.indexOf("青色") !== -1) {
            // 青色背景：使用红色通道增强
            console.log("  📝 青色背景 → 使用红色通道增强");
            最终图像 = 红色通道增强(输入图像);

        } else if (背景特征.颜色类型 === "亮色") {
            // 白色背景：反转增强
            console.log("  📝 白色背景 → 使用反转增强");
            最终图像 = 反转增强(输入图像);

        } else if (背景特征.颜色类型 === "暗色") {
            // 黑色背景：亮度增强
            console.log("  📝 黑色背景 → 使用亮度增强");
            最终图像 = 亮度增强(输入图像);

        } else {
            // 默认：安全的灰度转换
            console.log("  📝 未知背景 → 使用安全灰度转换");
            console.log("    🔍 输入图像通道数: " + 输入图像.channels());

            最终图像 = new Mat();
            if (输入图像.channels() === 3) {
                Imgproc.cvtColor(输入图像, 最终图像, Imgproc.COLOR_BGR2GRAY);
            } else if (输入图像.channels() === 4) {
                Imgproc.cvtColor(输入图像, 最终图像, Imgproc.COLOR_BGRA2GRAY);
            } else {
                // 已经是单通道，直接复制
                最终图像 = 输入图像.clone();
            }
        }

        console.log("✅ 智能颜色增强完成");
        return 最终图像;

    } catch (e) {
        console.error("智能颜色增强失败: " + e);
        // 备用方案：安全的灰度转换
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 红色通道增强 - 适用于绿色背景
 */
function 红色通道增强(输入图像) {
    try {
        console.log("    🔴 红色通道增强，输入图像通道数: " + 输入图像.channels());

        // 检查输入图像通道数
        if (输入图像.channels() < 3) {
            console.log("    ⚠️ 输入图像通道数不足，使用对比度增强替代");
            var 增强图像 = new Mat();
            输入图像.convertTo(增强图像, -1, 1.5, 20);
            return 增强图像;
        }

        // 🎨 保持颜色信息的红色通道增强
        var 增强图像 = new Mat();

        // 分离BGR通道
        var 通道列表 = new ArrayList();
        Core.split(输入图像, 通道列表);

        var B通道 = 通道列表.get(0);
        var G通道 = 通道列表.get(1);
        var R通道 = 通道列表.get(2);

        // 增强红色通道
        var 增强R通道 = new Mat();
        R通道.convertTo(增强R通道, -1, 1.3, 15);  // 降低增强强度

        // 重新合并通道，保持颜色信息
        var 增强通道列表 = new ArrayList();
        增强通道列表.add(B通道);
        增强通道列表.add(G通道);
        增强通道列表.add(增强R通道);

        Core.merge(增强通道列表, 增强图像);

        // 释放资源
        B通道.release();
        G通道.release();
        R通道.release();
        增强R通道.release();

        console.log("    ✅ 红色通道增强完成，输出图像通道数: " + 增强图像.channels());
        return 增强图像;
    } catch (e) {
        console.error("红色通道增强失败: " + e);
        var 备用图像 = new Mat();
        // 安全的备用方案：检查通道数
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 绿色通道增强 - 适用于紫色背景
 */
function 绿色通道增强(输入图像) {
    try {
        console.log("    🟢 绿色通道增强，输入图像通道数: " + 输入图像.channels());

        if (输入图像.channels() < 3) {
            console.log("    ⚠️ 输入图像通道数不足，使用对比度增强替代");
            var 增强图像 = new Mat();
            输入图像.convertTo(增强图像, -1, 1.5, 20);
            return 增强图像;
        }

        // 🎨 保持颜色信息的绿色通道增强
        var 增强图像 = new Mat();

        // 分离BGR通道
        var 通道列表 = new ArrayList();
        Core.split(输入图像, 通道列表);

        var B通道 = 通道列表.get(0);
        var G通道 = 通道列表.get(1);
        var R通道 = 通道列表.get(2);

        // 增强绿色通道
        var 增强G通道 = new Mat();
        G通道.convertTo(增强G通道, -1, 1.3, 15);  // 降低增强强度

        // 重新合并通道，保持颜色信息
        var 增强通道列表 = new ArrayList();
        增强通道列表.add(B通道);
        增强通道列表.add(增强G通道);
        增强通道列表.add(R通道);

        Core.merge(增强通道列表, 增强图像);

        // 释放资源
        B通道.release();
        G通道.release();
        R通道.release();
        增强G通道.release();

        console.log("    ✅ 绿色通道增强完成，输出图像通道数: " + 增强图像.channels());
        return 增强图像;
    } catch (e) {
        console.error("绿色通道增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 蓝色通道增强 - 适用于黄色/橙色背景
 */
function 蓝色通道增强(输入图像) {
    try {
        console.log("    🔵 蓝色通道增强，输入图像通道数: " + 输入图像.channels());

        if (输入图像.channels() < 3) {
            console.log("    ⚠️ 输入图像通道数不足，使用对比度增强替代");
            var 增强图像 = new Mat();
            输入图像.convertTo(增强图像, -1, 1.5, 20);
            return 增强图像;
        }

        // 🎨 保持颜色信息的蓝色通道增强
        var 增强图像 = new Mat();

        // 分离BGR通道
        var 通道列表 = new ArrayList();
        Core.split(输入图像, 通道列表);

        var B通道 = 通道列表.get(0);
        var G通道 = 通道列表.get(1);
        var R通道 = 通道列表.get(2);

        // 增强蓝色通道
        var 增强B通道 = new Mat();
        B通道.convertTo(增强B通道, -1, 1.3, 15);  // 降低增强强度

        // 重新合并通道，保持颜色信息
        var 增强通道列表 = new ArrayList();
        增强通道列表.add(增强B通道);
        增强通道列表.add(G通道);
        增强通道列表.add(R通道);

        Core.merge(增强通道列表, 增强图像);

        // 释放资源
        B通道.release();
        G通道.release();
        R通道.release();
        增强B通道.release();

        console.log("    ✅ 蓝色通道增强完成，输出图像通道数: " + 增强图像.channels());
        return 增强图像;
    } catch (e) {
        console.error("蓝色通道增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 青色通道增强 - 适用于红色背景
 */
function 青色通道增强(输入图像) {
    try {
        console.log("    🔷 青色通道增强，输入图像通道数: " + 输入图像.channels());

        if (输入图像.channels() < 3) {
            console.log("    ⚠️ 输入图像通道数不足，使用对比度增强替代");
            var 增强图像 = new Mat();
            输入图像.convertTo(增强图像, -1, 1.2, 15);
            return 增强图像;
        }

        // 提取绿色和蓝色通道
        var 绿色通道 = new Mat();
        var 蓝色通道 = new Mat();
        Core.extractChannel(输入图像, 绿色通道, 1);
        Core.extractChannel(输入图像, 蓝色通道, 0);

        // 青色 = 绿色 + 蓝色
        var 青色通道 = new Mat();
        Core.add(绿色通道, 蓝色通道, 青色通道);

        // 对比度增强
        var 增强图像 = new Mat();
        青色通道.convertTo(增强图像, -1, 1.2, 15);

        // 释放资源
        绿色通道.release();
        蓝色通道.release();
        青色通道.release();

        return 增强图像;
    } catch (e) {
        console.error("青色通道增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 黄色通道增强 - 适用于蓝色背景
 */
function 黄色通道增强(输入图像) {
    try {
        console.log("    🟡 黄色通道增强，输入图像通道数: " + 输入图像.channels());

        if (输入图像.channels() < 3) {
            console.log("    ⚠️ 输入图像通道数不足，使用对比度增强替代");
            var 增强图像 = new Mat();
            输入图像.convertTo(增强图像, -1, 1.2, 15);
            return 增强图像;
        }

        // 提取红色和绿色通道
        var 红色通道 = new Mat();
        var 绿色通道 = new Mat();
        Core.extractChannel(输入图像, 红色通道, 2);
        Core.extractChannel(输入图像, 绿色通道, 1);

        // 黄色 = 红色 + 绿色
        var 黄色通道 = new Mat();
        Core.add(红色通道, 绿色通道, 黄色通道);

        // 对比度增强
        var 增强图像 = new Mat();
        黄色通道.convertTo(增强图像, -1, 1.2, 15);

        // 释放资源
        红色通道.release();
        绿色通道.release();
        黄色通道.release();

        return 增强图像;
    } catch (e) {
        console.error("黄色通道增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 反转增强 - 适用于白色背景
 */
function 反转增强(输入图像) {
    try {
        console.log("    🔄 反转增强，输入图像通道数: " + 输入图像.channels());

        // 🎨 保持颜色信息的反转增强
        var 反转图像 = new Mat();
        var 增强图像 = new Mat();

        if (输入图像.channels() === 3 || 输入图像.channels() === 4) {
            // 彩色图像：保持颜色信息进行反转
            console.log("    🎨 保持彩色信息进行反转增强");
            Core.bitwise_not(输入图像, 反转图像);
            反转图像.convertTo(增强图像, -1, 1.2, 10);  // 降低增强强度

        } else {
            // 灰度图像：直接反转增强
            console.log("    🔘 灰度图像反转增强");
            Core.bitwise_not(输入图像, 反转图像);
            反转图像.convertTo(增强图像, -1, 1.3, 10);
        }

        // 释放资源
        反转图像.release();

        console.log("    ✅ 反转增强完成，输出图像通道数: " + 增强图像.channels());
        return 增强图像;
    } catch (e) {
        console.error("反转增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}

/**
 * 亮度增强 - 适用于黑色背景
 */
function 亮度增强(输入图像) {
    try {
        console.log("    💡 亮度增强，输入图像通道数: " + 输入图像.channels());

        // 🎨 保持颜色信息的亮度增强
        var 增强图像 = new Mat();

        if (输入图像.channels() === 3 || 输入图像.channels() === 4) {
            // 彩色图像：保持颜色信息，只增强亮度和对比度
            console.log("    🎨 保持彩色信息进行亮度增强");
            输入图像.convertTo(增强图像, -1, 1.3, 30);  // 降低增强强度：1.5→1.3, 50→30

        } else {
            // 灰度图像：直接增强
            console.log("    🔘 灰度图像亮度增强");
            输入图像.convertTo(增强图像, -1, 1.5, 50);
        }

        console.log("    ✅ 亮度增强完成，输出图像通道数: " + 增强图像.channels());
        return 增强图像;
    } catch (e) {
        console.error("亮度增强失败: " + e);
        var 备用图像 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 备用图像, Imgproc.COLOR_BGR2GRAY);
        } else {
            备用图像 = 输入图像.clone();
        }
        return 备用图像;
    }
}


/**
 * 图像预处理函数
 * 步骤：读取图片 → 灰度转换
 * @param {Mat} inputMat - 输入图像矩阵
 * @returns {Mat} - 处理后的灰度图像
 */
function 图像预处理(inputMat) {
    try {
        // 灰度转换 - 彩色转灰度
        var 灰度图像 = new Mat();
        Imgproc.cvtColor(inputMat, 灰度图像, Imgproc.COLOR_BGR2GRAY);

        return 灰度图像;
    } catch (e) {
        console.error("图像预处理失败: " + e);
        return null;
    }
}





/**
 * 分析颜色特征
 * 根据HSV值判断具体的颜色类型和检测建议
 * @param {Number} 色调 - HSV色调值 (0-180)
 * @param {Number} 饱和度 - HSV饱和度值 (0-255)
 * @param {Number} 明度 - HSV明度值 (0-255)
 * @returns {Object} - 颜色分析结果
 */
function 分析颜色特征(色调, 饱和度, 明度) {
    try {
        var 颜色名称 = "未知颜色";
        var 颜色类型 = "其他";
        var 检测建议 = "使用标准检测";

        // 判断是否为无色彩（黑白灰）
        if (饱和度 < 30) {
            // 低饱和度，判断为黑白灰色系
            if (明度 < 85) {
                颜色名称 = "黑色系";
                颜色类型 = "暗色";
                检测建议 = "优先使用白色模板";
            } else if (明度 > 170) {
                颜色名称 = "白色系";
                颜色类型 = "亮色";
                检测建议 = "优先使用黑色模板";
            } else {
                颜色名称 = "灰色系";
                颜色类型 = "中性";
                检测建议 = "根据明度选择模板";
            }
        } else {
            // 有色彩，根据色调判断颜色
            if (色调 >= 0 && 色调 < 10) {
                颜色名称 = "红色系";
                颜色类型 = "暖色";
                检测建议 = "使用对比色模板，注意红色干扰";
            } else if (色调 >= 10 && 色调 < 25) {
                颜色名称 = "橙色系";
                颜色类型 = "暖色";
                检测建议 = "使用冷色调模板，增强对比度";
            } else if (色调 >= 25 && 色调 < 35) {
                颜色名称 = "黄色系";
                颜色类型 = "暖色";
                检测建议 = "使用深色模板，避免黄色干扰";
            } else if (色调 >= 35 && 色调 < 85) {
                颜色名称 = "绿色系";
                颜色类型 = "冷色";
                检测建议 = "使用红色或蓝色模板，增强色彩对比";
            } else if (色调 >= 85 && 色调 < 125) {
                颜色名称 = "青色系";
                颜色类型 = "冷色";
                检测建议 = "使用暖色调模板，增强对比度";
            } else if (色调 >= 125 && 色调 < 155) {
                颜色名称 = "蓝色系";
                颜色类型 = "冷色";
                检测建议 = "使用黄色或红色模板，增强色彩对比";
            } else if (色调 >= 155 && 色调 < 180) {
                颜色名称 = "紫色系";
                颜色类型 = "冷色";
                检测建议 = "使用绿色或黄色模板，增强对比度";
            } else {
                颜色名称 = "红色系";  // 色调环回到红色
                颜色类型 = "暖色";
                检测建议 = "使用对比色模板，注意红色干扰";
            }

            // 根据饱和度调整建议
            if (饱和度 > 150) {
                检测建议 += "，高饱和度需要特别处理";
            } else if (饱和度 < 80) {
                检测建议 += "，低饱和度可使用标准方法";
            }
        }



        return {
            颜色名称: 颜色名称,
            颜色类型: 颜色类型,
            检测建议: 检测建议,
            色彩特征: {
                是否有色彩: 饱和度 >= 30,
                饱和度等级: 饱和度 < 80 ? "低" : (饱和度 > 150 ? "高" : "中"),
                明度等级: 明度 < 85 ? "暗" : (明度 > 170 ? "亮" : "中")
            }
        };
    } catch (e) {
        console.error("颜色特征分析失败: " + e);
        return {
            颜色名称: "分析失败",
            颜色类型: "未知",
            检测建议: "使用默认检测方法",
            色彩特征: {
                是否有色彩: false,
                饱和度等级: "未知",
                明度等级: "未知"
            }
        };
    }
}





/**
 * 轮廓检测函数
 * 步骤：确保输入为CV_8UC1格式后执行findContours
 * @param {Mat} 输入图像 - 可能是彩色或灰度图像
 * @returns {List} - 检测到的轮廓列表
 */
function 轮廓检测(输入图像) {
    try {
        console.log("  🔍 轮廓检测，输入图像通道数: " + 输入图像.channels());

        // 🎨 确保输入图像为CV_8UC1格式（单通道8位）
        var 灰度图像 = new Mat();

        if (输入图像.channels() === 3) {
            // 彩色图像转灰度
            Imgproc.cvtColor(输入图像, 灰度图像, Imgproc.COLOR_BGR2GRAY);
            console.log("  🎨 彩色图像已转换为灰度图进行轮廓检测");
        } else if (输入图像.channels() === 4) {
            // RGBA图像转灰度
            Imgproc.cvtColor(输入图像, 灰度图像, Imgproc.COLOR_BGRA2GRAY);
            console.log("  🎨 RGBA图像已转换为灰度图进行轮廓检测");
        } else if (输入图像.channels() === 1) {
            // 确保是CV_8UC1格式
            if (输入图像.type() === CvType.CV_8UC1) {
                灰度图像 = 输入图像.clone();
            } else {
                // 转换为CV_8UC1格式
                输入图像.convertTo(灰度图像, CvType.CV_8UC1);
            }
            console.log("  🔘 单通道图像已确保为CV_8UC1格式");
        } else {
            console.error("  ❌ 不支持的图像通道数: " + 输入图像.channels());
            return new ArrayList();
        }

        // 在灰度图像上查找轮廓
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();
        Imgproc.findContours(灰度图像, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

        console.log("  ✅ 轮廓检测完成，检测到 " + 轮廓列表.size() + " 个轮廓");

        // 释放资源
        灰度图像.release();
        层次结构.release();

        return 轮廓列表;
    } catch (e) {
        console.error("轮廓检测失败: " + e);
        return new ArrayList();
    }
}
// 已删除：轮廓分析函数 - 模板匹配功能已移除

// 已删除：加载模板轮廓函数 - 模板匹配功能已移除
/**
 * 保存智能调试图片 - 可视化处理效果，显示相似度信息和处理策略
 * @param {Mat} 原图 - 原始图像
 * @param {Mat} 处理图 - 处理后的图像
 * @param {List} 轮廓列表 - 检测到的轮廓
 * @param {String} 文件名 - 保存的文件名
 * @param {Array} 匹配信息列表 - 包含相似度、匹配状态、轮廓索引等信息的数组
 * @param {String} 策略名称 - 使用的处理策略名称
 * @param {Object} 背景特征 - 背景分析结果
 */
function 保存智能调试图片(原图, 处理图, 轮廓列表, 文件名, 匹配信息列表, 策略名称, 背景特征) {
    try {
        var 调试图像 = 原图.clone();

        // 先绘制所有轮廓为默认颜色（灰色）
        var 默认轮廓颜色 = new Scalar(128, 128, 128);  // 灰色轮廓
        var 线条粗细 = 2;
        Imgproc.drawContours(调试图像, 轮廓列表, -1, 默认轮廓颜色, 线条粗细);

        // 如果有匹配信息，在图片上显示相似度
        if (匹配信息列表 && 匹配信息列表.length > 0) {
            var 字体 = Imgproc.FONT_HERSHEY_SIMPLEX;
            var 字体大小 = 0.35;  // 进一步减小字体大小
            var 文字粗细 = 1;     // 保持文字粗细

            for (var i = 0; i < 匹配信息列表.length; i++) {
                var 匹配信息 = 匹配信息列表[i];
                if (匹配信息.中心点 && typeof 匹配信息.相似度 !== 'undefined') {
                    // 根据匹配状态绘制轮廓颜色
                    var 轮廓颜色 = 匹配信息.是否匹配 ? new Scalar(0, 255, 0) : new Scalar(0, 0, 255);  // 匹配=绿色，未匹配=红色
                    if (typeof 匹配信息.轮廓索引 !== 'undefined') {
                        Imgproc.drawContours(调试图像, 轮廓列表, 匹配信息.轮廓索引, 轮廓颜色, 线条粗细);
                    }

                    // 直接显示原始matchShapes相似度数值
                    var 相似度文本;
                    if (匹配信息.相似度 < 0.001) {
                        // 对于极小的科学计数法数值，使用科学计数法显示
                        相似度文本 = 匹配信息.相似度.toExponential(2);
                    } else {
                        // 对于正常数值，使用固定小数位显示
                        相似度文本 = 匹配信息.相似度.toFixed(4);
                    }

                    // 根据是否匹配使用不同颜色
                    var 当前文字颜色 = 匹配信息.是否匹配 ? new Scalar(0, 255, 0) : new Scalar(0, 0, 255);  // 匹配=绿色，未匹配=红色
                    var 当前圆圈颜色 = 匹配信息.是否匹配 ? new Scalar(0, 255, 0) : new Scalar(0, 0, 255);  // 匹配=绿色，未匹配=红色

                    // 将文字显示在轮廓中心点附近
                    var 文字位置 = new Point(
                        Math.max(5, 匹配信息.中心点.x - 10),  // 简单偏移
                        匹配信息.中心点.y + 20  // 显示在中心点下方
                    );

                    // 显示单一相似度数值
                    Imgproc.putText(调试图像, 相似度文本, 文字位置, 字体, 字体大小, 当前文字颜色, 文字粗细);

                    // 在中心点画一个小圆圈标记
                    var 中心点 = new Point(匹配信息.中心点.x, 匹配信息.中心点.y);
                    Imgproc.circle(调试图像, 中心点, 2, 当前圆圈颜色, -1);
                }
            }
        }

        // 在图片顶部显示智能检测信息
        var 信息文本 = "Strategy: " + (策略名称 || "Unknown") + " | Brightness: " + (背景特征 ? 背景特征.平均亮度 : "N/A");
        var 信息位置 = new Point(10, 25);

        // 第二行显示轮廓和阈值信息
        var 详细信息 = "Contours: " + 轮廓列表.size() + " | Threshold: " + 相似度阈值 + " | Type: " + (背景特征 ? 背景特征.背景类型 : "Unknown");
        var 详细信息位置 = new Point(10, 45);
        var 信息字体 = Imgproc.FONT_HERSHEY_SIMPLEX;
        var 信息字体大小 = 0.6;
        var 信息颜色 = new Scalar(255, 255, 0);  // 青色文字
        var 信息粗细 = 2;

        // 显示策略和背景信息
        Imgproc.putText(调试图像, 信息文本, 信息位置, 信息字体, 信息字体大小, 信息颜色, 信息粗细);

        // 显示详细检测信息
        Imgproc.putText(调试图像, 详细信息, 详细信息位置, 信息字体, 信息字体大小 * 0.8, 信息颜色, 1);

        // 保存到安卓存储目录 (添加时间戳避免覆盖)
        var 时间戳 = new Date().getTime();

        // 保存原图调试版本
        var 原图调试路径 = "/storage/emulated/0/Pictures/" + 文件名 + "_" + 时间戳 + "_原图调试.jpg";
        var 原图保存成功 = Imgcodecs.imwrite(原图调试路径, 调试图像);

        // 保存处理后的图像（如果提供）
        if (处理图) {
            var 处理图路径 = "/storage/emulated/0/Pictures/" + 文件名 + "_" + 时间戳 + "_处理图.jpg";
            var 处理图保存成功 = Imgcodecs.imwrite(处理图路径, 处理图);

            if (处理图保存成功) {
                console.log("处理图已保存: " + 处理图路径);
            }
        }

        if (原图保存成功) {
            console.log("调试图片已保存: " + 原图调试路径);
        } else {
            console.log("调试图片保存失败: " + 原图调试路径);
        }

        // 释放资源
        调试图像.release();

        return 原图调试路径;
    } catch (e) {
        console.error("保存调试图片失败: " + e);
        return null;
    }
}

// 已删除：获取目录图片函数 - 模板匹配功能已移除

/**
 * 判断是否为目标轮廓（X号、播放号、>>号等）
 * 根据轮廓的形状特征判断是否为需要保护的目标轮廓类型
 * @param {Object} 轮廓信息 - 轮廓信息对象
 * @returns {Boolean} - 是否为目标轮廓
 */
function 判断是否为目标轮廓(轮廓信息) {
    try {
        console.log("    🔍 开始判断轮廓类型...");

        if (!轮廓信息 || !轮廓信息.尺寸) {
            console.log("    ❌ 轮廓信息无效");
            return false;
        }

        var 宽度 = 轮廓信息.尺寸.宽度;
        var 高度 = 轮廓信息.尺寸.高度;
        var 面积 = 轮廓信息.面积 || (宽度 * 高度);

        console.log("    📊 轮廓基本信息：" + 宽度 + "×" + 高度 + "，面积：" + 面积);

        // 🎯 大幅放宽基本特征判断，保留所有可能的目标轮廓
        var 宽高比 = 宽度 / 高度;
        var 是合理宽高比 = 宽高比 > 0.1 && 宽高比 < 10.0;  // 进一步扩大范围：0.2-5.0 → 0.1-10.0

        // 大幅降低面积要求，保留小轮廓
        var 有合理面积 = 面积 > 20;  // 进一步降低：50 → 20像素

        // 大幅降低尺寸要求，保留小轮廓
        var 有合理尺寸 = 宽度 > 3 && 高度 > 3;  // 进一步降低：8 → 3像素

        // 检查形状描述（支持多种轮廓类型）
        var 形状描述 = 轮廓信息.形状 || "";
        console.log("    📝 形状描述：'" + 形状描述 + "'");

        var 是X号轮廓 = 形状描述.indexOf("X") !== -1 ||
                      形状描述.indexOf("交叉") !== -1 ||
                      形状描述.indexOf("叉") !== -1;

        var 是播放号轮廓 = 形状描述.indexOf("播放") !== -1 ||
                        形状描述.indexOf("三角") !== -1 ||
                        形状描述.indexOf("▶") !== -1;

        var 是箭头轮廓 = 形状描述.indexOf(">>") !== -1 ||
                      形状描述.indexOf("》") !== -1 ||
                      形状描述.indexOf("箭头") !== -1 ||
                      形状描述.indexOf("右") !== -1;

        var 是目标形状 = 是X号轮廓 || 是播放号轮廓 || 是箭头轮廓;

        console.log("    🎯 轮廓类型判断：X号=" + 是X号轮廓 + "，播放号=" + 是播放号轮廓 + "，箭头=" + 是箭头轮廓 + "，是目标=" + 是目标形状);

        // 综合判断：满足基本特征且是目标形状
        var 基本特征符合 = 是合理宽高比 && 有合理面积 && 有合理尺寸;

        // 如果有明确的形状描述，优先使用形状描述
        if (形状描述) {
            if (是目标形状) {
                console.log("    🎯 识别为目标轮廓：" + 形状描述 + "（" + 宽度 + "×" + 高度 + "，面积：" + 面积 + "）");
                return 基本特征符合;
            } else {
                console.log("    ⏭️ 非目标轮廓：" + 形状描述 + "（" + 宽度 + "×" + 高度 + "，面积：" + 面积 + "）");
                return false;
            }
        }

        // 🎯 如果没有形状描述，采用宽松策略保留所有轮廓
        console.log("    ⚠️ 无形状描述，采用宽松策略：" + 基本特征符合);
        console.log("    📊 基本特征：宽高比=" + 是合理宽高比 + "，面积=" + 有合理面积 + "，尺寸=" + 有合理尺寸);

        // 🛡️ 宽松策略：只要满足最基本的尺寸要求就保留
        var 宽松判断 = 宽度 > 3 && 高度 > 3 && 面积 > 20;
        console.log("    🛡️ 宽松策略判断：" + 宽松判断 + "（宽度>3，高度>3，面积>20）");

        return 宽松判断;

    } catch (e) {
        console.error("判断目标轮廓失败: " + e);
        return false;  // 出错时保守处理，不认为是目标轮廓
    }
}

/**
 * 过滤目标轮廓附近的多余线条
 * 专门用于清理目标轮廓（X号、播放号、>>号等）周围的干扰线条，保持目标轮廓的清晰度
 * @param {Mat} 输入图像 - 需要处理的图像
 * @param {Array} 轮廓信息列表 - 检测到的目标轮廓信息
 * @param {Number} 过滤半径 - 轮廓周围的过滤范围，默认30像素
 * @returns {Mat} - 过滤后的图像
 */
function 过滤轮廓附近线条(输入图像, 轮廓信息列表, 过滤半径) {
    try {
        // 默认参数
        if (typeof 过滤半径 === 'undefined') {
            过滤半径 = 30;
        }

        if (!轮廓信息列表 || 轮廓信息列表.length === 0) {
            console.log("  ⚠️ 无轮廓信息，跳过线条过滤");
            return 输入图像.clone();
        }

        console.log("  🧹 开始过滤轮廓附近的多余线条...");
        console.log("  📊 目标轮廓数量: " + 轮廓信息列表.length + "，过滤半径: " + 过滤半径 + "像素");

        // 创建输出图像
        var 过滤图像 = 输入图像.clone();

        // 转换为灰度图进行处理
        var 灰度图 = new Mat();
        if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, 灰度图, Imgproc.COLOR_BGR2GRAY);
        } else {
            灰度图 = 输入图像.clone();
        }

        // 遍历每个轮廓，清理其周围的线条
        for (var i = 0; i < 轮廓信息列表.length; i++) {
            var 轮廓信息 = 轮廓信息列表[i];
            var 中心点 = 轮廓信息.中心点;
            var 轮廓尺寸 = 轮廓信息.尺寸;

            console.log("  🎯 处理轮廓 " + (i + 1) + " 中心点: (" + 中心点.x + ", " + 中心点.y + ")");

            // 定义轮廓周围的处理区域
            var 扩展半径 = Math.max(过滤半径, Math.max(轮廓尺寸.宽度, 轮廓尺寸.高度));
            var 处理区域 = {
                x: Math.max(0, 中心点.x - 扩展半径),
                y: Math.max(0, 中心点.y - 扩展半径),
                width: Math.min(灰度图.cols() - Math.max(0, 中心点.x - 扩展半径), 扩展半径 * 2),
                height: Math.min(灰度图.rows() - Math.max(0, 中心点.y - 扩展半径), 扩展半径 * 2)
            };

            // 裁剪处理区域
            var 区域矩形 = new Rect(处理区域.x, 处理区域.y, 处理区域.width, 处理区域.height);
            var 区域图像 = new Mat(灰度图, 区域矩形);

            // 执行线条过滤处理
            var 清理后区域 = 执行区域线条清理(区域图像, 轮廓信息, 扩展半径);

            // 将清理后的区域复制回原图
            if (清理后区域) {
                if (过滤图像.channels() === 3) {
                    // 彩色图像处理
                    var 彩色区域 = new Mat();
                    Imgproc.cvtColor(清理后区域, 彩色区域, Imgproc.COLOR_GRAY2BGR);
                    彩色区域.copyTo(过滤图像.submat(区域矩形));
                    彩色区域.release();
                } else {
                    // 灰度图像处理
                    清理后区域.copyTo(过滤图像.submat(区域矩形));
                }
                清理后区域.release();
            }

            区域图像.release();
        }

        // 释放资源
        灰度图.release();

        console.log("  ✅ 轮廓附近线条过滤完成");
        return 过滤图像;

    } catch (e) {
        console.error("过滤轮廓附近线条失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 执行区域内的线条清理处理
 * 使用多种算法清理轮廓周围的干扰线条
 * @param {Mat} 区域图像 - 需要清理的区域图像
 * @param {Object} 轮廓信息 - 目标轮廓信息
 * @param {Number} 处理半径 - 处理范围半径
 * @returns {Mat} - 清理后的区域图像
 */
function 执行区域线条清理(区域图像, 轮廓信息, 处理半径) {
    try {
        var 清理图像 = 区域图像.clone();

        // 策略1: 针对小线条的精准形态学清理
        console.log("    🔧 策略1: 针对小线条的精准形态学清理");

        // 1.1: 轻度开运算 - 去除极小噪点
        var 轻度核 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(1, 1));
        Imgproc.morphologyEx(清理图像, 清理图像, Imgproc.MORPH_OPEN, 轻度核);
        轻度核.release();

        // 1.2: 针对性线条清理 - 专门处理细小线条
        var 水平核 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(3, 1));  // 水平线条
        var 垂直核 = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(1, 3));  // 垂直线条

        // 轻度清理水平和垂直细线
        Imgproc.morphologyEx(清理图像, 清理图像, Imgproc.MORPH_OPEN, 水平核);
        Imgproc.morphologyEx(清理图像, 清理图像, Imgproc.MORPH_OPEN, 垂直核);

        水平核.release();
        垂直核.release();

        // 策略2: 线条检测和移除
        console.log("    🔧 策略2: 检测并移除干扰线条");
        var 线条清理图像 = 移除干扰线条(清理图像, 轮廓信息);
        if (线条清理图像) {
            清理图像.release();
            清理图像 = 线条清理图像;
        }

        // 策略3: 轮廓保护性清理
        console.log("    🔧 策略3: 轮廓保护性清理");
        var 保护清理图像 = 轮廓保护性清理(清理图像, 轮廓信息, 处理半径);
        if (保护清理图像) {
            清理图像.release();
            清理图像 = 保护清理图像;
        }

        return 清理图像;

    } catch (e) {
        console.error("    ❌ 区域线条清理失败: " + e);
        return 区域图像.clone();
    }
}

/**
 * 移除干扰线条
 * 使用霍夫直线检测识别并移除干扰线条
 * @param {Mat} 输入图像 - 需要处理的图像
 * @param {Object} 轮廓信息 - 目标轮廓信息
 * @returns {Mat} - 处理后的图像
 */
function 移除干扰线条(输入图像, 轮廓信息) {
    try {
        var 处理图像 = 输入图像.clone();

        // � 适中级边缘检测 - 降低两级敏感度
        var 边缘图 = new Mat();
        Imgproc.Canny(输入图像, 边缘图, 60, 180);  // 提高阈值：30,120 → 60,180

        // � 适中级霍夫直线检测 - 降低两级敏感度
        var 直线列表 = new Mat();
        // 参数调整：threshold=25(提高), minLineLength=15(提高), maxLineGap=8(提高)
        Imgproc.HoughLinesP(边缘图, 直线列表, 1, Math.PI / 180, 25, 15, 8);

        console.log("      📏 检测到 " + 直线列表.rows() + " 条直线");

        // 分析并移除干扰线条
        if (直线列表.rows() > 0) {
            var 目标中心 = 轮廓信息.中心点;
            var 目标尺寸 = 轮廓信息.尺寸;

            for (var i = 0; i < 直线列表.rows(); i++) {
                var 直线数据 = 直线列表.get(i, 0);
                var x1 = 直线数据[0], y1 = 直线数据[1];
                var x2 = 直线数据[2], y2 = 直线数据[3];

                // 计算直线长度和角度
                var 长度 = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                var 角度 = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                // 判断是否为干扰线条
                var 是干扰线条 = 判断是否为干扰线条(x1, y1, x2, y2, 长度, 角度, 目标中心, 目标尺寸);

                if (是干扰线条) {
                    // 移除干扰线条 - 在线条位置绘制黑色
                    Imgproc.line(处理图像, new Point(x1, y1), new Point(x2, y2), new Scalar(0), 2);
                    console.log("      🗑️ 移除干扰线条: (" + x1 + "," + y1 + ") → (" + x2 + "," + y2 + ") 长度:" + Math.round(长度));
                }
            }
        }

        // 释放资源
        边缘图.release();
        直线列表.release();

        return 处理图像;

    } catch (e) {
        console.error("      ❌ 移除干扰线条失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 判断是否为干扰线条
 * 根据线条特征判断是否会干扰目标轮廓
 * @param {Number} x1, y1, x2, y2 - 线条端点坐标
 * @param {Number} 长度 - 线条长度
 * @param {Number} 角度 - 线条角度
 * @param {Object} 目标中心 - 目标轮廓中心点
 * @param {Object} 目标尺寸 - 目标轮廓尺寸
 * @returns {Boolean} - 是否为干扰线条
 */
function 判断是否为干扰线条(x1, y1, x2, y2, 长度, 角度, 目标中心, 目标尺寸) {
    try {
        // 计算线条中心点
        var 线条中心x = (x1 + x2) / 2;
        var 线条中心y = (y1 + y2) / 2;

        // 计算线条到目标中心的距离
        var 距离目标 = Math.sqrt(Math.pow(线条中心x - 目标中心.x, 2) + Math.pow(线条中心y - 目标中心.y, 2));

        // �️ 首先检查是否为X号轮廓的组成部分 - 保护目标轮廓不被误删
        var 是目标轮廓组成 = 检查是否为目标轮廓组成(x1, y1, x2, y2, 目标中心, 目标尺寸);
        if (是目标轮廓组成) {
            console.log("        🛡️ 保护目标轮廓线条: (" + x1 + "," + y1 + ") → (" + x2 + "," + y2 + ")");
            return false;  // 不过滤目标轮廓的线条
        }

        // � 适中级判断条件 - 降低两级过滤强度
        // 清理未使用的旧变量，使用新的分级过滤策略

        // 🎯 针对小线条的分级过滤策略
        var 目标最大尺寸 = Math.max(目标尺寸.宽度, 目标尺寸.高度);

        // 分类判断不同类型的干扰线条
        var 是极小线条 = 长度 < 15;  // 极小线条（<15像素）
        var 是小线条 = 长度 >= 15 && 长度 < 25;  // 小线条（15-25像素）

        // 不同距离的判断
        var 非常接近 = 距离目标 < 目标最大尺寸 * 0.6;  // 非常接近
        var 比较接近 = 距离目标 < 目标最大尺寸 * 1.0;  // 比较接近
        var 稍微接近 = 距离目标 < 目标最大尺寸 * 1.5;  // 稍微接近

        // 🎯 分级过滤策略 - 针对不同大小的线条使用不同标准
        var 过滤极小线条 = 是极小线条 && 稍微接近;  // 极小线条：范围较宽
        var 过滤小线条 = 是小线条 && 比较接近;      // 小线条：中等范围

        // 🚀 额外的角度判断 - 过滤明显的杂乱线条
        var 角度 = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
        var 是杂乱角度 = (Math.abs(角度) > 15 && Math.abs(角度) < 75) ||
                       (Math.abs(角度) > 105 && Math.abs(角度) < 165);  // 斜线角度
        var 角度过滤 = 是杂乱角度 && (是极小线条 || 是小线条) && 比较接近;

        // 最终判断：满足任一过滤条件（更宽松的OR逻辑）
        return 过滤极小线条 || 过滤小线条 || 角度过滤;

    } catch (e) {
        console.error("        ❌ 判断干扰线条失败: " + e);
        return false;
    }
}

/**
 * 检查是否为目标轮廓的组成部分
 * 判断线条是否属于X号轮廓本身，避免误删目标轮廓
 * @param {Number} x1, y1, x2, y2 - 线条端点坐标
 * @param {Object} 目标中心 - 目标轮廓中心点
 * @param {Object} 目标尺寸 - 目标轮廓尺寸
 * @returns {Boolean} - 是否为目标轮廓组成部分
 */
function 检查是否为目标轮廓组成(x1, y1, x2, y2, 目标中心, 目标尺寸) {
    try {
        // 计算线条的边界框
        var 线条左边界 = Math.min(x1, x2);
        var 线条右边界 = Math.max(x1, x2);
        var 线条上边界 = Math.min(y1, y2);
        var 线条下边界 = Math.max(y1, y2);

        // 计算目标轮廓的边界框（稍微扩大一点容忍度）
        var 容忍度 = 3;  // 3像素的容忍度
        var 目标左边界 = 目标中心.x - 目标尺寸.宽度 / 2 - 容忍度;
        var 目标右边界 = 目标中心.x + 目标尺寸.宽度 / 2 + 容忍度;
        var 目标上边界 = 目标中心.y - 目标尺寸.高度 / 2 - 容忍度;
        var 目标下边界 = 目标中心.y + 目标尺寸.高度 / 2 + 容忍度;

        // 检查线条是否完全在目标轮廓范围内
        var 线条在目标范围内 = 线条左边界 >= 目标左边界 &&
                           线条右边界 <= 目标右边界 &&
                           线条上边界 >= 目标上边界 &&
                           线条下边界 <= 目标下边界;

        // 计算线条长度和方向
        var 线条长度 = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        var 线条角度 = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

        // 检查是否为主要结构线条（长度接近目标尺寸）
        var 最小目标尺寸 = Math.min(目标尺寸.宽度, 目标尺寸.高度);
        var 最大目标尺寸 = Math.max(目标尺寸.宽度, 目标尺寸.高度);
        var 是主要结构 = 线条长度 > 最小目标尺寸 * 0.4;  // 长度超过较小尺寸的40%

        // 综合判断：在目标范围内且为主要结构的线条认为是目标轮廓组成
        return 线条在目标范围内 && 是主要结构;

    } catch (e) {
        console.error("        ❌ 检查目标轮廓组成失败: " + e);
        return false;  // 出错时保守处理，不认为是目标轮廓
    }
}

/**
 * 轮廓保护性清理
 * 在保护目标轮廓的前提下清理周围杂乱，同时保护指定的前景色不被过滤
 * @param {Mat} 输入图像 - 需要处理的图像
 * @param {Object} 轮廓信息 - 目标轮廓信息
 * @param {Number} 保护半径 - 保护范围半径
 * @returns {Mat} - 处理后的图像
 */
function 轮廓保护性清理(输入图像, 轮廓信息, 保护半径) {
    try {
        var 清理图像 = 输入图像.clone();

        // 🛡️ 创建综合保护掩码 - 保护目标轮廓区域和前景色
        var 保护掩码 = Mat.zeros(输入图像.size(), CvType.CV_8UC1);
        var 目标中心 = 轮廓信息.中心点;
        var 目标尺寸 = 轮廓信息.尺寸;

        console.log("      🎨 开始创建前景色保护掩码...");

        // 🎨 创建前景色保护掩码 - 保护 #3C3C3C 颜色
        var 前景色保护掩码 = 创建前景色保护掩码(输入图像);

        // 将前景色保护掩码合并到总保护掩码中
        Core.bitwise_or(保护掩码, 前景色保护掩码, 保护掩码);

        // 在目标轮廓周围创建保护区域
        var 保护区域 = new Rect(
            Math.max(0, 目标中心.x - 目标尺寸.宽度),
            Math.max(0, 目标中心.y - 目标尺寸.高度),
            Math.min(输入图像.cols() - Math.max(0, 目标中心.x - 目标尺寸.宽度), 目标尺寸.宽度 * 2),
            Math.min(输入图像.rows() - Math.max(0, 目标中心.y - 目标尺寸.高度), 目标尺寸.高度 * 2)
        );

        // 在保护区域外进行更激进的清理
        Imgproc.rectangle(保护掩码, 保护区域.tl(), 保护区域.br(), new Scalar(255), -1);

        // 对保护区域外的部分进行形态学闭运算，去除杂乱
        var 清理核 = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(3, 3));
        var 保护区域外 = new Mat();
        Core.bitwise_not(保护掩码, 保护区域外);

        // 在保护区域外应用更强的清理
        var 强清理图像 = new Mat();
        Imgproc.morphologyEx(清理图像, 强清理图像, Imgproc.MORPH_CLOSE, 清理核);

        // 将强清理结果应用到保护区域外
        强清理图像.copyTo(清理图像, 保护区域外);

        // 释放资源
        保护掩码.release();
        前景色保护掩码.release();
        保护区域外.release();
        强清理图像.release();
        清理核.release();

        console.log("      ✅ 轮廓保护性清理完成（包含前景色保护）");
        return 清理图像;

    } catch (e) {
        console.error("      ❌ 轮廓保护性清理失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 创建前景色保护掩码
 * 保护指定的前景色（#3C3C3C）不被过滤掉
 * @param {Mat} 输入图像 - 需要分析的图像
 * @returns {Mat} - 前景色保护掩码
 */
function 创建前景色保护掩码(输入图像) {
    try {
        var 前景色掩码 = new Mat();

        // #3C3C3C 转换为 BGR 值：R=60, G=60, B=60
        var 目标颜色_B = 60;  // Blue
        var 目标颜色_G = 60;  // Green
        var 目标颜色_R = 60;  // Red

        // 🎨 扩大颜色容忍度，确保能检测到#3C3C3C及其变体
        var 容忍度 = 30;  // 扩大容忍度：±15 → ±30

        if (输入图像.channels() === 3) {
            // 彩色图像处理
            var 下限 = new Scalar(目标颜色_B - 容忍度, 目标颜色_G - 容忍度, 目标颜色_R - 容忍度);
            var 上限 = new Scalar(目标颜色_B + 容忍度, 目标颜色_G + 容忍度, 目标颜色_R + 容忍度);

            // 创建颜色范围掩码
            Core.inRange(输入图像, 下限, 上限, 前景色掩码);

            console.log("      🎨 创建彩色前景色保护掩码：#3C3C3C ±" + 容忍度);

        } else {
            // 灰度图像处理 - 将RGB转换为灰度值
            var 目标灰度 = Math.round(0.299 * 目标颜色_R + 0.587 * 目标颜色_G + 0.114 * 目标颜色_B);  // 约为60

            // 创建灰度范围掩码
            var 下限灰度 = new Scalar(目标灰度 - 容忍度);
            var 上限灰度 = new Scalar(目标灰度 + 容忍度);

            Core.inRange(输入图像, 下限灰度, 上限灰度, 前景色掩码);

            console.log("      🎨 创建灰度前景色保护掩码：灰度值" + 目标灰度 + " ±" + 容忍度);
        }

        // 统计保护的像素数量
        var 保护像素数 = Core.countNonZero(前景色掩码);
        console.log("      📊 前景色保护像素数量：" + 保护像素数);

        return 前景色掩码;

    } catch (e) {
        console.error("      ❌ 创建前景色保护掩码失败: " + e);
        return Mat.zeros(输入图像.size(), CvType.CV_8UC1);
    }
}
/**
 * 主要功能：看广告 - 单函数多接口通用设计
 * 完整流程：截图 → 区域裁剪 → 图像预处理 → 轮廓检测 → 模板匹配 → 结果分析
 * @param {String} 指定区域 - 可选："close区域"、"右广告"、"左广告"，不指定则检测所有区域
 * @param {Boolean} 保存调试 - 是否保存调试图片，默认true
 * @returns {Object} - 检测结果，包含位置、相似度等信息
 */
function 看广告(指定区域, 保存调试) {
    console.log("🔍 开始看广告检测...");

    // 检查屏幕捕获权限
    if (!images.requestScreenCapture()) {
        console.log("❌ 屏幕捕获权限获取失败");
        return null;
    }

    console.log("⏰ 等待5秒后开始检测...");
    sleep(5000);  // 延时5秒
    console.log("🚀 开始执行检测...");

    // 默认参数处理
    if (typeof 保存调试 === 'undefined') {
        保存调试 = true;
    }

    // 截图 - 获取当前屏幕画面
    console.log("📸 正在捕获屏幕画面...");
    var 屏幕截图 = images.captureScreen();
    if (!屏幕截图) {
        console.log("❌ 截图失败");
        return null;
    }
    console.log("✅ 屏幕截图成功");

    var 屏幕Mat = 屏幕截图.getMat();
    // 检测结果变量已移除 - 模板匹配功能已禁用

    // 确定要检测的区域
    var 待检测区域 = [];
    if (指定区域 && 检测区域配置[指定区域]) {
        待检测区域.push(指定区域);
    } else {
        待检测区域 = Object.keys(检测区域配置);
    }

    // 遍历每个检测区域
    for (var i = 0; i < 待检测区域.length; i++) {
        var 区域名称 = 待检测区域[i];
        var 区域配置 = 检测区域配置[区域名称];

        console.log("📋 检测区域: " + 区域配置.名称);

        // 裁剪区域图像
        var 区域坐标 = 区域配置.区域;
        var 区域矩形 = new Rect(区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);
        var 区域图像 = new Mat(屏幕Mat, 区域矩形);

        // 🎯 预分析背景颜色，决定是否需要白色过滤
        // console.log("🔍 预分析背景颜色，判断是否需要白色过滤...");
        var 预分析结果 = 快速分析背景颜色(区域图像);
        var 需要白色过滤 = 预分析结果.颜色名称.indexOf("白色") === -1 &&
                          预分析结果.颜色名称.indexOf("灰色") === -1 &&
                          预分析结果.颜色名称.indexOf("黑色") === -1;

        var 过滤后区域图像;
        if (需要白色过滤) {
            // console.log("🎯 检测到彩色背景(" + 预分析结果.颜色名称 + ")，执行白色过滤...");
            var 白色过滤图像 = 执行白色过滤(区域图像);
            过滤后区域图像 = 白色过滤图像 || 区域图像;
            // console.log("✅ 白色过滤完成，使用过滤后图像进行后续处理");
        } else {
            // console.log("⏭️ 检测到单色背景(" + 预分析结果.颜色名称 + ")，跳过白色过滤");
            过滤后区域图像 = 区域图像;
        }

        // 🧹 第一步：使用原始区域图像进行初步轮廓分析，避免白色过滤影响轮廓检测
        console.log("🔍 使用原始区域图像进行初步轮廓分析...");
        console.log("  ⚠️ 重要：使用原始图像避免白色过滤导致轮廓丢失");
        var 初步背景特征 = 分析背景特征(区域图像, true, "轮廓保护", 区域坐标);  // 使用原始区域图像，避免白色过滤影响

        // 🌟 保存第一次边缘检测的轮廓信息
        var 初步边缘轮廓信息 = 初步背景特征.边缘轮廓信息 || [];

        // 🧹 第二步：在背景转换前过滤轮廓附近的多余线条（仅对目标轮廓）
        console.log("🔍 开始目标轮廓检测和线条过滤...");
        var 最终处理图像 = 过滤后区域图像;

        if (初步背景特征.轮廓信息 && 初步背景特征.轮廓信息.length > 0) {
            console.log("📊 从原始图像检测到 " + 初步背景特征.轮廓信息.length + " 个轮廓");

            // 🎯 检查是否包含目标轮廓（X号、播放号、>>号等）
            var 包含目标轮廓 = false;
            var 目标轮廓列表 = [];

            for (var i = 0; i < 初步背景特征.轮廓信息.length; i++) {
                var 轮廓信息 = 初步背景特征.轮廓信息[i];
                console.log("  🔍 分析轮廓 " + (i + 1) + "：" + 轮廓信息.尺寸.宽度 + "×" + 轮廓信息.尺寸.高度 + "，面积：" + 轮廓信息.面积);

                if (判断是否为目标轮廓(轮廓信息)) {
                    包含目标轮廓 = true;
                    目标轮廓列表.push(轮廓信息);
                    console.log("    🎯 ✅ 识别为目标轮廓");
                } else {
                    console.log("    ⏭️ ❌ 跳过非目标轮廓");
                }
            }

            if (包含目标轮廓 && 目标轮廓列表.length > 0) {
                console.log("🧹 检测到 " + 目标轮廓列表.length + " 个目标轮廓，开始线条过滤...");
                // 💡 重要：对白色过滤后的图像进行线条过滤，但使用原始图像检测到的轮廓信息
                var 线条过滤图像 = 过滤轮廓附近线条(过滤后区域图像, 目标轮廓列表, 20);
                if (线条过滤图像) {
                    console.log("✅ 目标轮廓线条过滤完成，使用过滤后图像重新分析背景");
                    最终处理图像 = 线条过滤图像;
                }
            } else {
                console.log("ℹ️ 未检测到目标轮廓，跳过线条过滤处理");
            }
        } else {
            console.log("⚠️ 原始图像未检测到任何轮廓");
        }

        // 🎨 第三步：使用过滤后的图像重新进行背景颜色检测和转换
        console.log("🔍 使用过滤后图像重新分析背景特征...");
        var 背景特征 = 分析背景特征(最终处理图像, true, "轮廓保护", 区域坐标);  // 启用轮廓保护模式

        // 🌟 合并两次边缘检测的轮廓信息
        var 合并边缘轮廓信息 = [];
        if (初步边缘轮廓信息 && 初步边缘轮廓信息.length > 0) {
            console.log("  🌟 合并初步边缘检测轮廓信息: " + 初步边缘轮廓信息.length + " 个");
            合并边缘轮廓信息 = 合并边缘轮廓信息.concat(初步边缘轮廓信息);
        }
        if (背景特征.边缘轮廓信息 && 背景特征.边缘轮廓信息.length > 0) {
            console.log("  🌟 合并最终边缘检测轮廓信息: " + 背景特征.边缘轮廓信息.length + " 个");
            合并边缘轮廓信息 = 合并边缘轮廓信息.concat(背景特征.边缘轮廓信息);
        }

        // 🎯 输出轮廓保护的全屏坐标信息（包含合并的边缘检测轮廓）
        if (背景特征.轮廓信息 && 背景特征.轮廓信息.length > 0) {
            输出全屏轮廓信息(背景特征.轮廓信息, 区域坐标, 区域配置.名称, 合并边缘轮廓信息);
        }

        // 🎯 智能图像预处理 - 根据背景色选择最佳处理策略
        var 源图像 = 背景特征.处理后图像 || 最终处理图像;
        var 预处理图像 = 智能颜色增强预处理(源图像, 背景特征);
        if (!预处理图像) {
            if (背景特征.处理后图像) {
                背景特征.处理后图像.release();
            }
            区域图像.release();
            continue;
        }

        // 轮廓检测
        var 目标轮廓列表 = 轮廓检测(预处理图像);

        // 模板匹配功能已移除 - 仅保留轮廓检测和信息输出
        // 存储当前区域的轮廓信息（用于调试输出）
        var 当前区域匹配信息 = [];

        // 保存调试图片（包含相似度信息和背景特征）
        if (保存调试) {
            // 🚀 使用线条过滤后的最终处理图像
            var 调试处理图像 = 最终处理图像 || 过滤后区域图像;  // 优先使用线条过滤后的图像
            var 处理策略名称 = 最终处理图像 ? "线条过滤+背景转换+轮廓检测" : "白色过滤+轮廓检测";
            var 文件后缀 = 最终处理图像 ? "_线条过滤" : "_白色过滤";
            保存智能调试图片(区域图像, 调试处理图像, 目标轮廓列表, 区域配置.名称 + 文件后缀, 当前区域匹配信息, 处理策略名称, 背景特征);
        }

        // 释放目标轮廓资源
        for (var n = 0; n < 目标轮廓列表.size(); n++) {
            目标轮廓列表.get(n).release();
        }

        // 释放区域图像资源
        预处理图像.release();
        if (背景特征.处理后图像) {
            背景特征.处理后图像.release();
        }
        if (需要白色过滤 && 白色过滤图像 && 白色过滤图像 !== 区域图像) {
            白色过滤图像.release();
        }
        if (最终处理图像 && 最终处理图像 !== 过滤后区域图像 && 最终处理图像 !== 区域图像) {
            最终处理图像.release();
        }
        区域图像.release();
    }

    // 释放屏幕截图资源
    屏幕Mat.release();
    屏幕截图.recycle();

    // 函数正常结束，无返回值
    return;
}



/**
 * 执行白色过滤，保留白色区域和重要前景轮廓
 * @param {Mat} 输入图像 - 输入的图像（支持灰度或彩色）
 * @returns {Mat} - 白色过滤后的图像
 */
function 执行白色过滤(输入图像) {
    try {
        console.log("  🎯 开始白色过滤处理（包含前景轮廓保护）...");
        console.log("  📊 输入图像信息: " + 输入图像.channels() + "通道, 尺寸: " + 输入图像.size());

        var 白色掩码;
        var 前景轮廓掩码;
        var 过滤图像;

        if (输入图像.channels() === 1) {
            // 灰度图像处理
            console.log("  📝 处理灰度图像...");

            // 创建输出图像
            过滤图像 = new Mat.zeros(输入图像.size(), CvType.CV_8UC1);

            // 对于灰度图像，直接使用阈值检测白色区域（更宽松的阈值）
            白色掩码 = new Mat();
            Imgproc.threshold(输入图像, 白色掩码, 160, 255, Imgproc.THRESH_BINARY);

        } else if (输入图像.channels() === 3 || 输入图像.channels() === 4) {
            // 彩色图像处理
            console.log("  📝 处理彩色图像...");

            // 创建输出图像
            过滤图像 = new Mat.zeros(输入图像.size(), 输入图像.type());

            // 转换为HSV色彩空间进行精确的白色检测
            var hsv图像 = new Mat();
            if (输入图像.channels() === 4) {
                // 4通道图像，先转换为3通道再转HSV
                var bgr图像 = new Mat();
                Imgproc.cvtColor(输入图像, bgr图像, Imgproc.COLOR_BGRA2BGR);
                Imgproc.cvtColor(bgr图像, hsv图像, Imgproc.COLOR_BGR2HSV);
                bgr图像.release();
            } else {
                // 3通道图像，直接转HSV
                Imgproc.cvtColor(输入图像, hsv图像, Imgproc.COLOR_BGR2HSV);
            }

            // 定义白色的HSV范围（更宽松的白色检测，保留更多白色区域）
            var 白色下限 = new Scalar(0, 0, 160);      // H: 0-180, S: 0-50, V: 160-255
            var 白色上限 = new Scalar(160, 50, 255);

            // 创建白色掩码
            白色掩码 = new Mat();
            Core.inRange(hsv图像, 白色下限, 白色上限, 白色掩码);

            // 释放HSV图像
            hsv图像.release();

        } else {
            console.log("  ⚠️ 不支持的图像通道数: " + 输入图像.channels());
            return 输入图像.clone();
        }

        // 🎨 创建前景轮廓保护掩码 - 保护重要的暗色轮廓
        console.log("  🛡️ 创建前景轮廓保护掩码...");
        前景轮廓掩码 = 创建前景色保护掩码(输入图像);

        // 统计前景轮廓像素数量
        var 前景轮廓像素数 = Core.countNonZero(前景轮廓掩码);
        console.log("  📊 检测到前景轮廓像素：" + 前景轮廓像素数 + " 个");

        // 🔗 合并白色掩码和前景轮廓掩码
        var 综合掩码 = new Mat();
        Core.bitwise_or(白色掩码, 前景轮廓掩码, 综合掩码);

        // 形态学操作去除噪点（使用更小的核心，保留更多细节）
        var 形态核 = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(2, 2));
        Imgproc.morphologyEx(综合掩码, 综合掩码, Imgproc.MORPH_OPEN, 形态核);
        // 跳过闭运算，避免过度填充

        // 将白色区域和前景轮廓复制到输出图像
        输入图像.copyTo(过滤图像, 综合掩码);

        // 🎨 对前景轮廓区域进行颜色增强，确保可见性
        if (前景轮廓像素数 > 0) {
            console.log("  ✨ 对前景轮廓进行颜色增强...");

            // 将前景轮廓区域设置为白色，确保在后续处理中可见
            if (输入图像.channels() === 3) {
                过滤图像.setTo(new Scalar(255, 255, 255), 前景轮廓掩码);
            } else {
                过滤图像.setTo(new Scalar(255), 前景轮廓掩码);
            }

            console.log("  ✅ 已将 " + 前景轮廓像素数 + " 个前景轮廓像素增强为白色");
        }

        // 释放临时掩码
        综合掩码.release();

        // 💾 保存白色过滤处理图（包含前景轮廓保护）
        try {
            var 时间戳 = new Date().getTime();
            var 保存路径 = "/storage/emulated/0/Pictures/右广告_白色过滤_" + 时间戳 + "_处理图.jpg";
            Imgcodecs.imwrite(保存路径, 过滤图像);
            console.log("  💾 白色过滤处理图已保存: " + 保存路径);
        } catch (saveError) {
            console.error("  ❌ 保存白色过滤处理图失败: " + saveError);
        }

        // 统计白色像素数量
        var 白色像素数 = Core.countNonZero(白色掩码);
        var 总像素数 = 输入图像.total();
        var 白色比例 = (白色像素数 / 总像素数 * 100).toFixed(2);

        // console.log("  📊 白色过滤统计:");
        // console.log("    🔍 检测到白色像素: " + 白色像素数 + " 个");
        // console.log("    📏 总像素数: " + 总像素数 + " 个");
        // console.log("    📈 白色占比: " + 白色比例 + "%");

        // 释放资源
        白色掩码.release();
        前景轮廓掩码.release();
        形态核.release();

        if (白色像素数 > 0) {
            // console.log("  ✅ 白色过滤成功，保留了 " + 白色像素数 + " 个白色像素");
            return 过滤图像;
        } else {
            // console.log("  ⚠️ 未检测到白色区域，返回原图像");
            过滤图像.release();
            return 输入图像.clone();
        }

    } catch (e) {
        console.error("  ❌ 白色过滤失败: " + e);
        return 输入图像.clone();
    }
}

/**
 * 快速分析背景颜色，用于判断是否需要白色过滤
 * @param {Mat} 输入图像 - 输入的区域图像
 * @returns {Object} - 包含颜色名称的分析结果
 */
function 快速分析背景颜色(输入图像) {
    try {
        console.log("  🔍 快速分析背景颜色...");

        // 转换为HSV色彩空间
        var hsv图像 = new Mat();
        if (输入图像.channels() === 4) {
            var bgr图像 = new Mat();
            Imgproc.cvtColor(输入图像, bgr图像, Imgproc.COLOR_BGRA2BGR);
            Imgproc.cvtColor(bgr图像, hsv图像, Imgproc.COLOR_BGR2HSV);
            bgr图像.release();
        } else if (输入图像.channels() === 3) {
            Imgproc.cvtColor(输入图像, hsv图像, Imgproc.COLOR_BGR2HSV);
        } else {
            // 灰度图像，直接分析亮度
            var 平均亮度 = Core.mean(输入图像).val[0];
            hsv图像.release();

            if (平均亮度 > 200) {
                return { 颜色名称: "白色系" };
            } else if (平均亮度 < 80) {
                return { 颜色名称: "黑色系" };
            } else {
                return { 颜色名称: "灰色系" };
            }
        }

        // 计算HSV平均值
        var hsv平均值 = Core.mean(hsv图像);
        var 色调 = Math.round(hsv平均值.val[0]);
        var 饱和度 = Math.round(hsv平均值.val[1]);
        var 明度 = Math.round(hsv平均值.val[2]);

        // 释放资源
        hsv图像.release();

        // 快速颜色分类
        var 颜色名称;

        // 判断是否为无色系（白色、灰色、黑色）
        if (饱和度 < 30) {
            if (明度 > 200) {
                颜色名称 = "白色系";
            } else if (明度 < 80) {
                颜色名称 = "黑色系";
            } else {
                颜色名称 = "灰色系";
            }
        } else {
            // 有色系，简单分类
            if (色调 >= 0 && 色调 < 30) {
                颜色名称 = "红色系";
            } else if (色调 >= 30 && 色调 < 90) {
                颜色名称 = "绿色系";
            } else if (色调 >= 90 && 色调 < 150) {
                颜色名称 = "蓝色系";
            } else if (色调 >= 150 && 色调 < 180) {
                颜色名称 = "紫色系";
            } else {
                颜色名称 = "红色系";  // 色调环回到红色
            }
        }

        // console.log("  📊 快速颜色分析结果: " + 颜色名称 + " (H:" + 色调 + "° S:" + 饱和度 + " V:" + 明度 + ")");

        return {
            颜色名称: 颜色名称,
            色调: 色调,
            饱和度: 饱和度,
            明度: 明度
        };

    } catch (e) {
        console.error("  ❌ 快速背景颜色分析失败: " + e);
        return { 颜色名称: "未知色系" };
    }
}




// 注释掉模块导出，方便直接测试
// module.exports = {
//     看广告: 看广告
// };



// // 在文件末尾添加
var 结果 = 看广告("右广告");  // 只检测右广告区域
console.log("检测结果:", JSON.stringify(结果, null, 2));

/**
 * 使用说明：
 *
 * 1. 基本用法：
 *    var 结果 = 看广告();  // 检测所有区域
 *
 * 2. 指定区域：
 *    var 结果 = 看广告("close区域");  // 只检测close区域
 *    var 结果 = 看广告("右广告");     // 只检测右广告
 *    var 结果 = 看广告("左广告");     // 只检测左广告
 *
 * 3. 控制调试：
 *    var 结果 = 看广告("close区域", false);  // 不保存调试图片
 *
 * 4. 返回结果格式：
 *    {
 *        成功: true/false,
 *        匹配数量: 数字,
 *        匹配结果: [
 *            {
 *                区域名称: "close区域",
 *                模板文件: "图片路径",
 *                屏幕坐标: {x: 100, y: 200},
 *                区域坐标: {x: 50, y: 100},
 *                相似度: 0.01,
 *                尺寸: {宽度: 50, 高度: 30},
 *                角度: 0,
 *                匹配时间: "14:30:25"
 *            }
 *        ],
 *        最佳匹配: 匹配结果[0]
 *    }
 *
 * 5. 使用的OpenCV功能总结：
 *    - cvtColor: 彩色转灰度
 *    - findContours: 轮廓检测
 *    - matchShapes: 形状匹配 (单一相似度判断)
 *    - minAreaRect: 最小外接矩形 (获取中心点、尺寸、角度)
 *    - drawContours: 轮廓可视化
 *
 * 6. 调试图片保存位置：
 *    /storage/emulated/0/区域名称_目标轮廓_调试.jpg
 */
