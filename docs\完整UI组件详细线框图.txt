脚本配置页面 - 完整UI组件详细线框图

╔═══════════════════════════════════════════════════════════════════╗
║                        脚本配置页面完整布局                        ║
╚═══════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────┐
│                    顶部导航栏                                    │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ☰     脚本配置                                              │ │
│ │ 菜单   (18sp白色粗体,左对齐,16dp左边距)                     │ │
│ │ (56dp) 绿色背景 #4CAF50, 56dp高, 4dp阴影                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   滚动内容区域 (浅灰背景)                       │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                  游戏配置卡片                                │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 游戏配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 登陆Play商店 ──────────────────────────── ●○ 开关      │ │ │
│ │ │ (14sp黑色文字, 48dp高度, 16dp内边距)                   │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ ☑ 帐号登陆        │        ☑ 首次教程                  │ │ │
│ │ │ (24dp勾选框+文字) │        (24dp勾选框+文字)           │ │ │
│ │ │ 48dp高度, 8dp内边距, 14sp黑色文字                      │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ ☑ 检查分数        │        ☐ 首次提现教程              │ │ │
│ │ │ (24dp勾选框+文字) │        (24dp勾选框+文字)           │ │ │
│ │ │ 48dp高度, 8dp内边距, 14sp黑色文字                      │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影, 16dp底边距                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                 操作配置卡片                                 │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 操作配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 左键设置                                                  │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ X坐标 (12sp灰色)    │ Y坐标 (12sp灰色)    │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:0,数字输入,8dp │ 提示:0,数字输入,8dp │     │ │ │ │
│ │ │ │ │ 圆角,浅灰背景       │ 圆角,浅灰背景       │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ 宽度 (12sp灰色)     │ 高度 (12sp灰色)     │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:100,数字输入   │ 提示:100,数字输入   │     │ │ │ │
│ │ │ │ │ 8dp圆角,浅灰背景    │ 8dp圆角,浅灰背景    │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │ 右键设置 ──────────────────────────────── ●○ 开关      │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │          坐标输入区域 (开关开启时显示)                │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ X坐标 (12sp灰色)    │ Y坐标 (12sp灰色)    │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:0,数字输入,8dp │ 提示:0,数字输入,8dp │     │ │ │ │
│ │ │ │ │ 圆角,浅灰背景       │ 圆角,浅灰背景       │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ 宽度 (12sp灰色)     │ 高度 (12sp灰色)     │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:100,数字输入   │ 提示:100,数字输入   │     │ │ │ │
│ │ │ │ │ 8dp圆角,浅灰背景    │ 8dp圆角,浅灰背景    │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影, 16dp底边距                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │               更多操作配置卡片                                │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 更多操作配置 (16sp粗体黑色)                            │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                分数控制卡片                                  │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 分数控制 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 分数暂停 ──────────────────────────────── ●○ 开关      │ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │          分数输入区域 (开关开启时显示)                │ │ │ │
│ │ │ │ ┌─────────────┬─────────────┐                        │ │ │ │
│ │ │ │ │ 最低        │ 最高        │ 分 (12sp灰色标签)      │ │ │ │
│ │ │ │ │ [_______]   │ [_______]   │                        │ │ │ │
│ │ │ │ │ 提示:100    │ 提示:110    │                        │ │ │ │
│ │ │ │ │ 数字输入    │ 数字输入    │                        │ │ │ │
│ │ │ │ │ 8dp圆角     │ 8dp圆角     │                        │ │ │ │
│ │ │ │ └─────────────┴─────────────┘                        │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影, 16dp底边距                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                广告配置卡片                                  │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 广告配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ (注意: 广告配置卡片不包含自动看广告开关)                │ │ │
│ │ │                                                         │ │ │
│ │ │ 广告左区域 ──────────────────────────────── （文字标题）│ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │          坐标输入区域                                │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ X坐标 (12sp灰色)    │ Y坐标 (12sp灰色)    │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:0,数字输入,8dp │ 提示:0,数字输入,8dp │     │ │ │ │
│ │ │ │ │ 圆角,浅灰背景       │ 圆角,浅灰背景       │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ 宽度 (12sp灰色)     │ 高度 (12sp灰色)     │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:100,数字输入   │ 提示:100,数字输入   │     │ │ │ │
│ │ │ │ │ 8dp圆角,浅灰背景    │ 8dp圆角,浅灰背景    │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │                                                         │ │ │
│ │ │ 广告右区域 ──────────────────────────────── （文字标题）│ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │          坐标输入区域                                │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ X坐标 (12sp灰色)    │ Y坐标 (12sp灰色)    │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:0,数字输入,8dp │ 提示:0,数字输入,8dp │     │ │ │ │
│ │ │ │ │ 圆角,浅灰背景       │ 圆角,浅灰背景       │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────┬─────────────────────┐     │ │ │ │
│ │ │ │ │ 宽度 (12sp灰色)     │ 高度 (12sp灰色)     │     │ │ │ │
│ │ │ │ │ [_______________]   │ [_______________]   │     │ │ │ │
│ │ │ │ │ 提示:100,数字输入   │ 提示:100,数字输入   │     │ │ │ │
│ │ │ │ │ 8dp圆角,浅灰背景    │ 8dp圆角,浅灰背景    │     │ │ │ │
│ │ │ │ └─────────────────────┴─────────────────────┘     │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影, 16dp底边距                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                  底部操作按钮区                                   │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────┬─────────────────────┐               │ │
│ │ │     保存配置        │     重置配置        │               │ │
│ │ │ Material Design样式 │ Material Design样式 │               │ │
│ │ │ backgroundTint绿色  │ backgroundTint绿色  │               │ │
│ │ │ 白色文字,16sp粗体   │ 白色文字,16sp粗体   │               │ │
│ │ │ 48dp高,自带圆角     │ 48dp高,自带圆角     │               │ │
│ │ │ 右8dp边距           │ 左8dp边距           │               │ │
│ │ └─────────────────────┴─────────────────────┘               │ │
│ │ 白色背景, 16dp内边距, 4dp阴影                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   底部导航栏 ✅已实现                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │    ⚙️ 脚本配置      🏠 主页        📄 日志                │ │
│ │   (白色,当前页面)   (半透明白色)   (半透明白色)              │ │
│ │   20sp图标         20sp图标       20sp图标                 │ │
│ │   8dp内边距        8dp内边距      8dp内边距                │ │
│ │ 绿色背景#4CAF50, 50dp高, 8dp阴影                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                           组件状态统计                            ║
╚═══════════════════════════════════════════════════════════════════╝

✅ 已完成的UI组件:
┌─────────────────────────────────────────────────────────────────┐
│ • 顶部导航栏 (菜单按钮 + 标题)                                  │
│ • 游戏配置卡片 (紧凑布局: 1个开关 + 4个勾选框)                 │
│   - 登陆Play商店 (开关) ✅                                      │
│   - 第一行: 帐号登陆 + 首次教程 (勾选框) 🔄 需重构             │
│   - 第二行: 检查分数 + 首次提现教程 (勾选框) 🔄 需重构         │
│   布局优化: 2x2勾选框网格，节省空间，48dp行高                  │
│ • 操作配置卡片 (左键设置完整功能)                               │
│   - 左键设置开关 + 4个输入框 ✅                                 │
│   - 右键设置开关 + 4个输入框 ✅                                 │
│ • 分数控制卡片 (分数暂停功能)                                   │
│   - 分数暂停开关 ✅                                             │
│   - 最低/最高分数输入框 ✅                                      │
│ • 广告配置卡片 (左右区域设置)                                   │
│   - 广告左区域 4个输入框 ✅                                     │
│   - 广告右区域 4个输入框 ✅                                     │
│ • 底部操作按钮 (保存配置、重置配置) ✅                          │
│ • 底部导航栏 (三个标签页) ✅                                    │
└─────────────────────────────────────────────────────────────────┘

🔄 需要完善的功能:
┌─────────────────────────────────────────────────────────────────┐
│ • 右键设置功能的显示隐藏逻辑                                    │
│ • 分数控制输入框的显示隐藏逻辑                                  │
│ • 所有输入框的数据验证和保存逻辑                                │
│ • 配置项的数据持久化                                            │
│ • 按钮的事件处理和反馈                                          │
│ • 开关状态的同步和保存                                          │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                         技术实现规范                              ║
╚═══════════════════════════════════════════════════════════════════╝

圆角设置正确方法:
┌─────────────────────────────────────────────────────────────────┐
│ Card控件圆角:                                                   │
│ • 正确: cardBackgroundColor="#FFFFFF" + cardCornerRadius="16dp" │
│ • 错误: background="#FFFFFF" (会覆盖圆角效果)                   │
│                                                                 │
│ Button控件圆角:                                                 │
│ • 正确: style="Widget.AppCompat.Button.Colored" +              │
│         backgroundTint="#4CAF50"                                │
│ • 错误: background="#4CAF50" + radius="12dp"                   │
│                                                                 │
│ Input控件圆角:                                                  │
│ • 正确: background="#FAFAFA" + radius="8dp"                    │
│ • 适用: 输入框可以使用radius属性                                │
└─────────────────────────────────────────────────────────────────┘

AutoXjs API使用规范:
┌─────────────────────────────────────────────────────────────────┐
│ • 使用ui.layout()加载XML布局                                    │
│ • 使用ui.控件ID访问控件                                         │
│ • 使用控件.on("click", function(){})绑定事件                    │
│ • 使用控件.attr()方法设置属性                                   │
│ • 使用控件.setText()、setTextColor()等方法更新内容              │
│ • 使用ui.post()确保UI操作在主线程执行                           │
└─────────────────────────────────────────────────────────────────┘

