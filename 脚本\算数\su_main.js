/**
 * Magic游戏辅助脚本 - 算数游戏完整全流程
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 目标游戏：Brain Battle (com.winrgames.brainbattle)
 */

// 全局屏幕捕获权限
if (!requestScreenCapture()) {
    console.log("❌ 屏幕捕获权限获取失败");
    exit();
}
console.log("✅ 屏幕捕获权限获取成功");

// 导入功能模块
var 登陆模块 = require('../常用功能/登陆.js');
var 帐号模块 = require('../常用功能/帐号.js');
var 新手教程模块 = require('./新手教程.js');
var 开始界面模块 = require('./开始界面.js');
var 首次提现模块 = require('./首次提现.js');


function 算数_程序() {
    try {

        // 循环控制变量
        var 循环局数 = 0;
        var 最大循环次数 = 3;
        var 总开始时间 = Date.now();

        // 分数统计变量
        var 上一局总分 = 0;
        var 累计总分 = 0;

        console.log("🔄 开始执行算数游戏循环，共" + 最大循环次数 + "局");
        console.log("═══════════════════════════════════════");

        while (循环局数 < 最大循环次数) {
            循环局数++;
            var 局开始时间 = Date.now();

            console.log("🎮 第" + 循环局数 + "局开始 (" + 循环局数 + "/" + 最大循环次数 + ")");
            console.log("───────────────────────────────────────");


            console.log("🎯 启动算数游戏 - Brain Battle");
            // 第一步：先检查算数游戏是否在运行，并执行启动操作
            var 当前包名 = currentPackage();
            if (当前包名 === "com.winrgames.brainbattle") {
                console.log("✅ 算数游戏已在运行，直接执行后续功能");
            } else {
                console.log("📋 算数游戏未运行，尝试启动游戏");
                var 登陆成功 = 登陆模块.登陆_游戏("com.winrgames.brainbattle", "Brain Battle", 1000, 200);

                if (!登陆成功) {
                    console.log("⚠️ 算数游戏启动失败，但继续执行后续功能（可能游戏已在运行）");
                    // 不直接返回false，继续执行后续功能
                } else {
                    console.log("✅ 算数游戏启动成功");
                }
            }

        // // 第2步：执行帐号登陆
        // console.log("📋 步骤2: 执行帐号登陆");
        // var 登陆 = 帐号模块.登陆_帐号();

        // if (!登陆) {
        //     console.log("⚠️ 帐号登陆未找到跳过按钮，继续执行后续功能");
        // } else {
        //     console.log("✅ 帐号登陆成功，已点击跳过按钮");
        // }

        // // 第3步：执行算数首次教程
        // var 教程结果 = 新手教程模块.算数首次教程();

        // if (!教程结果) {
        //     console.log("⚠️ 算数首次教程未完成，继续执行后续功能");
        // } else {
        //     console.log("✅ 算数首次教程完成");
        // }


        // // 第4步：执行首次提现教程
        // var 首次提现结果 = 首次提现模块.首次提现教程();

        // if (!首次提现结果) {
        //     console.log("⚠️ 首次提现教程未完成，继续执行后续功能");
        // } else {
        //     console.log("✅ 首次提现教程完成");
        // }

            // 延时200毫秒
            console.log("⏳ 延时200毫秒...");
            sleep(200);

            // 第5步：执行每日领奖
            console.log("📋 步骤3.2: 执行每日领奖");
            var 每日领奖结果 = 开始界面模块.每日领奖();

            if (!每日领奖结果) {
                console.log("⚠️ 每日领奖未完成，继续执行后续功能");
            } else {
                console.log("✅ 每日领奖完成");
            }

            // 第5.1步：随机等待时间（0.1秒-0.2秒）
            var 随机等待时间5 = random(100, 200);
            console.log("⏳ 随机等待 " + 随机等待时间5 + " 毫秒后开始点击开始界面");
            sleep(随机等待时间5);

            // 第5.2步：执行点击开始界面
            console.log("📋 步骤3.5: 执行点击开始界面");
            var 开始界面结果 = 开始界面模块.点击开始();

            if (!开始界面结果) {
                console.log("⚠️ 点击开始界面未完成，继续执行后续功能");
            } else {
                console.log("✅ 点击开始界面完成");
            }

            // 第5.3步：随机等待时间（0.1秒-0.2秒）
            var 随机等待时间6 = random(100, 200);
            console.log("⏳ 随机等待 " + 随机等待时间6 + " 毫秒后开始算数游戏识别和计算");
            sleep(随机等待时间6);

            // 第6步：执行算数游戏核心功能
            console.log("📋 步骤4: 开始算数游戏识别和计算");
            console.log("🧮 启动三区域公式识别引擎");

            try {
                // 导入公式2.js模块并执行计算_公式函数
                var 公式模块 = require('./公式2.js');
                公式模块.计算_公式();
                console.log("✅ 算数游戏识别引擎执行完成");
            } catch (e) {
                console.log("❌ 算数游戏识别引擎执行失败: " + e);
            }

            // 第6.1步：随机等待时间（9-10秒）
            var 随机等待时间7 = random(9000, 10000);
            console.log("⏳ 随机等待 " + 随机等待时间7 + " 毫秒后开始看广告功能");
            sleep(随机等待时间7);

            // 第7步：看广告功能
            try {
                // 导入ocr看广告.js模块并执行看广告完整流程函数
                var 看广告模块 = require('./ocr看广告.js');
                var 看广告结果 = 看广告模块.执行看广告完整流程();

                if (看广告结果.成功) {
                    console.log("✅ 看广告功能执行成功: " + 看广告结果.消息);
                    console.log("🔄 执行了 " + 看广告结果.循环次数 + " 次循环");
                } else {
                    console.log("⚠️ 看广告功能执行失败: " + 看广告结果.消息);
                }
            } catch (e) {
                console.log("❌ 看广告功能执行出错: " + e);
            }

            // // 第8步：再次执行算数首次教程
            // console.log("📋 步骤6: 再次执行算数首次教程");
            // var 教程结果2 = 新手教程模块.算数首次教程();

            // if (!教程结果2) {
            //     console.log("⚠️ 第二次算数首次教程未完成，继续执行后续功能");
            // } else {
            //     console.log("✅ 第二次算数首次教程完成");
            // }

            // 第9步：分数统计和计算
            try {
                // 导入分数统计.js模块并执行分数统计函数
                var 分数统计模块 = require('./分数统计.js');
                var 分数统计结果 = 分数统计模块.分数统计();

                if (分数统计结果.成功) {
                    // 获取当前总分
                    累计总分 = 分数统计结果.总分;

                    // 计算本局获得的分数
                    var 本局分数 = 累计总分 - 上一局总分;

                    console.log("📊 第" + 循环局数 + "局分数统计:");
                    console.log("   🎯 本局分数: " + 本局分数 + " 分");
                    console.log("   📈 累计总分: " + 累计总分 + " 分");

                    // 更新上一局总分为当前总分，供下一局计算使用
                    上一局总分 = 累计总分;
                } else {
                    console.log("⚠️ 分数统计功能执行失败: " + 分数统计结果.消息);
                }
            } catch (e) {
                console.log("❌ 分数统计功能执行出错: " + e);
            }

            // 第10步：计算本局耗时
            var 局耗时 = Date.now() - 局开始时间;
            console.log("───────────────────────────────────────");
            console.log("✅ 第" + 循环局数 + "局，耗时: " + Math.round(局耗时/1000) + "秒");

            // 如果不是最后一局，等待一下
            if (循环局数 < 最大循环次数) {
                console.log("⏳ 等待0.2秒后开始下一局...");
                sleep(200);
            }
        }

        // 第11步：计算总耗时和分数统计
        var 总耗时 = Date.now() - 总开始时间;
        console.log("═══════════════════════════════════════");
        console.log("🎯 所有游戏循环完成！");
        console.log("📊 执行统计: 共" + 循环局数 + "局，总耗时: " + Math.round(总耗时/1000) + "秒");
        console.log("📈 平均每局耗时: " + Math.round(总耗时/循环局数/1000) + "秒");
        console.log("🏆 最终总分: " + 累计总分 + " 分");
        console.log("⭐ 平均每局得分: " + Math.round(累计总分/循环局数) + " 分");



    } catch (error) {
        console.error("❌ 算数程序执行时发生错误: " + error);
        return false;
    }
}

// 主程序入口
console.log("🚀 Magic算数游戏辅助脚本启动");

// 执行算数程序
var 执行结果 = 算数_程序();
console.log("🎯 程序执行结果: " + (执行结果 ? "✅ 成功" : "❌ 失败"));