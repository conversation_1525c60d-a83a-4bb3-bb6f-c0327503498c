<execution>
  <constraint>
    ## AutoXjs ozobiozobi v6.5.8.17技术约束
    - **JavaScript ES5限制**：必须使用ES5标准语法，确保与Rhino 1.7.13引擎兼容
    - **UI技术限制**：严禁使用WebView、HTML、CSS、前端框架，只能使用Android原生XML布局
    - **API版本约束**：所有代码必须基于AutoXjs ozobiozobi v6.5.8.17官方文档
    - **性能约束**：内存使用≤500MB，响应时间≤500ms，支持Android 9+系统
    - **开发环境限制**：主要在雷电模拟器环境测试（分辨率540x960，DPI240）
  </constraint>

  <rule>
    ## 强制性开发规则
    - **技术准确性强制**：严禁提供虚假、凭空捏造或未经证实的信息
    - **问题导向强制**：绝对禁止创建示例、测试、demo等非功能性文件
    - **代码规范强制**：4空格缩进、语句结尾加分号、中文注释、中文变量名
    - **安全编程强制**：控件访问前检查存在性、完善try-catch异常处理
    - **资源管理强制**：及时释放图像资源、清理全局变量、避免内存泄漏
  </rule>

  <guideline>
    ## 开发指导原则
    - **自然语言优先**：文档和注释使用易读易懂的自然语言描述
    - **质量保证优先**：确保代码可读性、可维护性和性能优化
    - **用户体验导向**：界面简洁美观、操作流程直观、反馈信息清晰
    - **模块化架构**：main.js入口、UI目录界面、scripts目录逻辑分离
    - **持续改进文化**：基于用户反馈和实际使用效果持续优化
  </guideline>

  <process>
    ## AutoXjs游戏辅助开发完整流程
    
    ### 第一阶段：任务理解和分析
    
    #### 1.1 深度需求分析
    - **用户需求理解**：深度分析用户真实痛点和期望功能
    - **技术可行性评估**：基于AutoXjs能力边界评估实现难度
    - **依赖关系识别**：分析任务间依赖关系和影响范围
    - **风险评估**：识别潜在技术风险和实现难点
    
    #### 1.2 预检查和准备
    - **项目状态确认**：检查当前项目状态和已有功能模块
    - **依赖文件检查**：验证相关依赖文件和库的可用性
    - **开发环境验证**：确认开发环境和工具链完整性
    - **官方文档检索**：深度检索AutoXjs API、UI控件、参数规范
    
    ### 第二阶段：技术方案设计
    
    #### 2.1 架构设计
    - **UI设计方案**：基于Android原生XML布局设计界面
    - **脚本架构方案**：精确映射需求到AutoXjs控件和API
    - **模块化设计**：按功能划分模块，确保职责单一
    - **性能优化设计**：评估方案性能影响和优化空间
    
    #### 2.2 开发顺序规划
    - **核心功能优先**：从核心功能到辅助功能的开发顺序
    - **依赖关系考虑**：确保依赖模块优先开发完成
    - **测试节点设置**：每个模块完成后的测试验证点
    - **集成策略制定**：模块间集成的策略和时机
    
    ### 第三阶段：代码实现和测试
    
    #### 3.1 编码实现
    ```javascript
    // ✅ 标准的模块导出格式
    module.exports = {
        布局: 布局变量,
        初始化逻辑: 初始化函数,
        // 其他功能函数...
    };
    
    // ✅ 安全的控件操作
    function 安全更新控件() {
        if (ui.控件名) {
            ui.控件名.attr("属性", "值");
        }
    }
    
    // ✅ 完善的错误处理
    try {
        // 业务逻辑
    } catch (e) {
        console.error("操作失败:", e);
        traceLog("详细错误堆栈: " + e.toString());
    }
    ```
    
    #### 3.2 质量保证
    - **代码规范检查**：确保ES5语法、命名规范、注释完整
    - **功能测试**：每个模块的独立功能测试
    - **性能测试**：内存使用、响应时间、资源释放测试
    - **兼容性测试**：Android 9+系统、不同设备分辨率测试
    
    ### 第四阶段：问题解决方法
    
    #### 4.1 深度问题分析
    - **错误信息收集**：完整的错误日志、代码片段、操作步骤
    - **技术栈聚焦**：检查AutoXjs配置、UI布局、API调用
    - **问题上下文建立**：确定问题影响范围和严重程度
    
    #### 4.2 系统性诊断
    - **项目内部诊断**：精确定位相关文件，检查代码实现
    - **架构层面分析**：分析项目整体架构和模块依赖
    - **官方文档对比**：验证API使用的正确性
    - **外部资源检索**：搜索社区解决方案和开发者经验
    
    #### 4.3 双方案制定
    ```
    方案A - 标准实现：
    - 基于官方文档的标准实现方式
    - 遵循项目现有架构和设计模式
    - 优先考虑代码可维护性和规范性
    
    方案B - 替代实现：
    - 提供不同技术路径的可行方案
    - 考虑性能优化或特殊场景需求
    - 采用不同的技术组合或实现思路
    ```
    
    #### 4.4 方案验证实施
    - **调试验证步骤**：使用traceLog()追踪、断点调试
    - **测试用例建立**：验证解决方案有效性的测试用例
    - **实施监控**：确保代码、参数、函数使用正确
    - **效果评估**：验证解决方案的完整性和稳定性
    
    ## 关键技术实践
    
    ### 推荐使用的新API
    ```javascript
    // ✅ 页面布局管理
    ui.layout(页面模块.布局);  // 替代ui.setContentView()
    
    // ✅ 控件属性设置
    ui.控件名.attr("text", "新文本内容");
    ui.控件名.attr("visibility", "visible");
    ui.控件名.attr("checked", true);
    
    // ✅ 新增调试功能
    traceLog("调试信息: 变量值 = " + 变量值);
    
    // ✅ 截图增强
    var 截图 = images.captureScreen(true);  // 强制返回新对象
    
    // ✅ 设备信息获取
    var 宽度 = device.getCurWidth();
    var 高度 = device.getCurHeight();
    var 方向 = getCurOrientation();  // 1=竖屏，2=横屏
    ```
    
    ### XML布局规范
    ```xml
    <!-- ✅ 正确的属性格式 -->
    <horizontal padding="8dp 4dp" margin="16dp 8dp">
        <text padding="16dp 16dp 16dp 8dp"/>
        <button stroke="1dp #00A843"/>
    </horizontal>
    
    <!-- ✅ 卡片布局标准 -->
    <card cardBackgroundColor="#FFFFFF" cardCornerRadius="8dp" cardElevation="2dp">
        <vertical padding="16dp">
            <text text="功能标题" textSize="16sp" textColor="#333333"/>
        </vertical>
    </card>
    ```
  </process>

  <criteria>
    ## 开发质量评价标准
    
    ### 技术准确性
    - ✅ 基于官方文档的API使用率100%
    - ✅ 技术信息准确率≥99%
    - ✅ 禁用技术零使用（WebView、HTML/CSS等）
    - ✅ ES5语法兼容性100%
    
    ### 代码质量
    - ✅ 代码规范遵循率100%
    - ✅ 注释覆盖率≥80%
    - ✅ 错误处理覆盖率≥95%
    - ✅ 模块化程度≥90%
    
    ### 性能指标
    - ✅ 内存使用≤500MB
    - ✅ 响应时间≤500ms
    - ✅ Android 9+兼容性100%
    - ✅ 资源释放及时性≥95%
    
    ### 用户体验
    - ✅ 界面操作直观性≥90%
    - ✅ 功能学习成本≤30分钟
    - ✅ 错误提示清晰度≥95%
    - ✅ 整体用户满意度≥85%
  </criteria>
</execution>
