/**
 * 分数统计模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：上区域分数识别，专门识别数字，过滤英文
 * 作者：Magic
 * 版本：v1.0
 */

/**
 * 主程序##分数统计 - 使用Paddle OCR进行上区域分数识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含分数信息
 *
 * 调用方式：
 * 1. 分数统计()                           - 使用默认配置识别分数
 * 2. 分数统计({置信度阈值: 0.8})           - 自定义置信度阈值
 */
function 分数统计(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        console.log("📊 开始分数统计识别");

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        // 上区域坐标
        var 上区域坐标 = [2, 2, 536, 120];
        console.log("🔍 识别区域: 上区域[" + 上区域坐标.join(",") + "]");

        // 截取上区域
        var 上区域图像 = images.clip(屏幕图像, 上区域坐标[0], 上区域坐标[1], 上区域坐标[2], 上区域坐标[3]);
        if (!上区域图像) {
            throw new Error("截取上区域失败");
        }

        // 使用OCR识别上区域
        console.log("🔤 开始OCR识别，CPU核心数: " + 配置.CPU核心数 + "，快速模式: " + 配置.使用快速模型);
        var OCR结果 = paddle.ocr(上区域图像, 配置.CPU核心数, 配置.使用快速模型);

        // 释放图像资源
        上区域图像.recycle();
        屏幕图像.recycle();

        // 处理OCR结果，提取数字
        var 分数结果 = 处理分数结果(OCR结果, 配置);

        return {
            成功: true,
            分数: 分数结果.分数,
            原始文本: 分数结果.原始文本,
            置信度: 分数结果.置信度,
            消息: "总分：" + 分数结果.分数
        };

    } catch (e) {
        console.error("分数统计识别失败:", e);
        return {
            成功: false,
            分数: 0,
            错误: e.toString(),
            消息: "分数识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 2,                    // 使用2个CPU核心
        使用快速模型: true,              // 使用快速识别模式
        置信度阈值: 0.6                 // 置信度阈值
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 处理分数识别结果 - 提取数字，过滤英文
 */
function 处理分数结果(OCR结果, 配置) {
    if (!OCR结果 || OCR结果.length === 0) {
        console.log("⚠️ OCR未识别到任何内容");
        return {
            分数: 0,
            原始文本: "",
            置信度: 0
        };
    }

    console.log("📝 OCR识别到 " + OCR结果.length + " 个文本项");

    var 所有数字 = [];
    var 原始文本列表 = [];
    var 置信度列表 = [];

    // 遍历OCR结果
    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];
        var 识别文字 = 项目.text;
        var 置信度 = 项目.confidence;

        console.log("   ├─ 识别文字: \"" + 识别文字 + "\" 置信度: " + 置信度.toFixed(3));

        // 过滤置信度过低的结果
        if (置信度 < 配置.置信度阈值) {
            console.log("   │  └─ 置信度过低，跳过");
            continue;
        }

        原始文本列表.push(识别文字);
        置信度列表.push(置信度);

        // 提取数字（移除所有非数字字符）
        var 提取的数字 = 提取数字(识别文字);
        if (提取的数字) {
            所有数字.push(提取的数字);
            console.log("   │  └─ 提取数字: " + 提取的数字);
        } else {
            console.log("   │  └─ 未提取到数字");
        }
    }

    // 计算最终分数
    var 最终分数 = 计算最终分数(所有数字);
    var 平均置信度 = 置信度列表.length > 0 ?
        (置信度列表.reduce(function(a, b) { return a + b; }, 0) / 置信度列表.length) : 0;

    console.log("🎯 分数统计结果:");
    console.log("   ├─ 原始文本: [" + 原始文本列表.join(", ") + "]");
    console.log("   ├─ 提取数字: [" + 所有数字.join(", ") + "]");
    console.log("   ├─ 最终分数: " + 最终分数);
    console.log("   └─ 平均置信度: " + 平均置信度.toFixed(3));

    return {
        分数: 最终分数,
        原始文本: 原始文本列表.join(" "),
        置信度: 平均置信度
    };
}