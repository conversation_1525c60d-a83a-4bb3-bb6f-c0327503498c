第一次：识别数字23的测试结果：
03:25:13.016/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/ocr/黄色区域识别.js]
03:25:13.315/D: ✅ 截图权限申请成功
03:25:15.317/D: 🎯 开始黄色区域数字识别
03:25:15.318/D: 📦 Tesseract OCR实例创建成功
03:25:15.318/D: 📁 数据目录路径: /storage/emulated/0/脚本/magic/脚本/ocr/
03:25:15.319/D: 🔧 正在初始化Tesseract OCR...
03:25:15.635/D: ✅ Tesseract OCR初始化成功
03:25:15.635/D: ⚙️ 设置OCR识别优化参数
03:25:15.636/D: ✅ OCR识别优化配置完成
03:25:15.636/D: 📸 截图并裁剪黄色区域 (83,625,170,105)
03:25:15.637/D: ✅ 裁剪完成，区域尺寸: 170x105
03:25:15.643/D: 🔧 方案3: 黑色数字直接提取 (识别所有数字0-9)
03:25:15.648/D: 📊 将测试 100 个黑色范围，识别所有数字0-9
03:25:15.819/D:    ⭐ 更好结果！
03:25:19.213/D:    ⭐ 更好结果！
03:25:20.030/D:    ⭐ 更好结果！
03:25:20.178/D:    ⭐ 更好结果！
03:25:25.670/D:    ⭐ 更好结果！
03:25:25.833/D:    ⭐ 更好结果！
03:25:26.600/D:    ⭐ 更好结果！
03:25:27.398/D:    ⭐ 更好结果！
03:25:30.738/D:    🔧 数字纠错: [15131] → [13131] (原因: 右侧开口特征)
03:25:32.757/D: 
📊 按置信度排序的识别结果（置信度从低到高）:
03:25:32.758/D:    1. 阈值100: 原始[2] 纯数字[2] 字符数[1] 置信度[0.0%]
03:25:32.758/D:    2. 阈值99: 原始[2] 纯数字[2] 字符数[1] 置信度[0.0%]
03:25:32.759/D:    3. 阈值98: 原始[2] 纯数字[2] 字符数[1] 置信度[0.0%]
03:25:32.759/D:    4. 阈值97: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.759/D:    5. 阈值96: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.760/D:    6. 阈值95: 原始[22] 纯数字[22] 字符数[2] 置信度[0.0%]
03:25:32.760/D:    7. 阈值94: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.761/D:    8. 阈值93: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.761/D:    9. 阈值92: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.762/D:    10. 阈值91: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.762/D:    11. 阈值90: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.763/D:    12. 阈值88: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.763/D:    13. 阈值87: 原始[23] 纯数字[23] 字符数[2] 置信度[0.0%]
03:25:32.763/D:    14. 阈值86: 原始[7 23] 纯数字[723] 字符数[3] 置信度[0.0%]
03:25:32.764/D:    15. 阈值85: 原始[13131] 纯数字[13131] 字符数[5] 置信度[0.0%]
03:25:32.764/D:    16. 阈值53: 原始[23] 纯数字[23] 字符数[2] 置信度[11.0%]
03:25:32.765/D:    17. 阈值55: 原始[23] 纯数字[23] 字符数[2] 置信度[18.0%]
03:25:32.765/D:    18. 阈值48: 原始[23] 纯数字[23] 字符数[2] 置信度[18.0%]
03:25:32.766/D:    19. 阈值54: 原始[23] 纯数字[23] 字符数[2] 置信度[18.0%]
03:25:32.766/D:    20. 阈值44: 原始[23] 纯数字[23] 字符数[2] 置信度[20.0%]
03:25:32.767/D:    21. 阈值84: 原始[4 23 1] 纯数字[4231] 字符数[4] 置信度[21.0%]
03:25:32.767/D:    22. 阈值57: 原始[23] 纯数字[23] 字符数[2] 置信度[22.0%]
03:25:32.768/D:    23. 阈值59: 原始[23] 纯数字[23] 字符数[2] 置信度[22.0%]
03:25:32.768/D:    24. 阈值60: 原始[23] 纯数字[23] 字符数[2] 置信度[22.0%]
03:25:32.768/D:    25. 阈值50: 原始[23] 纯数字[23] 字符数[2] 置信度[23.0%]
03:25:32.769/D:    26. 阈值51: 原始[23] 纯数字[23] 字符数[2] 置信度[23.0%]
03:25:32.769/D:    27. 阈值52: 原始[23] 纯数字[23] 字符数[2] 置信度[23.0%]
03:25:32.770/D:    28. 阈值49: 原始[23] 纯数字[23] 字符数[2] 置信度[23.0%]
03:25:32.770/D:    29. 阈值83: 原始[23 4] 纯数字[234] 字符数[3] 置信度[26.0%]
03:25:32.770/D:    30. 阈值56: 原始[23] 纯数字[23] 字符数[2] 置信度[27.0%]
03:25:32.771/D:    31. 阈值43: 原始[23] 纯数字[23] 字符数[2] 置信度[33.0%]
03:25:32.771/D:    32. 阈值47: 原始[23] 纯数字[23] 字符数[2] 置信度[34.0%]
03:25:32.772/D:    33. 阈值58: 原始[23] 纯数字[23] 字符数[2] 置信度[37.0%]
03:25:32.772/D:    34. 阈值45: 原始[23] 纯数字[23] 字符数[2] 置信度[40.0%]
03:25:32.773/D:    35. 阈值79: 原始[23 5 4] 纯数字[2354] 字符数[4] 置信度[46.0%]
03:25:32.773/D:    36. 阈值46: 原始[23] 纯数字[23] 字符数[2] 置信度[46.0%]
03:25:32.774/D:    37. 阈值38: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.774/D:    38. 阈值37: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.775/D:    39. 阈值39: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.775/D:    40. 阈值40: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.776/D:    41. 阈值41: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.776/D:    42. 阈值42: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.777/D:    43. 阈值36: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.777/D:    44. 阈值35: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.778/D:    45. 阈值34: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.778/D:    46. 阈值33: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.778/D:    47. 阈值32: 原始[23] 纯数字[23] 字符数[2] 置信度[56.0%]
03:25:32.779/D:    48. 阈值81: 原始[7 23 4] 纯数字[7234] 字符数[4] 置信度[58.0%]
03:25:32.779/D:    49. 阈值21: 原始[23] 纯数字[23] 字符数[2] 置信度[59.0%]
03:25:32.780/D:    50. 阈值61: 原始[23] 纯数字[23] 字符数[2] 置信度[62.0%]
03:25:32.781/D:    51. 阈值8: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.781/D:    52. 阈值4: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.782/D:    53. 阈值11: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.782/D:    54. 阈值9: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.783/D:    55. 阈值7: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.783/D:    56. 阈值6: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.784/D:    57. 阈值20: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.785/D:    58. 阈值5: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.785/D:    59. 阈值19: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.786/D:    60. 阈值18: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.786/D:    61. 阈值1: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.786/D:    62. 阈值10: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.787/D:    63. 阈值12: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.787/D:    64. 阈值13: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.788/D:    65. 阈值14: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.788/D:    66. 阈值15: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.789/D:    67. 阈值16: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.789/D:    68. 阈值17: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.790/D:    69. 阈值3: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.790/D:    70. 阈值2: 原始[23] 纯数字[23] 字符数[2] 置信度[65.0%]
03:25:32.791/D:    71. 阈值26: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.791/D:    72. 阈值22: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.792/D:    73. 阈值29: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.792/D:    74. 阈值30: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.793/D:    75. 阈值31: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.793/D:    76. 阈值25: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.794/D:    77. 阈值24: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.794/D:    78. 阈值23: 原始[23] 纯数字[23] 字符数[2] 置信度[66.0%]
03:25:32.795/D:    79. 阈值27: 原始[23] 纯数字[23] 字符数[2] 置信度[68.0%]
03:25:32.795/D:    80. 阈值78: 原始[7 23 1 4] 纯数字[72314] 字符数[5] 置信度[68.0%]
03:25:32.795/D:    81. 阈值80: 原始[7 23 4] 纯数字[7234] 字符数[4] 置信度[69.0%]
03:25:32.796/D:    82. 阈值82: 原始[7 23 4] 纯数字[7234] 字符数[4] 置信度[70.0%]
03:25:32.796/D:    83. 阈值28: 原始[23] 纯数字[23] 字符数[2] 置信度[71.0%]
03:25:32.797/D:    84. 阈值77: 原始[7 23] 纯数字[723] 字符数[3] 置信度[76.0%]
03:25:32.797/D:    85. 阈值67: 原始[23] 纯数字[23] 字符数[2] 置信度[79.0%]
03:25:32.798/D:    86. 阈值66: 原始[23] 纯数字[23] 字符数[2] 置信度[79.0%]
03:25:32.799/D:    87. 阈值65: 原始[23] 纯数字[23] 字符数[2] 置信度[79.0%]
03:25:32.799/D:    88. 阈值76: 原始[7 23] 纯数字[723] 字符数[3] 置信度[79.0%]
03:25:32.799/D:    89. 阈值62: 原始[23] 纯数字[23] 字符数[2] 置信度[80.0%]
03:25:32.800/D:    90. 阈值64: 原始[23] 纯数字[23] 字符数[2] 置信度[82.0%]
03:25:32.800/D:    91. 阈值63: 原始[23] 纯数字[23] 字符数[2] 置信度[82.0%]
03:25:32.801/D:    92. 阈值72: 原始[23] 纯数字[23] 字符数[2] 置信度[85.0%]
03:25:32.801/D:    93. 阈值68: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.802/D:    94. 阈值69: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.802/D:    95. 阈值70: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.802/D:    96. 阈值71: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.803/D:    97. 阈值75: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.803/D:    98. 阈值74: 原始[23] 纯数字[23] 字符数[2] 置信度[89.0%]
03:25:32.804/D:    99. 阈值73: 原始[23] 纯数字[23] 字符数[2] 置信度[91.0%]
03:25:32.804/D: 🏆 最佳结果: 黑色范围73 (#494949) → "23" (数字:2个, 置信度:91.0%)
03:25:32.805/D: 
🎯 最终识别结果: "23"
03:25:32.805/D: 📍 识别区域坐标信息:
03:25:32.806/D:    区域左上角: (83, 625)
03:25:32.806/D:    区域宽高: 170 x 105
03:25:32.806/D:    区域右下角: (253, 730)
03:25:32.807/D:    区域中心点: (168, 677.5)
03:25:32.808/D:    识别到的数字: "23" 位于黄色区域
03:25:32.809/D: 🧹 图片资源已释放
03:25:32.809/D: 
📋 黄色区域数字识别完成
03:25:32.810/D: 🎯 黄色区域范围: (83,625,170,105)
03:25:32.810/D: 🚀 遍历阈值1-100，多重图像优化+像素分布分析识别所有数字0-9
03:25:32.810/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/ocr/黄色区域识别.js ]运行结束，用时19.776000秒


