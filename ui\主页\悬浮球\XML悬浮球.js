/**
 * Magic悬浮球系统 - XML原生悬浮球实现
 * 基于AutoXjs v6.5.8.17 ozobi版本开发
 * 使用floaty.window + Android原生XML + FontAwesome图标
 */

// 导入依赖模块
var 通用图标管理器 = require('../../全局样式/通用图标管理器.js');

// 全局悬浮球实例检查
function 清理全局悬浮球实例() {
    try {
        // 检查是否有其他悬浮球实例
        if (global.所有悬浮球实例) {
            global.所有悬浮球实例.forEach(function(实例) {
                try {
                    if (实例 && typeof 实例.close === 'function') {
                        实例.close();
                    }
                } catch (e) {
                    console.warn("清理悬浮球实例失败:", e);
                }
            });
        }
        global.所有悬浮球实例 = [];
    } catch (e) {
        console.error("清理全局悬浮球实例失败:", e);
    }
}

// 悬浮球状态管理
var 悬浮球状态 = {
    悬浮球窗口: null,
    已创建: false,
    已显示: false,
    位置: { x: -20, y: null },
    透明度: 0.6,
    拖拽状态: {
        正在拖拽: false,
        起始位置: { x: 0, y: 0 },
        起始触摸点: { x: 0, y: 0 },
        拖拽阈值: 10  // 像素，超过此距离才认为是拖拽
    }
};

/**
 * XML悬浮球模块
 */
var XML悬浮球 = {
    
    /**
     * 创建主悬浮球
     * @returns {boolean} 创建是否成功
     */
    创建: function() {
        try {
            console.log("开始创建XML原生悬浮球...");

            // 清理全局悬浮球实例
            清理全局悬浮球实例();

            // 检查是否已经创建
            if (悬浮球状态.已创建 && 悬浮球状态.悬浮球窗口) {
                console.log("悬浮球已存在，先销毁旧实例");
                this.销毁();
            }

            // 检查悬浮窗权限
            if (!floaty.checkPermission()) {
                console.warn("缺少悬浮窗权限，无法创建悬浮球");
                return false;
            }

            // 计算动态位置
            悬浮球状态.位置.y = device.height - 300;

            // 创建悬浮球窗口
            悬浮球状态.悬浮球窗口 = floaty.window(
                <frame w="50dp" h="50dp" gravity="center">
                    <card id="主悬浮球"
                          w="40dp" h="40dp"
                          cardBackgroundColor="#4CAF50"
                          cardCornerRadius="20dp"
                          cardElevation="4dp"
                          alpha="0.6">

                        <text id="生化危险图标"
                              text=""
                              textSize="20sp"
                              textColor="#FFFFFF"
                              gravity="center"
                              w="*" h="*"/>
                    </card>
                </frame>
            );

            if (悬浮球状态.悬浮球窗口) {
                // 注册到全局实例列表
                if (!global.所有悬浮球实例) {
                    global.所有悬浮球实例 = [];
                }
                global.所有悬浮球实例.push(悬浮球状态.悬浮球窗口);

                // 设置位置
                悬浮球状态.悬浮球窗口.setPosition(悬浮球状态.位置.x, 悬浮球状态.位置.y);

                // 应用FontAwesome图标
                this.应用图标();

                // 自动绑定拖拽事件
                this.绑定拖拽事件();

                悬浮球状态.已创建 = true;
                console.log("XML悬浮球创建成功");
                return true;
            } else {
                console.error("悬浮球窗口创建失败");
                return false;
            }

        } catch (e) {
            console.error("创建XML悬浮球失败:", e);
            return false;
        }
    },
    
    /**
     * 应用FontAwesome图标
     */
    应用图标: function() {
        try {
            if (悬浮球状态.悬浮球窗口 && 悬浮球状态.悬浮球窗口.生化危险图标) {
                // 使用通用图标管理器应用生化危险图标
                通用图标管理器.应用悬浮球控件图标(
                    悬浮球状态.悬浮球窗口,
                    "生化危险图标",
                    "安全",
                    "生化危险",
                    {
                        颜色: "#FFFFFF",
                        大小: "20sp"
                    }
                );
                console.log("生化危险图标应用成功");
            } else {
                console.warn("悬浮球图标控件不存在");
            }
        } catch (e) {
            console.error("应用悬浮球图标失败:", e);
        }
    },
    
    /**
     * 显示悬浮球
     * @returns {boolean} 显示是否成功
     */
    显示: function() {
        try {
            if (!悬浮球状态.已创建) {
                console.warn("悬浮球未创建，无法显示");
                return false;
            }
            
            if (悬浮球状态.悬浮球窗口) {
                // 悬浮球默认就是显示的，这里可以添加显示动画
                悬浮球状态.已显示 = true;
                console.log("悬浮球显示成功");
                return true;
            }
            
            return false;
        } catch (e) {
            console.error("显示悬浮球失败:", e);
            return false;
        }
    },
    
    /**
     * 隐藏悬浮球
     * @returns {boolean} 隐藏是否成功
     */
    隐藏: function() {
        try {
            if (悬浮球状态.悬浮球窗口) {
                悬浮球状态.悬浮球窗口.close();
                悬浮球状态.已显示 = false;
                console.log("悬浮球隐藏成功");
                return true;
            }
            return false;
        } catch (e) {
            console.error("隐藏悬浮球失败:", e);
            return false;
        }
    },
    
    /**
     * 销毁悬浮球
     * @returns {boolean} 销毁是否成功
     */
    销毁: function() {
        try {
            if (悬浮球状态.悬浮球窗口) {
                // 从全局实例列表中移除
                if (global.所有悬浮球实例) {
                    var 索引 = global.所有悬浮球实例.indexOf(悬浮球状态.悬浮球窗口);
                    if (索引 > -1) {
                        global.所有悬浮球实例.splice(索引, 1);
                    }
                }

                悬浮球状态.悬浮球窗口.close();
                悬浮球状态.悬浮球窗口 = null;
                悬浮球状态.已创建 = false;
                悬浮球状态.已显示 = false;
                悬浮球状态.拖拽状态.正在拖拽 = false;
                console.log("悬浮球销毁成功");
                return true;
            }
            return false;
        } catch (e) {
            console.error("销毁悬浮球失败:", e);
            return false;
        }
    },
    
    /**
     * 设置透明度
     * @param {number} alpha 透明度值 (0.0-1.0)
     * @returns {boolean} 设置是否成功
     */
    设置透明度: function(alpha) {
        try {
            if (悬浮球状态.悬浮球窗口 && 悬浮球状态.悬浮球窗口.主悬浮球) {
                悬浮球状态.悬浮球窗口.主悬浮球.setAlpha(alpha);
                悬浮球状态.透明度 = alpha;
                console.log("悬浮球透明度设置为:", alpha);
                return true;
            }
            return false;
        } catch (e) {
            console.error("设置悬浮球透明度失败:", e);
            return false;
        }
    },
    
    /**
     * 设置位置
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @returns {boolean} 设置是否成功
     */
    设置位置: function(x, y) {
        try {
            if (悬浮球状态.悬浮球窗口) {
                悬浮球状态.悬浮球窗口.setPosition(x, y);
                悬浮球状态.位置.x = x;
                悬浮球状态.位置.y = y;
                console.log("悬浮球位置设置为:", x, y);
                return true;
            }
            return false;
        } catch (e) {
            console.error("设置悬浮球位置失败:", e);
            return false;
        }
    },
    
    /**
     * 获取当前位置
     * @returns {Object} 位置对象 {x, y}
     */
    获取位置: function() {
        return {
            x: 悬浮球状态.位置.x,
            y: 悬浮球状态.位置.y
        };
    },
    
    /**
     * 获取悬浮球状态
     * @returns {boolean} 是否正在运行
     */
    获取状态: function() {
        return 悬浮球状态.已创建 && 悬浮球状态.已显示;
    },
    
    /**
     * 绑定拖拽事件
     */
    绑定拖拽事件: function() {
        try {
            if (!悬浮球状态.悬浮球窗口 || !悬浮球状态.悬浮球窗口.主悬浮球) {
                console.warn("悬浮球窗口或控件不存在，无法绑定拖拽事件");
                return false;
            }

            var 主悬浮球 = 悬浮球状态.悬浮球窗口.主悬浮球;
            var 拖拽状态 = 悬浮球状态.拖拽状态;

            // 绑定触摸事件
            主悬浮球.setOnTouchListener(function(view, event) {
                try {
                    // 检查菜单状态，如果菜单展开则不允许拖拽
                    if (XML悬浮球.检查菜单状态()) {
                        return false; // 不消费事件，让其他处理器处理
                    }

                    var action = event.getAction();
                    var x = event.getRawX();
                    var y = event.getRawY();

                    switch (action) {
                        case android.view.MotionEvent.ACTION_DOWN:
                            // 记录起始位置
                            拖拽状态.起始触摸点.x = x;
                            拖拽状态.起始触摸点.y = y;
                            拖拽状态.起始位置.x = 悬浮球状态.悬浮球窗口.getX();
                            拖拽状态.起始位置.y = 悬浮球状态.悬浮球窗口.getY();
                            拖拽状态.正在拖拽 = false;

                            console.log("触摸开始:", x, y);
                            return true;

                        case android.view.MotionEvent.ACTION_MOVE:
                            // 计算移动距离
                            var deltaX = x - 拖拽状态.起始触摸点.x;
                            var deltaY = y - 拖拽状态.起始触摸点.y;
                            var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                            // 超过阈值才开始拖拽
                            if (distance > 拖拽状态.拖拽阈值) {
                                拖拽状态.正在拖拽 = true;

                                // 计算新位置
                                var newX = 拖拽状态.起始位置.x + deltaX;
                                var newY = 拖拽状态.起始位置.y + deltaY;

                                // 边界检查
                                var 边界位置 = XML悬浮球.检查边界(newX, newY);

                                // 确保使用同一个窗口实例更新位置
                                if (悬浮球状态.悬浮球窗口 && 悬浮球状态.悬浮球窗口.setPosition) {
                                    悬浮球状态.悬浮球窗口.setPosition(边界位置.x, 边界位置.y);
                                    悬浮球状态.位置.x = 边界位置.x;
                                    悬浮球状态.位置.y = 边界位置.y;
                                }
                            }
                            return true;

                        case android.view.MotionEvent.ACTION_UP:
                            console.log("触摸结束，拖拽状态:", 拖拽状态.正在拖拽);

                            // 如果没有拖拽，则触发点击事件
                            if (!拖拽状态.正在拖拽) {
                                // 延迟触发点击，避免与拖拽冲突
                                setTimeout(function() {
                                    XML悬浮球.触发点击事件();
                                }, 50);
                            } else {
                                // 拖拽结束，执行吸边效果
                                XML悬浮球.执行吸边效果();
                            }

                            拖拽状态.正在拖拽 = false;
                            return true;
                    }

                    return false;
                } catch (e) {
                    console.error("处理触摸事件失败:", e);
                    return false;
                }
            });

            console.log("悬浮球拖拽事件绑定成功");
            return true;

        } catch (e) {
            console.error("绑定拖拽事件失败:", e);
            return false;
        }
    },

    /**
     * 检查边界限制
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @returns {Object} 修正后的位置 {x, y}
     */
    检查边界: function(x, y) {
        try {
            var screenWidth = device.width;
            var screenHeight = device.height;
            var ballSize = 50; // 悬浮球容器大小

            // 限制X坐标范围
            var minX = -ballSize / 2; // 允许一半露出屏幕外
            var maxX = screenWidth - ballSize / 2;

            // 限制Y坐标范围
            var minY = 0; // 不允许超出顶部
            var maxY = screenHeight - ballSize - 100; // 底部留出导航栏空间

            return {
                x: Math.max(minX, Math.min(maxX, x)),
                y: Math.max(minY, Math.min(maxY, y))
            };
        } catch (e) {
            console.error("检查边界失败:", e);
            return { x: x, y: y };
        }
    },

    /**
     * 执行吸边效果
     */
    执行吸边效果: function() {
        try {
            var currentX = 悬浮球状态.位置.x;
            var currentY = 悬浮球状态.位置.y;
            var screenWidth = device.width;
            var ballSize = 50;

            // 判断吸附到左边还是右边
            var centerX = screenWidth / 2;
            var targetX;

            if (currentX < centerX) {
                // 吸附到左边
                targetX = -ballSize / 2 + 5; // 稍微露出一点
            } else {
                // 吸附到右边
                targetX = screenWidth - ballSize / 2 - 5;
            }

            // 执行吸边动画
            this.执行吸边动画(currentX, currentY, targetX, currentY);

        } catch (e) {
            console.error("执行吸边效果失败:", e);
        }
    },

    /**
     * 执行吸边动画
     * @param {number} fromX 起始X坐标
     * @param {number} fromY 起始Y坐标
     * @param {number} toX 目标X坐标
     * @param {number} toY 目标Y坐标
     */
    执行吸边动画: function(fromX, fromY, toX, toY) {
        try {
            var steps = 10; // 动画步数
            var stepX = (toX - fromX) / steps;
            var stepY = (toY - fromY) / steps;
            var currentStep = 0;

            var animateStep = function() {
                if (currentStep < steps && 悬浮球状态.悬浮球窗口) {
                    var newX = fromX + stepX * currentStep;
                    var newY = fromY + stepY * currentStep;

                    // 确保使用同一个窗口实例
                    if (悬浮球状态.悬浮球窗口.setPosition) {
                        悬浮球状态.悬浮球窗口.setPosition(newX, newY);
                        悬浮球状态.位置.x = newX;
                        悬浮球状态.位置.y = newY;
                    }

                    currentStep++;
                    setTimeout(animateStep, 30); // 30ms间隔
                } else if (悬浮球状态.悬浮球窗口) {
                    // 动画结束，设置最终位置
                    if (悬浮球状态.悬浮球窗口.setPosition) {
                        悬浮球状态.悬浮球窗口.setPosition(toX, toY);
                        悬浮球状态.位置.x = toX;
                        悬浮球状态.位置.y = toY;
                        console.log("吸边动画完成，最终位置:", toX, toY);
                    }
                }
            };

            animateStep();

        } catch (e) {
            console.error("执行吸边动画失败:", e);
        }
    },

    /**
     * 触发点击事件
     */
    触发点击事件: function() {
        try {
            console.log("悬浮球被点击（非拖拽）");

            // 检查全局点击处理器
            if (global.悬浮球点击处理器 && typeof global.悬浮球点击处理器 === 'function') {
                global.悬浮球点击处理器();
            } else {
                console.warn("悬浮球点击处理器未设置或无效，使用降级处理");
                // 降级处理：直接切换菜单状态
                if (global.悬浮球管理系统) {
                    var 悬浮球菜单 = global.悬浮球管理系统.获取模块('悬浮球菜单');
                    if (悬浮球菜单) {
                        var 主球位置 = this.获取位置();
                        悬浮球菜单.切换菜单状态(主球位置);
                        device.vibrate(80); // 提供震动反馈
                    }
                }
            }

        } catch (e) {
            console.error("触发点击事件失败:", e);
        }
    },

    /**
     * 获取悬浮球窗口实例
     * @returns {Object} 悬浮球窗口实例
     */
    获取窗口实例: function() {
        return 悬浮球状态.悬浮球窗口;
    },

    /**
     * 检查菜单状态
     * @returns {boolean} 菜单是否展开
     */
    检查菜单状态: function() {
        try {
            // 检查全局菜单状态
            if (global.悬浮球管理系统) {
                var 悬浮球菜单 = global.悬浮球管理系统.获取模块('悬浮球菜单');
                if (悬浮球菜单) {
                    var 菜单状态 = 悬浮球菜单.获取菜单状态();
                    return 菜单状态.菜单已展开;
                }
            }
            return false;
        } catch (e) {
            console.error("检查菜单状态失败:", e);
            return false;
        }
    },

    /**
     * 获取拖拽状态
     * @returns {Object} 拖拽状态信息
     */
    获取拖拽状态: function() {
        return {
            正在拖拽: 悬浮球状态.拖拽状态.正在拖拽,
            当前位置: this.获取位置()
        };
    }
};

// 导出模块
module.exports = XML悬浮球;
