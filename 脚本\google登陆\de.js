/**
 * Magic 游戏辅助脚本 - Google Play商店登录模块（简洁版）
 * 基于AutoXjs ozobiozobi v6.5.8.17开发
 * 遵循简洁代码规则：用最少的代码，最灵活的参数，解决最多的实际问题
 */

// 加载简洁日志管理器
var 日志管理器 = require("../../简洁日志管理器.js");

// ==================== 5个核心函数（遵循简洁代码规则） ====================

/**
 * 检查_权限 - 通用权限检查
 * @param {Array} 权限列表 - ["无障碍", "截图", "悬浮窗"]，默认["无障碍", "截图"]
 * @param {Boolean} 自动申请 - 是否自动申请权限，默认true
 * @returns {Boolean} 权限是否满足
 */
function 检查_权限(权限列表, 自动申请) {
    权限列表 = 权限列表 || ["无障碍", "截图"];
    自动申请 = 自动申请 !== false;

    try {
        for (var i = 0; i < 权限列表.length; i++) {
            var 权限类型 = 权限列表[i];

            if (权限类型 === "无障碍") {
                if (!auto.service) {
                    日志管理器.错误("缺少无障碍权限", "Google登录");
                    if (自动申请) auto.waitFor();
                    return auto.service;
                }
            } else if (权限类型 === "截图") {
                if (typeof requestScreenCapture !== 'function') {
                    日志管理器.错误("截图API不可用", "Google登录");
                    return false;
                } else if (自动申请) {
                    requestScreenCapture();
                    sleep(1000);
                }
            } else if (权限类型 === "悬浮窗") {
                if (!floaty.checkPermission()) {
                    日志管理器.错误("缺少悬浮窗权限", "Google登录");
                    if (自动申请) floaty.requestPermission();
                    return floaty.checkPermission();
                }
            }
        }

        日志管理器.成功("权限检查通过", "Google登录");
        return true;
    } catch (e) {
        日志管理器.错误("权限检查异常: " + e.message, "Google登录");
        return false;
    }
}

/**
 * 查找_邮箱界面 - 专门处理邮箱相关界面识别
 * @param {String} 界面类型 - "邮箱输入框"/"密码输入框"/"登录按钮"等
 * @param {String} 动作 - "点击"/"等待"/"返回坐标"/不填
 * @param {Number} 等待秒数 - 等待超时时间，默认5秒
 * @param {Array} 阈值列表 - 匹配阈值，默认[0.9, 0.8, 0.7]
 * @returns {Object|Boolean} 根据动作返回坐标或成功状态
 */
function 查找_邮箱界面(界面类型, 动作, 等待秒数, 阈值列表) {
    界面类型 = 界面类型 || "邮箱输入框";
    等待秒数 = 等待秒数 || 20;
    阈值列表 = 阈值列表 || [0.9, 0.8, 0.7];

    var 图片映射 = {
        "邮箱输入框": "google邮箱.png",
        "密码输入框": "google密码.png",
        "登录按钮": "google登录.png"
    };

    var 图片名 = 图片映射[界面类型] || "google邮箱.png";

    try {
        var 开始时间 = Date.now();
        var 超时时间 = 等待秒数 * 1000;

        while (Date.now() - 开始时间 < 超时时间) {
            var 截图 = captureScreen();
            var 模板图片 = images.read("./assets/google/" + 图片名);

            if (截图 && 模板图片) {
                for (var i = 0; i < 阈值列表.length; i++) {
                    var 位置 = findImage(截图, 模板图片, { threshold: 阈值列表[i] });
                    if (位置) {
                        截图.recycle();
                        模板图片.recycle();

                        if (动作 === "点击") {
                            click(位置.x, 位置.y);
                            return true;
                        } else if (动作 === "等待") {
                            return true;
                        } else {
                            return 位置;
                        }
                    }
                }
                截图.recycle();
                模板图片.recycle();
            }
            sleep(500);
        }

        return 动作 === "点击" || 动作 === "等待" ? false : null;
    } catch (e) {
        日志器.错误("查找邮箱界面异常: " + e.message);
        return false;
    }
}

/**
 * 输入_内容 - 通用内容输入
 * @param {String} 内容 - 要输入的内容
 * @param {String} 输入方式 - "setText"/"paste"/"type"，默认setText
 * @param {Number} 延时毫秒 - 输入后延时，默认1000毫秒
 * @param {String} 内容类型 - "邮箱"/"密码"/"其他"，用于日志输出
 * @returns {Boolean} 输入是否成功
 */
function 输入_内容(内容, 输入方式, 延时毫秒, 内容类型) {
    输入方式 = 输入方式 || "setText";
    延时毫秒 = 延时毫秒 || 1000;
    内容类型 = 内容类型 || "内容";

    try {
        if (输入方式 === "setText") {
            setText(内容);
        } else if (输入方式 === "paste") {
            setClip(内容);
            paste();
        } else if (输入方式 === "type") {
            for (var i = 0; i < 内容.length; i++) {
                KeyCode(内容.charAt(i));
                sleep(50);
            }
        }

        sleep(延时毫秒);

        // 根据内容类型输出不同的日志
        if (内容类型 === "邮箱") {
            日志管理器.成功("邮箱帐号输入完成", "Google登录");
        } else if (内容类型 === "密码") {
            日志管理器.成功("密码输入完成", "Google登录");
        } else {
            日志管理器.成功(内容类型 + "输入完成", "Google登录");
        }

        return true;
    } catch (e) {
        日志管理器.错误("输入" + 内容类型 + "失败: " + e.message, "Google登录");
        return false;
    }
}

/**
 * 等待_控件 - 通用控件操作
 * @param {String} 选择器 - "text:下一步"/"id:button"/"className:Button"
 * @param {String} 动作 - "点击"/"输入"/"获取"/不填
 * @param {String} 参数 - 动作相关参数（如输入内容）
 * @param {Number} 等待秒数 - 等待超时时间，默认5秒
 * @returns {Object|Boolean|String} 根据动作返回不同结果
 */
function 等待_控件(选择器, 动作, 参数, 等待秒数) {
    等待秒数 = 等待秒数 || 5;

    try {
        var 选择器部分 = 选择器.split(":");
        var 类型 = 选择器部分[0];
        var 值 = 选择器部分[1];

        var 控件;
        if (类型 === "text") {
            控件 = text(值).findOne(等待秒数 * 1000);
        } else if (类型 === "id") {
            控件 = id(值).findOne(等待秒数 * 1000);
        } else if (类型 === "className") {
            控件 = className(值).findOne(等待秒数 * 1000);
        }

        if (!控件) return false;

        if (动作 === "点击") {
            return 控件.click();
        } else if (动作 === "输入" && 参数) {
            控件.setText(参数);
            return true;
        } else if (动作 === "获取") {
            return 控件.text();
        } else {
            return 控件;
        }
    } catch (e) {
        日志管理器.错误("等待控件失败: " + e.message, "Google登录");
        return false;
    }
}

/**
 * 登陆_Play商店 - 主登录流程（简洁版）
 * @param {String} 邮箱 - 登录邮箱，可选（从配置文件读取）
 * @param {String} 密码 - 登录密码，可选（从配置文件读取）
 * @param {Object} 配置参数 - 可选配置：{配置文件路径, 等待时间, 阈值列表}
 * @returns {Boolean} 登录是否成功
 */
function 登陆_Play商店(邮箱, 密码, 配置参数) {
    配置参数 = 配置参数 || {};

    try {
        日志管理器.信息("开始Play商店登录流程", "Google登录");

        // 1. 权限检查
        if (!检查_权限()) {
            日志管理器.错误("权限检查失败", "Google登录");
            return false;
        }

        // 2. 启动应用
        home();
        sleep(500);
        app.launch("com.android.vending");
        sleep(5000);

        // 3. 点击登录
        if (!等待_控件("text:登录", "点击", null, 5) &&
            !等待_控件("text:Sign in", "点击", null, 5)) {
            日志管理器.警告("未找到登录按钮，可能已登录", "Google登录");
        }
        sleep(10000);

        // 4. 输入邮箱
        if (查找_邮箱界面("邮箱输入框", "点击", 8)) {
            var 邮箱内容 = 邮箱 || 读取_配置(配置参数.配置文件路径 || "/storage/emulated/0/Pictures/google1.txt", 0);
            if (邮箱内容 && 输入_内容(邮箱内容, "setText", 1000, "邮箱")) {
                等待_控件("text:下一步", "点击", null, 8);
                sleep(3000);

                // 5. 输入密码
                var 密码内容 = 密码 || 读取_配置(配置参数.配置文件路径 || "/storage/emulated/0/Pictures/google1.txt", 1);
                if (密码内容 && 输入_内容(密码内容, "setText", 1000, "密码")) {
                    等待_控件("text:下一步", "点击", null, 8);
                    sleep(3000);
                }
            }
        }

        // 6. 处理后续步骤（重构逻辑）
        处理_登录后续步骤();

        // 7. 验证登录
        if (等待_控件("text:不用了", "点击", null, 8)) {
            日志管理器.成功("Play商店登录成功", "Google登录");
            return true;
        } else {
            日志管理器.成功("登录流程完成", "Google登录");
            return true;
        }

    } catch (e) {
        日志管理器.错误("登录异常: " + e.message, "Google登录");
        return false;
    }
}

/**
 * 读取_配置 - 通用配置文件读取
 * @param {String} 文件路径 - 配置文件路径
 * @param {Number} 字段索引 - 字段索引，默认0
 * @param {String} 分隔符 - 字段分隔符，默认逗号
 * @returns {String|null} 字段内容或null
 */
function 读取_配置(文件路径, 字段索引, 分隔符) {
    文件路径 = 文件路径 || "/storage/emulated/0/Pictures/google1.txt";
    字段索引 = 字段索引 || 0;
    分隔符 = 分隔符 || ",";

    try {
        if (!files.exists(文件路径)) return null;

        var 内容 = files.read(文件路径).trim();
        var 字段列表 = 内容.split(分隔符);

        return 字段列表.length > 字段索引 ? 字段列表[字段索引].trim() : null;
    } catch (e) {
        日志管理器.错误("读取配置失败: " + e.message, "Google登录");
        return null;
    }
}

/**
 * 处理_登录后续步骤 - 重构版本（智能处理更多和接受按钮）
 * @returns {Boolean} 处理是否成功
 */
function 处理_登录后续步骤() {
    try {
        日志管理器.信息("开始处理登录后续步骤", "Google登录");

        // 1. 跳过按钮
        if (等待_控件("text:跳过", "点击", null, 10)) {
            日志管理器.成功("点击跳过按钮", "Google登录");
            sleep(500);
        }

        // 2. 我同意按钮
        if (等待_控件("text:我同意", "点击", null, 10)) {
            日志管理器.成功("点击我同意按钮", "Google登录");
            sleep(7000);
        }

        // 3. 智能处理更多和接受按钮
        var 更多按钮找到 = 等待_控件("text:更多", "点击", null, 10);
        if (更多按钮找到) {
            日志管理器.成功("点击更多按钮", "Google登录");
            sleep(5000);

            // 找到更多按钮后，尝试点击接受按钮
            if (等待_控件("text:接受", "点击", null, 10)) {
                日志管理器.成功("点击接受按钮", "Google登录");
            } else {
                // 如果文本接受无法点击，使用className方式
                try {
                    var 按钮控件 = className("android.widget.Button").findOne(5000);
                    if (按钮控件) {
                        按钮控件.click();
                        日志管理器.成功("点击接受按钮", "Google登录");
                    }
                } catch (e) {
                    日志管理器.警告("接受按钮点击失败: " + e.message, "Google登录");
                }
            }
        } else {
            // 没有找到更多按钮，直接尝试点击接受按钮
            日志管理器.信息("未找到更多按钮，直接尝试点击接受按钮", "Google登录");

            if (等待_控件("text:接受", "点击", null, 8)) {
                日志管理器.成功("点击接受按钮", "Google登录");
            } else {
                // 如果文本接受无法点击，使用className方式
                try {
                    var 按钮控件 = className("android.widget.Button").findOne(3000);
                    if (按钮控件) {
                        按钮控件.click();
                        日志管理器.成功("点击接受按钮", "Google登录");
                    } else {
                        日志管理器.警告("未找到可点击的按钮控件", "Google登录");
                    }
                } catch (e) {
                    日志管理器.警告("接受按钮点击失败: " + e.message, "Google登录");
                }
            }
        }

        sleep(500);
        日志管理器.成功("登录后续步骤处理完成", "Google登录");
        return true;

    } catch (e) {
        日志管理器.错误("处理登录后续步骤失败: " + e.message, "Google登录");
        return false;
    }
}

// ==================== 模块导出 ====================

module.exports = {
    // 6个核心函数
    检查_权限: 检查_权限,
    查找_邮箱界面: 查找_邮箱界面,
    输入_内容: 输入_内容,
    等待_控件: 等待_控件,
    登陆_Play商店: 登陆_Play商店,
    读取_配置: 读取_配置,
    处理_登录后续步骤: 处理_登录后续步骤,

    // 兼容性接口
    登陆Play商店: 登陆_Play商店
};



