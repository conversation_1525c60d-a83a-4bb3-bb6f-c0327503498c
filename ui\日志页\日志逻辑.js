/**
 * Magic 游戏辅助脚本 - 日志页面逻辑
 * 负责日志页面的交互逻辑、日志管理功能
 * 基于最新AutoXjs API开发，避免过时API
 */

// 导入依赖模块
var 全局样式 = require('../全局样式/公共样式.js');
var 日志界面 = require('./UI日志页面.js');
var 通用图标管理器 = require('../全局样式/通用图标管理器.js');

// 日志逻辑状态管理
var 日志状态 = {
    当前页面: "日志",
    日志列表: [],
    最大日志数量: 1000, // 限制日志数量，避免内存溢出
    自动滚动: true
};

/**
 * 初始化日志逻辑
 */
function 初始化日志逻辑() {
    try {
        console.log("开始初始化日志逻辑...");

        // 初始化现代图标
        初始化现代图标();

        // 注册事件监听器
        注册按钮事件();
        注册导航事件();

        // 初始化抽屉逻辑
        初始化抽屉逻辑();

        // 初始化界面状态
        更新界面状态();

        // 添加欢迎日志
        添加日志("日志系统已启动", "成功");

        // 启动异步日志文件监听（方案三）
        启动异步日志监听();

        console.log("日志逻辑初始化完成");
        return true;
    } catch (e) {
        console.error("日志逻辑初始化失败:", e);
        toast("日志逻辑初始化失败: " + e.message);
        return false;
    }
}

/**
 * 初始化现代图标
 */
function 初始化现代图标() {
    try {
        console.log("开始初始化日志页面现代图标...");

        // 应用导航栏图标
        通用图标管理器.应用导航栏图标({
            脚本图标: { 颜色: "#CCFFFFFF" },
            主页图标: { 颜色: "#CCFFFFFF" },
            日志图标: { 颜色: 全局样式.颜色主题.白色 },
            菜单图标: { 颜色: 全局样式.颜色主题.白色 }
        });

        // 应用日志页面特有图标
        通用图标管理器.应用日志页面图标({
            清空图标: true
        });

        // 应用清空日志按钮图标 - 使用删除图标
        通用图标管理器.应用图标("清空日志", "操作", "删除", {
            颜色: 全局样式.颜色主题.白色,
            大小: "18sp"
        });

        // 应用空状态图标
        通用图标管理器.应用图标("空状态图标", "文件", "文档", {
            颜色: 全局样式.颜色主题.文字次色,
            大小: "48sp"
        });

        console.log("日志页面现代图标初始化完成");
    } catch (e) {
        console.error("初始化日志页面现代图标失败:", e);
    }
}

/**
 * 注册按钮事件监听器
 */
function 注册按钮事件() {
    try {
        // 刷新异步日志按钮事件
        if (ui.刷新异步日志) {
            ui.刷新异步日志.on("click", function() {
                显示气泡提示("刷新异步日志");
                刷新异步日志();
            });
        }

        // 清空日志按钮事件（重构版本）
        if (ui.清空日志) {
            ui.清空日志.on("click", function() {
                显示气泡提示("清空所有日志");

                // 显示确认对话框
                dialogs.confirm("确认清空", "确定要清空所有日志记录吗？此操作不可恢复。")
                    .then(确认 => {
                        if (确认) {
                            清空所有日志();
                            // 同时清空异步日志文件（方案三）
                            清空异步日志文件();
                            显示气泡提示("日志已清空", "成功");
                        }
                    })
                    .catch(e => {
                        console.error("清空日志确认对话框出错:", e);
                    });
            });
        }

        // 菜单按钮事件
        if (ui.菜单按钮) {
            ui.菜单按钮.on("click", function() {
                打开抽屉页面();
            });
        }

        console.log("日志页面按钮事件注册完成");
    } catch (e) {
        console.error("注册按钮事件失败:", e);
    }
}

/**
 * 注册导航栏事件监听器
 */
function 注册导航事件() {
    try {
        // 脚本配置标签点击事件
        if (ui.脚本标签) {
            ui.脚本标签.on("click", function() {
                切换到页面("脚本配置");
            });
        }

        // 主页标签点击事件
        if (ui.主页标签) {
            ui.主页标签.on("click", function() {
                切换到页面("主页");
            });
        }

        // 日志标签点击事件（当前页面，无需切换）
        if (ui.日志标签) {
            ui.日志标签.on("click", function() {
                显示气泡提示("当前已在日志页面");
            });
        }

        console.log("日志页面导航事件注册完成");
    } catch (e) {
        console.error("注册导航事件失败:", e);
    }
}

/**
 * 添加日志记录
 * @param {String} 内容 - 日志内容
 * @param {String} 类型 - 日志类型：成功、警告、错误、信息
 */
function 添加日志(内容, 类型) {
    try {
        if (!内容) {
            console.warn("日志内容不能为空");
            return;
        }

        类型 = 类型 || "信息";

        // 不再重复记录到控制台，由调用方负责控制台输出

        // 检查UI是否可用，避免在应用退出时操作UI
        if (!ui) {
            // 静默跳过，不输出调试信息
            return;
        }

        // 创建日志对象
        var 日志对象 = {
            时间: new Date(),
            内容: 内容,
            类型: 类型,
            id: Date.now() + Math.random()
        };

        // 添加到日志列表
        日志状态.日志列表.push(日志对象);

        // 限制日志数量，删除最旧的日志
        if (日志状态.日志列表.length > 日志状态.最大日志数量) {
            日志状态.日志列表.shift();
        }

        // 安全地更新界面显示
        try {
            if (日志界面 && 日志界面.添加日志) {
                日志界面.添加日志(内容, 类型);
            }
        } catch (uiError) {
            // 静默处理UI错误，控制台日志已记录
        }

    } catch (e) {
        console.error("添加日志失败:", e);
    }
}

/**
 * 清空所有日志
 */
function 清空所有日志() {
    try {
        // 记录清空前的日志数量
        var 清空前数量 = 日志状态.日志列表.length;

        // 清空日志列表 - 使用更高效的方式
        日志状态.日志列表.length = 0;

        // 安全地清空界面显示
        if (日志界面 && 日志界面.清空日志) {
            日志界面.清空日志();
        }

        console.log("所有日志已清空，清空了", 清空前数量, "条日志");
    } catch (e) {
        console.error("清空日志失败:", e);
        显示气泡提示("清空日志失败: " + e.message, "错误");
    }
}

/**
 * 刷新异步日志（方案三核心功能）
 */
function 刷新异步日志() {
    try {
        var 简洁日志管理器 = require('../../简洁日志管理器.js');
        var 异步日志列表 = 简洁日志管理器.读取异步日志();

        if (异步日志列表.length > 0) {
            console.log("读取到", 异步日志列表.length, "条异步日志");

            // 将异步日志添加到UI显示
            for (var i = 0; i < 异步日志列表.length; i++) {
                var 日志项 = 异步日志列表[i];
                添加日志(日志项.消息, 日志项.类型);
            }

            显示气泡提示("已刷新 " + 异步日志列表.length + " 条异步日志", "成功");
        } else {
            显示气泡提示("暂无异步日志", "信息");
        }
    } catch (e) {
        console.error("刷新异步日志失败:", e);
        显示气泡提示("刷新异步日志失败: " + e.message, "错误");
    }
}

/**
 * 清空异步日志文件（方案三核心功能）
 */
function 清空异步日志文件() {
    try {
        var 简洁日志管理器 = require('../../简洁日志管理器.js');
        var 清空结果 = 简洁日志管理器.清空异步日志();

        if (清空结果) {
            console.log("异步日志文件已清空");
        } else {
            console.warn("异步日志文件清空失败");
        }
    } catch (e) {
        console.error("清空异步日志文件失败:", e);
    }
}

/**
 * 启动异步日志监听（方案三核心功能）
 */
function 启动异步日志监听() {
    try {
        var 简洁日志管理器 = require('../../简洁日志管理器.js');

        // 设置定时器，每2秒检查一次异步日志文件
        setInterval(function() {
            try {
                var 当前修改时间 = 简洁日志管理器.获取文件修改时间();

                // 如果文件有更新
                if (当前修改时间 > 简洁日志管理器.上次文件修改时间) {
                    简洁日志管理器.上次文件修改时间 = 当前修改时间;

                    // 读取新的异步日志
                    var 异步日志列表 = 简洁日志管理器.读取异步日志();

                    // 只显示新增的日志（简化处理，显示所有）
                    for (var i = 0; i < 异步日志列表.length; i++) {
                        var 日志项 = 异步日志列表[i];
                        // 检查是否是新日志（时间戳大于上次检查时间）
                        if (日志项.时间戳 > (简洁日志管理器.上次文件修改时间 - 5000)) {
                            添加日志(日志项.消息, 日志项.类型);
                        }
                    }
                }
            } catch (e) {
                // 静默处理监听错误，避免干扰用户
            }
        }, 2000);

        console.log("异步日志文件监听已启动");
    } catch (e) {
        console.error("启动异步日志监听失败:", e);
    }
}

/**
 * 获取日志列表
 * @param {String} 类型过滤 - 可选，按类型过滤日志
 * @returns {Array} 日志列表
 */
function 获取日志列表(类型过滤) {
    try {
        if (类型过滤) {
            return 日志状态.日志列表.filter(日志 => 日志.类型 === 类型过滤);
        }
        return 日志状态.日志列表.slice(); // 返回副本
    } catch (e) {
        console.error("获取日志列表失败:", e);
        return [];
    }
}

/**
 * 导出日志到文件
 * @returns {Boolean} 是否导出成功
 */
function 导出日志() {
    try {
        var 日志内容 = "Magic 游戏辅助脚本 - 运行日志\n";
        日志内容 += "导出时间: " + new Date().toLocaleString() + "\n";
        日志内容 += "=" * 50 + "\n\n";

        日志状态.日志列表.forEach(function(日志) {
            日志内容 += "[" + 日志.时间.toLocaleString() + "] ";
            日志内容 += "[" + 日志.类型 + "] ";
            日志内容 += 日志.内容 + "\n";
        });

        // 保存到文件
        var 文件路径 = "/sdcard/Magic游戏助手/日志/" + new Date().toISOString().slice(0, 10) + "_log.txt";
        files.ensureDir("/sdcard/Magic游戏助手/日志/");
        files.write(文件路径, 日志内容);

        显示气泡提示("日志已导出到: " + 文件路径, "成功");
        return true;
    } catch (e) {
        console.error("导出日志失败:", e);
        显示气泡提示("导出日志失败: " + e.message, "错误");
        return false;
    }
}

/**
 * 切换到指定页面
 */
function 切换到页面(页面名称) {
    try {
        console.log("从日志页面切换到:", 页面名称);

        // 根据页面名称加载对应的页面内容
        switch (页面名称) {
            case "日志":
                // 日志页面已经是当前页面，无需切换
                console.log("当前已在日志页面");
                break;
            case "主页":
                // 使用ui.layout重新加载主页页面
                var 主页页面 = require('../主页/UI主页页面.js');
                ui.layout(主页页面.布局);

                // 初始化主页逻辑
                var 主页逻辑 = require('../主页/主页逻辑.js');
                if (主页逻辑.初始化主页逻辑) {
                    主页逻辑.初始化主页逻辑();
                }
                break;
            case "脚本配置":
                // 使用ui.layout重新加载脚本配置页面
                var 脚本页面 = require('../脚本配置页/UI脚本页面.js');
                ui.layout(脚本页面.布局);

                // 初始化脚本配置页面逻辑
                var 脚本逻辑 = require('../脚本配置页/脚本逻辑.js');
                if (脚本逻辑.初始化脚本逻辑) {
                    脚本逻辑.初始化脚本逻辑();
                }
                break;
            default:
                console.warn("未知页面:", 页面名称);
                显示气泡提示("未知页面: " + 页面名称, "警告");
                return;
        }

        // 显示切换成功提示
        显示气泡提示("已切换到" + 页面名称 + "页面");

    } catch (e) {
        console.error("切换页面失败:", e);
        显示气泡提示("页面切换失败: " + e.message, "错误");
    }
}

/**
 * 初始化抽屉逻辑
 */
function 初始化抽屉逻辑() {
    try {
        var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
        抽屉逻辑.初始化抽屉逻辑("日志");
        console.log("日志页抽屉逻辑初始化完成");
    } catch (e) {
        console.error("初始化抽屉逻辑失败:", e);
    }
}

/**
 * 打开抽屉页面
 */
function 打开抽屉页面() {
    try {
        console.log("从日志页面打开抽屉");

        // 导入侧滑抽屉模块
        var 侧滑抽屉 = require('../菜单抽屉页/侧滑抽屉.js');

        // 打开侧滑抽屉
        侧滑抽屉.打开抽屉();

        console.log("抽屉页已打开");

    } catch (e) {
        console.error("打开抽屉页面失败:", e);
        显示气泡提示("打开菜单失败: " + e.message, "错误");
    }
}

/**
 * 更新界面状态
 */
function 更新界面状态() {
    try {
        // 更新导航栏状态，确保日志标签为激活状态
        更新导航栏状态("日志");
    } catch (e) {
        console.error("更新界面状态失败:", e);
    }
}

/**
 * 更新导航栏状态
 */
function 更新导航栏状态(当前页面) {
    try {
        // 重置所有导航图标为非激活状态（半透明白色）
        安全设置控件颜色(ui.脚本图标, "#CCFFFFFF");
        安全设置控件颜色(ui.主页图标, "#CCFFFFFF");
        安全设置控件颜色(ui.日志图标, "#CCFFFFFF");

        // 设置当前页面图标为激活状态（纯白色）
        switch (当前页面) {
            case "脚本配置":
                安全设置控件颜色(ui.脚本图标, 全局样式.颜色主题.白色);
                break;
            case "主页":
                安全设置控件颜色(ui.主页图标, 全局样式.颜色主题.白色);
                break;
            case "日志":
                安全设置控件颜色(ui.日志图标, 全局样式.颜色主题.白色);
                break;
        }
    } catch (e) {
        console.error("更新导航栏状态失败:", e);
    }
}

/**
 * 安全设置控件颜色
 * @param {Object} 控件
 * @param {String} 颜色
 */
function 安全设置控件颜色(控件, 颜色) {
    try {
        if (控件 && 颜色) {
            // 优先使用新API
            if (控件.attr && typeof 控件.attr === 'function') {
                控件.attr("textColor", 颜色);
            } else if (控件.setTextColor && typeof 控件.setTextColor === 'function') {
                控件.setTextColor(colors.parseColor(颜色));
            }
            // 静默处理不支持的方法，避免控制台警告
        }
    } catch (e) {
        // 静默处理错误，避免控制台警告
    }
}

/**
 * 显示气泡提示
 */
function 显示气泡提示(消息, 类型) {
    try {
        日志界面.显示提示(消息, 类型);
    } catch (e) {
        console.error("显示气泡提示失败:", e);
        // 备用提示方式
        toast(消息);
    }
}

/**
 * 获取日志统计信息
 * @returns {Object} 日志统计对象
 */
function 获取日志统计() {
    try {
        var 统计 = {
            总数: 日志状态.日志列表.length,
            成功: 0,
            警告: 0,
            错误: 0,
            信息: 0
        };

        日志状态.日志列表.forEach(function(日志) {
            switch (日志.类型) {
                case "成功":
                    统计.成功++;
                    break;
                case "警告":
                    统计.警告++;
                    break;
                case "错误":
                    统计.错误++;
                    break;
                default:
                    统计.信息++;
            }
        });

        return 统计;
    } catch (e) {
        console.error("获取日志统计失败:", e);
        return { 总数: 0, 成功: 0, 警告: 0, 错误: 0, 信息: 0 };
    }
}

/**
 * 设置日志自动滚动
 * @param {Boolean} 启用 - 是否启用自动滚动
 */
function 设置自动滚动(启用) {
    日志状态.自动滚动 = 启用;
}

/**
 * 获取日志状态
 * @returns {Object} 日志状态对象
 */
function 获取日志状态() {
    return 日志状态;
}

// 导出模块功能
module.exports = {
    初始化日志逻辑: 初始化日志逻辑,
    添加日志: 添加日志,
    清空所有日志: 清空所有日志,
    获取日志列表: 获取日志列表,
    导出日志: 导出日志,
    切换到页面: 切换到页面,
    打开抽屉页面: 打开抽屉页面,
    更新界面状态: 更新界面状态,
    显示气泡提示: 显示气泡提示,
    获取日志统计: 获取日志统计,
    设置自动滚动: 设置自动滚动,
    获取日志状态: 获取日志状态
};