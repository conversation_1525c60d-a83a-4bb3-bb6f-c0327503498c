

//简洁代码编写规则示例

// 查找图片函数：查找_图片(图片路径, "点击", 等待秒数, 间隔毫秒)

function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) {
    等待秒数 = 等待秒数 || 1;        // 默认只等1秒（找一次）
    间隔毫秒 = 间隔毫秒 || 500;      // 默认每500毫秒找一次

    var 结束时间 = new Date().getTime() + 等待秒数 * 1000;  // 计算结束时间

    do {
        // 读取图片并在屏幕上查找
        var img = images.read(图片路径);
        var pos = images.findImage(captureScreen(), img);
        img.recycle();  // 释放图片内存

        if (pos) {  // 找到图片了
            if (动作 == "点击") {
                click(pos.x, pos.y);  // 立即点击
                return true;          // 返回点击成功
            }
            return pos;  // 返回图片位置坐标
        }

        // 如果需要等待且还没到结束时间，就休眠一下再继续找
        if (等待秒数 > 1) sleep(间隔毫秒);
    } while (new Date().getTime() < 结束时间);  // 在时间范围内循环查找

    return null;  // 超时未找到，返回null
}

// 使用示例
查找_图片("./assets/google/google邮箱.png");                           // 只找图一次
查找_图片("./assets/google/google邮箱.png", "点击");                   // 找图一次，找到立即点击
查找_图片("/sdcard/my_image.png", "点击", 10);                       // 在10秒内反复找图，一找到就立即点击
查找_图片("./assets/google/google密码界面.png", "点击", 5, 200);        // 在5秒内每隔200毫秒找一次图，一找到就立即点击