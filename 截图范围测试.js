/**
 * 截图范围测试脚本
 * 用于测试指定范围的截图功能
 * 范围：x=204, y=630, w=298, h=216
 */

function 截图范围测试() {
    try {
        console.log("🔍 开始截图范围测试");

        // 请求截图权限
        if (!requestScreenCapture()) {
            console.log("❌ 请求截图权限失败");
            return false;
        }

        console.log("✅ 截图权限获取成功");

        // 等待一下确保权限生效
        sleep(2000);

        // 截取全屏
        console.log("📸 开始截取全屏");
        var 全屏截图 = captureScreen();

        if (!全屏截图) {
            console.log("❌ 全屏截图失败");
            return false;
        }

        console.log("✅ 全屏截图成功，尺寸: " + 全屏截图.getWidth() + "x" + 全屏截图.getHeight());

        // 指定截图范围
        var 区域x = 204;
        var 区域y = 630;
        var 区域宽 = 298;
        var 区域高 = 216;

        console.log("🎯 截图范围: x=" + 区域x + ", y=" + 区域y + ", w=" + 区域宽 + ", h=" + 区域高);

        // 检查范围是否超出屏幕
        if (区域x + 区域宽 > 全屏截图.getWidth() || 区域y + 区域高 > 全屏截图.getHeight()) {
            console.log("⚠️ 警告：截图范围超出屏幕边界");
            console.log("屏幕尺寸: " + 全屏截图.getWidth() + "x" + 全屏截图.getHeight());
            console.log("范围右下角: " + (区域x + 区域宽) + "x" + (区域y + 区域高));
        }

        // 剪切指定区域
        console.log("✂️ 开始剪切指定区域");
        var 区域截图 = images.clip(全屏截图, 区域x, 区域y, 区域宽, 区域高);

        if (!区域截图) {
            console.log("❌ 区域截图失败");
            全屏截图.recycle();
            return false;
        }

        console.log("✅ 区域截图成功，尺寸: " + 区域截图.getWidth() + "x" + 区域截图.getHeight());

        // 保存截图到指定路径
        var 保存路径 = "/storage/emulated/0/截图范围测试.png";
        console.log("💾 保存截图到: " + 保存路径);

        var 保存结果 = images.save(区域截图, 保存路径);

        if (保存结果) {
            console.log("✅ 截图保存成功: " + 保存路径);
        } else {
            console.log("❌ 截图保存失败");
        }

        // 释放图片资源
        全屏截图.recycle();
        区域截图.recycle();

        console.log("🎉 截图范围测试完成");
        return 保存结果;

    } catch (error) {
        console.error("❌ 截图范围测试发生错误: " + error);
        return false;
    }
}

// 执行测试
截图范围测试();