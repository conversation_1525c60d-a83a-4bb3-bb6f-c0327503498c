# Magic 游戏辅助脚本项目结构

## 项目概述

Magic是一个基于AutoXjs ozobiozobi魔改版v6.5.8.17开发的游戏辅助脚本应用，采用Android原生XML布局和AutoXjs内置UI组件构建用户界面。

## 完整项目结构

```
Magic/
├── main.js                          # 应用入口文件
├── project.json                     # 项目配置文件
├── README.md                        # 项目开发规范文档
├── 项目结构.md                      # 项目结构说明文档
├── 第三方库.md                      # 第三方库说明文档
├── 开发进度.md                      # 开发进度跟踪文档
├── 开发需求.md                      # 开发需求管理文档
├── 问题报告.md                      # 问题报告和bug跟踪文档
│
├── ui/                              # 用户界面目录
│   ├── 主页/                        # 主页模块
│   │   ├── UI主页页面.js            # 主页UI界面定义
│   │   └── 主页逻辑.js              # 主页业务逻辑
│   │
│   ├── 日志页/                      # 日志页模块
│   │   ├── UI日志页面.js            # 日志页UI界面定义
│   │   └── 日志逻辑.js              # 日志页业务逻辑
│   │
│   ├── 脚本配置页/                  # 脚本配置页模块
│   │   ├── UI脚本页面.js            # 脚本配置UI界面定义
│   │   └── 脚本逻辑.js              # 脚本配置业务逻辑
│   │
│   ├── 菜单抽屉页/                  # 菜单抽屉页模块
│   │   ├── UI抽屉页面.js            # 抽屉菜单UI界面定义
│   │   └── 抽屉页逻辑.js            # 抽屉菜单业务逻辑
│   │
│   ├── 全局样式/                    # 全局样式模块
│   │   └── 公共样式.js              # 全局公共样式定义
│   │
│   ├── 图标库/                      # 图标库模块
│   │   ├── 原生图标库.js            # 原生图标定义
│   │   └── 图片图标库.js            # 图片图标定义
│   │
│   └── 通用组件/                    # 通用组件模块
│       └── (空目录)                 # 待开发通用UI组件
│
├── 脚本/                            # 脚本逻辑目录
│   ├── 主脚本.js                    # 主要脚本逻辑
│   └── 辅助.js                      # 辅助功能脚本
│
├── assets/                          # 资源文件目录
│   ├── 应用图标/                    # 应用图标资源
│   │   └── 应用图标.png             # 应用主图标
│   ├── 广告模板/                    # 广告模板资源
│   └── 通用模板/                    # 通用模板资源
│
├── 存储数据/                        # 数据存储目录
│   └── 配置管理.js                  # 配置数据管理
│
└── config/                          # 配置文件目录
    └── (空目录)                     # 待添加配置文件
```

## 目录结构说明

### 核心文件
- **main.js**: 应用程序的入口文件，负责初始化应用和启动主界面
- **project.json**: AutoXjs项目配置文件，定义项目基本信息和构建配置
- **README.md**: 项目开发规范和技术文档
- **项目结构.md**: 当前文件，项目结构说明文档
- **第三方库.md**: 第三方库使用说明和依赖管理

### 项目管理文件
- **开发进度.md**: 开发进度跟踪文档，记录项目开发状态和里程碑
- **开发需求.md**: 开发需求管理文档，记录功能需求和优先级
- **问题报告.md**: 问题报告和bug跟踪文档，记录已知问题和解决方案

### UI界面模块 (ui/)
采用模块化设计，每个功能页面独立成一个目录，包含UI定义和业务逻辑：

**主页模块 (ui/主页/)**
- UI主页页面.js: 定义主页的XML布局和控件
- 主页逻辑.js: 处理主页的业务逻辑和事件响应

**日志页模块 (ui/日志页/)**
- UI日志页面.js: 定义日志显示界面的布局
- 日志逻辑.js: 处理日志收集、过滤和显示逻辑

**脚本配置页模块 (ui/脚本配置页/)**
- UI脚本页面.js: 定义脚本配置界面的布局
- 脚本逻辑.js: 处理脚本配置的业务逻辑

**菜单抽屉页模块 (ui/菜单抽屉页/)**
- UI抽屉页面.js: 定义侧边栏抽屉菜单的布局
- 抽屉页逻辑.js: 处理抽屉菜单的交互逻辑

**全局样式模块 (ui/全局样式/)**
- 公共样式.js: 定义全局通用的样式和主题

**图标库模块 (ui/图标库/)**
- 原生图标库.js: 定义使用文本符号的原生图标
- 图片图标库.js: 定义使用图片资源的图标

**通用组件模块 (ui/通用组件/)**
- 目前为空目录，预留用于存放可复用的UI组件

### 脚本逻辑模块 (脚本/)
- **主脚本.js**: 核心游戏辅助脚本逻辑
- **辅助.js**: 辅助功能和工具函数

### 资源文件模块 (assets/)
- **应用图标/**: 存放应用图标资源
- **广告模板/**: 存放广告相关的模板资源
- **通用模板/**: 存放通用的模板资源

### 数据存储模块 (存储数据/)
- **配置管理.js**: 负责应用配置数据的存储和管理

### 配置文件模块 (config/)
- 目前为空目录，预留用于存放各种配置文件

## 设计原则

### 模块化设计
每个功能模块独立成目录，包含UI定义和业务逻辑，便于维护和扩展。

### 职责分离
UI定义文件专注于界面布局和控件定义，逻辑文件专注于业务处理和事件响应。

### 中文命名
所有自定义文件和目录使用中文命名，提高代码可读性和维护性。

### 资源集中管理
所有静态资源集中存放在assets目录，便于管理和引用。

## 开发规范

### 文件命名规范
- UI文件: UI[功能名]页面.js
- 逻辑文件: [功能名]逻辑.js
- 配置文件: [功能名]配置.js
- 工具文件: [功能名]工具.js

### 目录组织规范
- 每个功能模块独立成目录
- UI相关文件放在ui/目录下
- 脚本逻辑放在脚本/目录下
- 静态资源放在assets/目录下
- 配置数据放在存储数据/目录下

### 模块依赖规范
- 使用require()引入模块
- 避免循环依赖
- 明确模块接口和导出

## 扩展计划

### 待开发模块
- ui/通用组件/: 开发可复用的UI组件
- config/: 添加各种配置文件

### 项目管理文档
- 开发进度.md: 完善开发进度跟踪内容
- 开发需求.md: 完善需求管理和优先级规划
- 问题报告.md: 建立问题跟踪和解决流程

### 功能扩展
- 添加更多游戏辅助功能模块
- 完善日志系统和错误处理
- 增加用户设置和个性化配置
- 优化性能和用户体验

---

**最后更新时间**: 2025年1月
**文档版本**: v1.1 (新增项目管理文档)
**基于项目**: Magic 游戏辅助脚本 v1.0