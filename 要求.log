03:31:40.387/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/ocr看广告.js]
03:31:40.408/D: 
=== 看广告精准符号识别功能开始 ===
03:31:40.413/D: ⏰ 等待3秒后开始识别...
03:31:43.417/D: 📸 开始截图识别
03:31:43.594/D: <time>: 2025-08-03 03:31:43.593
	<line>: 1056
	<line>: 36 (看广告)
<msg>: 开始全屏OCR符号识别，配置: {"CPU核心数":4,"使用快速模型":false,"置信度阈值":0.1,"启用二值化":true,"二值化阈值":186,"二值化最大值":255,"反向二值化阈值":241,"反向二值化最大值":255,"启用图片匹配":true,"图片匹配阈值":0.8,"图片模板路径":"../../assets/算数游戏/广告/右广告","使用中心点坐标":true}

03:31:43.594/D: 🚀 启动统一识别符号引擎...
03:31:43.595/D: 📷 第一阶段：开始原图OCR识别...
03:31:46.687/D:    ├─ 原图OCR识别完成，耗时: 3091ms，识别到: 5 个文本
03:31:46.692/D:    └─ 原图结果处理完成，耗时: 4ms，符号数量: 1
03:31:46.693/D: ✅ 第一阶段完成，总耗时: 3098ms
03:31:46.693/D: 🔄 第二阶段：开始多阈值二值化处理与识别...
03:31:46.734/D:    ├─ 多阈值图像处理完成，耗时: 40ms，模式: 全部
03:31:48.609/D:    ├─ 二值化OCR识别完成，耗时: 1872ms，识别到: 4 个文本
03:31:48.612/D:    ├─ 二值化结果处理完成，耗时: 1ms，符号数量: 2
03:31:49.833/D:    ├─ 反向二值化 OCR识别完成，耗时: 1217ms，识别到: 2 个文本
03:31:49.834/D:    ├─ 反向二值化 结果处理完成，耗时: 0ms，符号数量: 0
03:31:49.837/D: ✅ 第二阶段完成，总耗时: 3144ms
03:31:49.838/D: 🖼️ 第三阶段：开始图片模板匹配识别...
03:31:56.387/D:    └─ 图片模板匹配完成，耗时: 6548ms，符号数量: 2
03:31:56.389/D: ✅ 第三阶段完成，总耗时: 6551ms
03:31:56.391/D: 🔗 第四阶段：开始智能结果合并...
03:31:56.392/D:    ├─ 原图结果合并完成，耗时: 0ms，添加: 1 个符号
03:31:56.394/D:    ├─ 二值化结果合并完成，耗时: 0ms，新增: 1 个，替换: 0 个，跳过: 1 个
03:31:56.394/D:    ├─ 反向二值化结果合并完成，耗时: 0ms，新增: 0 个，替换: 0 个，跳过: 0 个
03:31:56.395/D:    ├─ 图片匹配结果合并完成，耗时: 0ms，新增: 0 个，替换: 1 个，跳过: 1 个
03:31:56.396/D:    └─ 最终结果构建完成，耗时: 0ms
03:31:56.397/D: ✅ 第四阶段完成，总耗时: 6ms
03:31:56.398/D: 🎯 ===== 统一识别符号引擎性能分析 =====
03:31:56.399/D:    📊 各阶段耗时分布:
03:31:56.399/D:    ├─ 第一阶段（原图识别）: 3098ms
03:31:56.400/D:    ├─ 第二阶段（多阈值二值化）: 3144ms
03:31:56.401/D:    ├─ 第三阶段（图片匹配）: 6551ms
03:31:56.401/D:    └─ 第四阶段（结果合并）: 6ms
03:31:56.402/D:    🏆 总耗时: 12804ms，最终识别到: 2 个符号
03:31:56.403/D:    📈 平均置信度: 0.878
03:31:56.403/D:    📝 识别符号: [X-9, TO×REVEAL]
03:31:56.407/D: ⚡ 统一识别符号引擎完成！
03:31:56.408/D: 
📋 符号识别结果:
03:31:56.409/D:   🔣 识别到符号: 2 个
03:31:56.410/D: 🔣 符号详情: X-9(30,30) TO×REVEAL(395,410)
03:31:56.575/D: ✅ 符号点击成功: "X-9" 坐标(30, 30)
03:31:58.577/D: 🎯 第八步：检查游戏状态...
03:31:58.580/D: 🔍 当前应用包名: bin.mt.plus
03:31:58.582/D: ⚠️ 算数游戏不在前台，正在切换回算数游戏...
03:31:58.731/D: ✅ 已切换回算数游戏应用
03:31:58.837/D: 🔍 检查是否返回游戏开始界面...
03:31:58.841/D: 🔍 图片路径: ../../assets/算数游戏/新手教程图片/游戏开始界面结束英文.png
03:31:58.973/D: 🔍 未检测到游戏开始界面，继续执行
03:31:58.974/D: 📊 算数游戏看广告程序执行完成
03:31:58.976/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/ocr看广告.js ]运行结束，用时18.582000秒
