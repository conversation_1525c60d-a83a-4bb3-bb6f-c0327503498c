{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-03T08:01:26.814Z", "updatedAt": "2025-08-03T08:01:26.820Z", "resourceCount": 6}, "resources": [{"id": "autoxjs-game-expert", "source": "project", "protocol": "role", "name": "Autoxjs Game Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/autoxjs-game-expert.role.md", "metadata": {"createdAt": "2025-08-03T08:01:26.815Z", "updatedAt": "2025-08-03T08:01:26.815Z", "scannedAt": "2025-08-03T08:01:26.815Z", "path": "domain/autoxjs-game-expert/autoxjs-game-expert.role.md"}}, {"id": "autoxjs-development-workflow", "source": "project", "protocol": "execution", "name": "Autoxjs Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/execution/autoxjs-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-03T08:01:26.816Z", "updatedAt": "2025-08-03T08:01:26.816Z", "scannedAt": "2025-08-03T08:01:26.816Z", "path": "domain/autoxjs-game-expert/execution/autoxjs-development-workflow.execution.md"}}, {"id": "simple-code-rules", "source": "project", "protocol": "execution", "name": "Simple Code Rules 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/execution/simple-code-rules.execution.md", "metadata": {"createdAt": "2025-08-03T08:01:26.817Z", "updatedAt": "2025-08-03T08:01:26.817Z", "scannedAt": "2025-08-03T08:01:26.817Z", "path": "domain/autoxjs-game-expert/execution/simple-code-rules.execution.md"}}, {"id": "autoxjs-ozobiozobi-expertise", "source": "project", "protocol": "knowledge", "name": "Autoxjs Ozobiozobi Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/knowledge/autoxjs-ozobiozobi-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-03T08:01:26.818Z", "updatedAt": "2025-08-03T08:01:26.818Z", "scannedAt": "2025-08-03T08:01:26.818Z", "path": "domain/autoxjs-game-expert/knowledge/autoxjs-ozobiozobi-expertise.knowledge.md"}}, {"id": "magic-project-standards", "source": "project", "protocol": "knowledge", "name": "Magic Project Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/knowledge/magic-project-standards.knowledge.md", "metadata": {"createdAt": "2025-08-03T08:01:26.818Z", "updatedAt": "2025-08-03T08:01:26.818Z", "scannedAt": "2025-08-03T08:01:26.818Z", "path": "domain/autoxjs-game-expert/knowledge/magic-project-standards.knowledge.md"}}, {"id": "autoxjs-expert-thinking", "source": "project", "protocol": "thought", "name": "Autoxjs Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/thought/autoxjs-expert-thinking.thought.md", "metadata": {"createdAt": "2025-08-03T08:01:26.819Z", "updatedAt": "2025-08-03T08:01:26.819Z", "scannedAt": "2025-08-03T08:01:26.819Z", "path": "domain/autoxjs-game-expert/thought/autoxjs-expert-thinking.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 1, "execution": 2, "knowledge": 2, "thought": 1}, "bySource": {"project": 6}}}