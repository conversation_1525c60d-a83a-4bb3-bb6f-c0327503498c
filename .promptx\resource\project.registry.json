{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-03T07:46:47.325Z", "updatedAt": "2025-08-03T07:46:47.392Z", "resourceCount": 6}, "resources": [{"id": "autoxjs-game-expert", "source": "project", "protocol": "role", "name": "Autoxjs Game Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/autoxjs-game-expert.role.md", "metadata": {"createdAt": "2025-08-03T07:46:47.337Z", "updatedAt": "2025-08-03T07:46:47.337Z", "scannedAt": "2025-08-03T07:46:47.337Z", "path": "domain/autoxjs-game-expert/autoxjs-game-expert.role.md"}}, {"id": "autoxjs-development-workflow", "source": "project", "protocol": "execution", "name": "Autoxjs Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/execution/autoxjs-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-03T07:46:47.348Z", "updatedAt": "2025-08-03T07:46:47.348Z", "scannedAt": "2025-08-03T07:46:47.348Z", "path": "domain/autoxjs-game-expert/execution/autoxjs-development-workflow.execution.md"}}, {"id": "simple-code-rules", "source": "project", "protocol": "execution", "name": "Simple Code Rules 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/execution/simple-code-rules.execution.md", "metadata": {"createdAt": "2025-08-03T07:46:47.358Z", "updatedAt": "2025-08-03T07:46:47.358Z", "scannedAt": "2025-08-03T07:46:47.358Z", "path": "domain/autoxjs-game-expert/execution/simple-code-rules.execution.md"}}, {"id": "autoxjs-ozobiozobi-expertise", "source": "project", "protocol": "knowledge", "name": "Autoxjs Ozobiozobi Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/knowledge/autoxjs-ozobiozobi-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-03T07:46:47.371Z", "updatedAt": "2025-08-03T07:46:47.371Z", "scannedAt": "2025-08-03T07:46:47.371Z", "path": "domain/autoxjs-game-expert/knowledge/autoxjs-ozobiozobi-expertise.knowledge.md"}}, {"id": "magic-project-standards", "source": "project", "protocol": "knowledge", "name": "Magic Project Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/knowledge/magic-project-standards.knowledge.md", "metadata": {"createdAt": "2025-08-03T07:46:47.381Z", "updatedAt": "2025-08-03T07:46:47.381Z", "scannedAt": "2025-08-03T07:46:47.381Z", "path": "domain/autoxjs-game-expert/knowledge/magic-project-standards.knowledge.md"}}, {"id": "autoxjs-expert-thinking", "source": "project", "protocol": "thought", "name": "Autoxjs Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/autoxjs-game-expert/thought/autoxjs-expert-thinking.thought.md", "metadata": {"createdAt": "2025-08-03T07:46:47.391Z", "updatedAt": "2025-08-03T07:46:47.391Z", "scannedAt": "2025-08-03T07:46:47.391Z", "path": "domain/autoxjs-game-expert/thought/autoxjs-expert-thinking.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 1, "execution": 2, "knowledge": 2, "thought": 1}, "bySource": {"project": 6}}}