/**
 * Magic游戏辅助脚本 - 坐标辅助模块
 * 提供坐标常量定义和距离计算功能
 */

// ==================== 坐标常量定义 ====================

/**
 * 原点坐标
 * @type {Object}
 */
const 原点 = {
    x: 267,
    y: 803
};

/**
 * 障碍物坐标
 * @type {Object}
 */
const 障碍物 = {
    x: 87,
    y: 237
};

// ==================== 距离计算功能 ====================

/**
 * 计算原点与障碍物的Y轴距离
 * @returns {number} Y轴距离（绝对值）
 */
function 计算Y轴距离() {
    const Y轴距离 = Math.abs(原点.y - 障碍物.y);
    console.log("=== 坐标距离计算 ===");
    console.log("原点坐标:", 原点.x + "," + 原点.y);
    console.log("障碍物坐标:", 障碍物.x + "," + 障碍物.y);
    console.log("Y轴距离:", Y轴距离 + "像素");
    console.log("==================");
    return Y轴距离;
}

/**
 * 计算两点之间的直线距离
 * @returns {number} 直线距离
 */
function 计算直线距离() {
    const deltaX = 原点.x - 障碍物.x;
    const deltaY = 原点.y - 障碍物.y;
    const 直线距离 = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    console.log("X轴距离:", Math.abs(deltaX) + "像素");
    console.log("Y轴距离:", Math.abs(deltaY) + "像素");
    console.log("直线距离:", 直线距离.toFixed(2) + "像素");
    return 直线距离;
}

/**
 * 获取坐标信息
 * @returns {Object} 包含所有坐标和距离信息的对象
 */
function 获取坐标信息() {
    const Y轴距离 = Math.abs(原点.y - 障碍物.y);
    const X轴距离 = Math.abs(原点.x - 障碍物.x);
    const 直线距离 = Math.sqrt(X轴距离 * X轴距离 + Y轴距离 * Y轴距离);

    return {
        原点: 原点,
        障碍物: 障碍物,
        距离: {
            X轴: X轴距离,
            Y轴: Y轴距离,
            直线: parseFloat(直线距离.toFixed(2))
        }
    };
}

// ==================== 立即计算并显示结果 ====================

// 计算Y轴距离
const Y轴距离结果 = 计算Y轴距离();

// 计算完整距离信息
console.log("\n=== 完整距离信息 ===");
计算直线距离();

// ==================== 模块导出 ====================

module.exports = {
    // 坐标常量
    原点: 原点,
    障碍物: 障碍物,

    // 计算函数
    计算Y轴距离: 计算Y轴距离,
    计算直线距离: 计算直线距离,
    获取坐标信息: 获取坐标信息,

    // 预计算结果
    Y轴距离: Y轴距离结果
};