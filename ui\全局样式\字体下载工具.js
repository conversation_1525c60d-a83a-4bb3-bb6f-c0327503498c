/**
 * FontAwesome字体下载工具
 * 提供简单的界面来下载和管理FontAwesome字体文件
 */

"ui";

var 字体管理器 = require('./字体管理器.js');

ui.layout(
    <vertical padding="16dp">
        <appbar>
            <toolbar title="FontAwesome字体下载工具" titleTextColor="#FFFFFF"/>
        </appbar>
        
        <ScrollView>
            <vertical>
                {/* 字体状态卡片 */}
                <card w="*" h="wrap_content" margin="8dp" cardCornerRadius="8dp">
                    <vertical padding="16dp">
                        <text text="字体状态" textSize="18sp" textStyle="bold" textColor="#333"/>
                        <horizontal margin="8dp 0">
                            <text text="FontAwesome 5.15.2: " textSize="14sp"/>
                            <text id="字体状态" text="检查中..." textSize="14sp" textColor="#FF9800"/>
                        </horizontal>
                        <text id="字体路径" text="" textSize="12sp" textColor="#666" margin="4dp 0"/>
                        <text id="字体大小" text="" textSize="12sp" textColor="#666"/>
                    </vertical>
                </card>

                {/* 操作按钮 */}
                <card w="*" h="wrap_content" margin="8dp" cardCornerRadius="8dp">
                    <vertical padding="16dp">
                        <text text="操作" textSize="18sp" textStyle="bold" textColor="#333"/>
                        
                        <button id="检查字体按钮" 
                                text="检查字体状态" 
                                textColor="#FFFFFF"
                                bg="#2196F3"
                                margin="4dp 0"/>
                        
                        <button id="下载字体按钮" 
                                text="下载FontAwesome字体" 
                                textColor="#FFFFFF"
                                bg="#4CAF50"
                                margin="4dp 0"/>
                        
                        <button id="删除字体按钮" 
                                text="删除字体文件" 
                                textColor="#FFFFFF"
                                bg="#F44336"
                                margin="4dp 0"/>
                        
                        <button id="测试字体按钮" 
                                text="测试字体显示" 
                                textColor="#FFFFFF"
                                bg="#9C27B0"
                                margin="4dp 0"/>
                    </vertical>
                </card>

                {/* 字体测试区域 */}
                <card w="*" h="wrap_content" margin="8dp" cardCornerRadius="8dp">
                    <vertical padding="16dp">
                        <text text="字体测试" textSize="18sp" textStyle="bold" textColor="#333"/>
                        <text text="如果字体加载成功，下面应该显示FontAwesome图标：" textSize="14sp" margin="8dp 0"/>
                        
                        <horizontal gravity="center" margin="16dp 0">
                            <text id="测试图标1" text="?" textSize="24sp" margin="8dp" textColor="#2196F3"/>
                            <text id="测试图标2" text="?" textSize="24sp" margin="8dp" textColor="#4CAF50"/>
                            <text id="测试图标3" text="?" textSize="24sp" margin="8dp" textColor="#F44336"/>
                            <text id="测试图标4" text="?" textSize="24sp" margin="8dp" textColor="#FF9800"/>
                        </horizontal>
                        
                        <text text="主页 | 设置 | 删除 | 刷新" textSize="12sp" textColor="#666" gravity="center"/>
                    </vertical>
                </card>

                {/* 说明信息 */}
                <card w="*" h="wrap_content" margin="8dp" cardCornerRadius="8dp">
                    <vertical padding="16dp">
                        <text text="使用说明" textSize="18sp" textStyle="bold" textColor="#333"/>
                        <text text="1. 点击'检查字体状态'查看当前字体文件状态" textSize="14sp" margin="4dp 0"/>
                        <text text="2. 如果字体不存在，点击'下载FontAwesome字体'进行下载" textSize="14sp" margin="4dp 0"/>
                        <text text="3. 下载完成后，点击'测试字体显示'验证字体是否正常工作" textSize="14sp" margin="4dp 0"/>
                        <text text="4. 字体文件将保存在：/storage/emulated/0/脚本/ui/全局样式/fonts/" textSize="12sp" margin="4dp 0" textColor="#666"/>
                        <text text="5. 如需重新下载，可先删除现有字体文件" textSize="14sp" margin="4dp 0"/>
                    </vertical>
                </card>
            </vertical>
        </ScrollView>
    </vertical>
);

// 初始化界面
function 初始化界面() {
    检查字体状态();
}

// 检查字体状态
function 检查字体状态() {
    try {
        var 字体信息 = 字体管理器.字体管理器.获取字体信息("FontAwesome");
        
        if (字体信息.存在) {
            ui.字体状态.setText("已安装");
            ui.字体状态.setTextColor(colors.parseColor("#4CAF50"));
            ui.字体路径.setText("路径: " + 字体信息.本地路径);
            ui.字体大小.setText("大小: " + Math.round(字体信息.大小 / 1024) + " KB");
        } else {
            ui.字体状态.setText("未安装");
            ui.字体状态.setTextColor(colors.parseColor("#F44336"));
            ui.字体路径.setText("字体文件不存在");
            ui.字体大小.setText("");
        }
    } catch (e) {
        console.error("检查字体状态失败:", e);
        ui.字体状态.setText("检查失败");
        ui.字体状态.setTextColor(colors.parseColor("#F44336"));
    }
}

// 下载字体
function 下载字体() {
    try {
        ui.下载字体按钮.setText("下载中...");
        ui.下载字体按钮.setEnabled(false);
        
        字体管理器.下载FontAwesome字体(function(成功, 消息) {
            ui.下载字体按钮.setText("下载FontAwesome字体");
            ui.下载字体按钮.setEnabled(true);
            
            if (成功) {
                toast("字体下载成功！");
                检查字体状态();
                测试字体显示();
            } else {
                toast("字体下载失败: " + 消息);
                console.error("字体下载失败:", 消息);
            }
        });
    } catch (e) {
        console.error("下载字体失败:", e);
        toast("下载失败: " + e.message);
        ui.下载字体按钮.setText("下载FontAwesome字体");
        ui.下载字体按钮.setEnabled(true);
    }
}

// 删除字体
function 删除字体() {
    try {
        dialogs.confirm("确认删除", "确定要删除FontAwesome字体文件吗？")
            .then(确认 => {
                if (确认) {
                    var 成功 = 字体管理器.字体管理器.删除字体("FontAwesome");
                    if (成功) {
                        toast("字体文件已删除");
                        检查字体状态();
                        // 重置测试图标
                        ui.测试图标1.setText("?");
                        ui.测试图标2.setText("?");
                        ui.测试图标3.setText("?");
                        ui.测试图标4.setText("?");
                    } else {
                        toast("删除失败");
                    }
                }
            });
    } catch (e) {
        console.error("删除字体失败:", e);
        toast("删除失败: " + e.message);
    }
}

// 测试字体显示
function 测试字体显示() {
    try {
        字体管理器.获取FontAwesome字体(function(fontAwesome, 错误信息) {
            if (fontAwesome) {
                // 导入图标库
                var FA图标库 = require('./FontAwesome图标库.js');
                
                // 设置测试图标
                var 主页信息 = FA图标库.获取完整图标('导航', '主页');
                ui.测试图标1.setText(主页信息.字符);
                ui.测试图标1.setTypeface(fontAwesome);
                
                var 设置信息 = FA图标库.获取完整图标('工具', '设置');
                ui.测试图标2.setText(设置信息.字符);
                ui.测试图标2.setTypeface(fontAwesome);
                
                var 删除信息 = FA图标库.获取完整图标('操作', '删除');
                ui.测试图标3.setText(删除信息.字符);
                ui.测试图标3.setTypeface(fontAwesome);
                
                var 刷新信息 = FA图标库.获取完整图标('操作', '刷新');
                ui.测试图标4.setText(刷新信息.字符);
                ui.测试图标4.setTypeface(fontAwesome);
                
                toast("字体测试成功！");
            } else {
                toast("字体测试失败: " + 错误信息);
                ui.测试图标1.setText("✗");
                ui.测试图标2.setText("✗");
                ui.测试图标3.setText("✗");
                ui.测试图标4.setText("✗");
            }
        });
    } catch (e) {
        console.error("测试字体失败:", e);
        toast("测试失败: " + e.message);
    }
}

// 注册事件监听器
ui.检查字体按钮.on("click", function() {
    检查字体状态();
});

ui.下载字体按钮.on("click", function() {
    下载字体();
});

ui.删除字体按钮.on("click", function() {
    删除字体();
});

ui.测试字体按钮.on("click", function() {
    测试字体显示();
});

// 启动初始化
初始化界面();
