/**
 * Magic 游戏辅助脚本 - 全局样式定义
 * 基于AutoXjs原生UI组件的样式系统
 * 参考WeUI设计规范，统一使用#00A843主题色
 *
 * 设计原则：
 * 1. 统一的绿色主题和视觉风格
 * 2. 响应式布局适配不同屏幕尺寸
 * 3. 简洁现代的界面设计
 * 4. 良好的用户体验和交互反馈
 */

// 全局颜色主题 - 统一使用浅绿色主题
var 颜色主题 = {
    // 主色调 - 统一浅绿色主题
    成功绿: "#4CAF50",         // 成功绿 - 强调颜色
    白色: "#FFFFFF",           // 白色 - 次要颜色

    // 辅助色 - 保留日志系统需要的颜色
    成功色: "#4CAF50",         // 成功绿色
    警告色: "#FF9500",         // 橙色
    错误色: "#FF3B30",         // 红色
    信息色: "#4CAF50",         // 信息绿色

    // 中性色
    文字主色: "#000000",       // 黑色文字
    文字次色: "#666666",       // 灰色文字
    文字禁用: "#CCCCCC",       // 禁用文字
    文字反色: "#FFFFFF",       // 白色文字

    // 背景色
    背景主色: "#F5F5F5",       // 浅灰背景
    背景次色: "#FFFFFF",       // 白色背景
    背景禁用: "#FAFAFA",       // 禁用背景
    分割线色: "#E0E0E0"        // 分割线颜色
};

// 全局尺寸规范
var 尺寸规范 = {
    // 间距
    间距超小: "4dp",
    间距小: "8dp",
    间距中: "16dp",
    间距大: "24dp",
    间距超大: "32dp",

    // 字体大小
    字体超小: "10sp",
    字体小: "12sp",
    字体中: "14sp",
    字体大: "16sp",
    字体超大: "18sp",
    字体标题: "20sp",
    字体大标题: "24sp",

    // 控件高度
    按钮高度: "48dp",
    小按钮高度: "32dp",
    输入框高度: "40dp",
    列表项高度: "56dp",
    工具栏高度: "56dp",
    开关高度: "32dp"
};





// 输入框样式
var 输入框样式 = {
    // 标准输入框
    标准输入框: {
        background: 颜色主题.背景次色,
        textColor: 颜色主题.文字主色,
        textColorHint: 颜色主题.文字次色,
        textSize: 尺寸规范.字体中,
        padding: 尺寸规范.间距中,
        stroke: "1dp " + 颜色主题.分割线色,
        minHeight: 尺寸规范.输入框高度
    },

    // 聚焦输入框 - 成功绿边框
    聚焦输入框: {
        background: 颜色主题.白色,
        textColor: 颜色主题.文字主色,
        textColorHint: 颜色主题.文字次色,
        textSize: 尺寸规范.字体中,
        padding: 尺寸规范.间距中,
        stroke: "2dp " + 颜色主题.成功绿,
        minHeight: 尺寸规范.输入框高度
    }
};

// 开关样式 - 统一白色主题
var 开关样式 = {
    // 标准开关 - 白色主题，不使用蓝色
    标准开关: {
        trackColor: 颜色主题.分割线色,        // 未激活轨道 - 灰色
        thumbColor: 颜色主题.白色,           // 未激活按钮 - 白色
        checkedTrackColor: 颜色主题.白色,    // 激活轨道 - 白色
        checkedThumbColor: 颜色主题.白色,    // 激活按钮 - 白色
        elevation: "2dp"
    },

    // 配置页开关 - 成功绿激活，浅灰关闭
    配置页开关: {
        trackColor: "#E0E0E0",               // 未激活轨道 - 浅灰色
        thumbColor: "#FFFFFF",               // 未激活按钮 - 白色
        checkedTrackColor: 颜色主题.成功绿,  // 激活轨道 - 成功绿
        checkedThumbColor: "#FFFFFF",        // 激活按钮 - 白色
        elevation: "2dp"
    }
};

// 导航栏样式 - 统一成功绿主题
var 导航栏样式 = {
    // 主导航栏 - 成功绿背景
    主导航栏: {
        background: 颜色主题.成功绿,
        titleTextColor: 颜色主题.文字主色,
        titleTextSize: 尺寸规范.字体超大,
        elevation: "4dp",
        height: 尺寸规范.工具栏高度
    },

    // 次导航栏 - 白色背景
    次导航栏: {
        background: 颜色主题.白色,
        titleTextColor: 颜色主题.文字主色,
        titleTextSize: 尺寸规范.字体大,
        elevation: "2dp",
        height: 尺寸规范.工具栏高度,
        stroke: "1dp " + 颜色主题.分割线色
    }
};

// 列表样式
var 列表样式 = {
    // 标准列表项
    标准列表项: {
        background: 颜色主题.背景次色,
        textColor: 颜色主题.文字主色,
        textSize: 尺寸规范.字体中,
        padding: 尺寸规范.间距中,
        minHeight: 尺寸规范.列表项高度
    },

    // 激活列表项 - 成功绿背景
    激活列表项: {
        background: 颜色主题.成功绿,
        textColor: 颜色主题.文字主色,
        textSize: 尺寸规范.字体中,
        padding: 尺寸规范.间距中,
        minHeight: 尺寸规范.列表项高度
    }
};

// 对话框样式
var 对话框样式 = {
    // 标准对话框
    标准对话框: {
        background: 颜色主题.背景次色,
        titleTextColor: 颜色主题.文字主色,
        titleTextSize: 尺寸规范.字体超大,
        contentTextColor: 颜色主题.文字次色,
        contentTextSize: 尺寸规范.字体中,
        elevation: "8dp"
    }
};

// 状态栏样式
var 状态栏样式 = {
    // 成功绿状态栏
    成功绿状态栏: {
        statusBarColor: 颜色主题.成功绿,
        lightStatusBar: true
    },

    // 白色状态栏
    白色状态栏: {
        statusBarColor: 颜色主题.白色,
        lightStatusBar: true
    }
};

// 导出样式对象
module.exports = {
    颜色主题: 颜色主题,
    尺寸规范: 尺寸规范,
    输入框样式: 输入框样式,
    开关样式: 开关样式,
    导航栏样式: 导航栏样式,
    列表样式: 列表样式,
    对话框样式: 对话框样式,
    状态栏样式: 状态栏样式
};