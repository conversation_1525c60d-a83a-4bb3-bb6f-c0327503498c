# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/06/22 14:41 START
我已成功激活AutoXjs游戏辅助脚本专家角色，并深度学习了两个核心知识体系：1) AutoXjs ozobiozobi v6.5.8.17专业技术知识，包括ES5语法限制、Android原生UI开发、新增功能API、推荐使用规范、XML布局格式、性能优化和错误处理等；2) Magic项目标准规范，包括项目定位、技术栈、开发原则、编码规范、架构标准、质量要求、工作流程和简洁代码编写规则。现在具备完整的专业能力为Magic游戏辅助脚本项目提供技术支持和开发指导。 --tags AutoXjs专家 Magic项目 技术知识 开发规范 专业能力
--tags #最佳实践 #流程管理 #评分:8 #有效期:长期
- END



- 2025/06/22 14:43 START
学习了Magic项目的代码简洁示例，这是一个完美体现简洁代码编写规则的实际案例。核心特点：1) 函数命名遵循动词_名词格式（查找_图片），语义清晰；2) 参数全部可调且有合理默认值（等待秒数默认1，间隔毫秒默认500）；3) 一个函数多用途，通过动作参数控制不同行为（点击或返回坐标）；4) 直线逻辑，do-while循环结构清晰；5) 及时释放资源（img.recycle()）；6) 极简注释，只在关键位置说明；7) 支持多种路径格式（相对路径、绝对路径）；8) 返回值有用且明确（成功返回true/坐标，失败返回null）。这个示例完美诠释了"用最少的代码，最灵活的参数，最简洁的命名，解决最多的实际问题"的核心原则。 --tags 简洁代码 代码规范 AutoXjs 函数设计 最佳实践
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/22 14:45 START
Magic项目完整知识库：这是一个基于AutoXjs ozobiozobi v6.5.8.17构建的专业游戏辅助脚本应用项目。项目核心技术栈包括Rhino 1.7.13 JavaScript ES5引擎、Android原生XML布局、严禁WebView/HTML/CSS技术。开发环境为VS Code + AutoXjs VSCode Extension，测试环境为雷电模拟器(540x960, DPI240)。

项目架构：main.js入口，ui/目录存放界面文件，scripts/目录存放脚本逻辑，assets/目录存放资源文件。模块化使用require/module.exports，统一导出格式为{布局: 布局变量, 初始化逻辑: 初始化函数}。

核心开发原则：1)技术准确性第一-所有代码基于官方文档，严禁虚假信息；2)问题导向开发-专注解决实际问题，禁止创建测试demo文件；3)简洁代码规则-用最少代码、最灵活参数、最简洁命名解决最多问题；4)质量保证优先-确保可读性、可维护性、性能优化。

编码规范：ES5语法、4空格缩进、分号结尾、中文注释、中文变量名配合英文API。函数命名采用动词_名词格式(如查找_图片)，不超过5个中文字。参数全部可调且有合理默认值，一个函数多用途通过参数控制行为。

技术要点：推荐使用ui.layout()替代ui.setContentView()，控件操作使用attr()方法，安全的控件访问需先检查存在性，使用traceLog()进行调试，images.captureScreen(true)强制返回新对象，及时释放图像资源避免内存泄漏。

性能标准：内存使用≤500MB，响应时间≤500ms，支持Android 9+系统。代码质量要求覆盖率≥80%，注释覆盖率关键函数100%，完善try-catch异常处理。

已成功部署PromptX MCP服务器，创建AutoXjs游戏辅助脚本专家角色，学习了完整的技术知识体系和项目标准规范，具备专业的技术支持和开发指导能力。 --tags Magic项目 AutoXjs 游戏辅助脚本 项目知识库 开发规范 技术标准
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/22 20:00 START
用户的AutoXjs编码习惯和规范：

1. 图片资源命名简洁化：
   - 不使用"查找跳过.png"这样的复合命名
   - 直接使用"跳过.png"这样的简洁命名
   - 图片文件名应该直接反映功能，避免动词前缀

2. 模块设计双重功能：
   - 每个.js文件都要支持导出模块和独立运行两种模式
   - 通过简单注释切换，不使用复杂的条件判断
   - 导出模块在上，独立运行测试在下（注释状态）

3. 函数设计原则：
   - 函数名使用"动词_名词"格式（如：登陆_游戏、登陆_帐号）
   - 参数要灵活可调，避免硬编码
   - 单个函数实现完整功能，不过度拆分

4. 资源路径规范：
   - 使用完整的绝对路径：/storage/emulated/0/magic/assets/
   - 图片分类存放在对应文件夹（如：通用模板/）
   - 文件名简洁明了，直接表达用途

5. 代码结构偏好：
   - 追求简洁，用最少代码解决最多问题
   - 避免过度模块化和复杂设计模式
   - 注释简洁，不要冗余的参数说明
   - 中文变量名配合英文API调用 --tags magic项目 编码习惯 AutoXjs 开发规范
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/22 20:03 START
用户的AutoXjs编码习惯和规范（更新版）：

1. 变量命名简洁化原则：
   - 图片变量名直接使用功能名，如：var 跳过 = images.read("跳过.png")
   - 不使用复合描述性命名，如：var 模板图片、var 跳过按钮图片
   - 变量名要简洁明了，直接表达用途

2. 图片资源命名简洁化：
   - 不使用"查找跳过.png"这样的复合命名
   - 直接使用"跳过.png"这样的简洁命名
   - 图片文件名应该直接反映功能，避免动词前缀

3. 模块设计双重功能：
   - 每个.js文件都要支持导出模块和独立运行两种模式
   - 通过简单注释切换，不使用复杂的条件判断
   - 导出模块在上，独立运行测试在下（注释状态）

4. 函数设计原则：
   - 函数名使用"动词_名词"格式（如：登陆_游戏、登陆_帐号）
   - 参数要灵活可调，避免硬编码
   - 单个函数实现完整功能，不过度拆分

5. 资源路径规范：
   - 使用完整的绝对路径：/storage/emulated/0/magic/assets/
   - 图片分类存放在对应文件夹（如：通用模板/）
   - 文件名简洁明了，直接表达用途

6. 代码结构偏好：
   - 追求简洁，用最少代码解决最多问题
   - 避免过度模块化和复杂设计模式
   - 注释简洁，不要冗余的参数说明
   - 中文变量名配合英文API调用
   - 变量名要简洁直接，避免描述性前缀 --tags magic项目 编码习惯 AutoXjs 开发规范 变量命名
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/22 20:25 START
用户AutoXjs开发新习惯：

1. 区域范围参数处理：
   - 区域范围不用定义常量与变量，直接在函数调用中使用数字
   - 例如：images.findImageInRegion(屏幕截图, 图片, 66, 456, 413, 70) 
   - 不要创建 var 区域 = {x: 66, y: 456, width: 413, height: 70}

2. 找图功能通用化：
   - 将找图功能提取到独立的截图.js文件中
   - 创建通用的找图函数供多个模块使用
   - 保持原有逻辑功能不变，只是重构代码结构

3. 功能扩展模式：
   - 在现有函数中增加新功能时，保持原有逻辑完整
   - 新功能作为后续步骤添加，不影响原有流程
   - 使用随机等待时间增加人性化操作

4. 图片资源命名：
   - 继续使用简洁命名：报名.png、跳过.png
   - 变量名直接使用功能名：var 报名、var 跳过 --tags magic项目 AutoXjs 开发习惯 找图功能 代码重构
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/22 20:31 START
用户AutoXjs开发阶段习惯：

1. 测试阶段默认设置：
   - 导出模块部分默认注释掉：// module.exports = { ... };
   - 直接运行测试部分默认启用：console.log("🧪 开始测试..."); 
   - 这样便于直接运行文件进行测试，无需手动切换注释

2. 模块导入路径问题：
   - 在AutoXjs中，相对路径require可能有问题
   - 需要使用绝对路径或其他方式解决模块导入
   - 测试阶段优先保证功能可运行，模块化可后续调整

3. 开发优先级：
   - 功能实现 > 代码结构
   - 先确保功能正常工作，再考虑模块化重构
   - 测试阶段以独立运行为主，模块导入为辅 --tags magic项目 AutoXjs 开发阶段 测试习惯 模块导入
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/23 00:27 START
用户开发习惯：1. 测试阶段默认注释模块导出，启用直接运行测试代码；2. 不用定义常量与变量区域范围，直接使用数字坐标；3. 默认先添加随机等待时间0.5-2秒；4. 图片查找失败改为8秒内200毫秒查找；5. 在图片范围内随机位置点击而非固定点击 --tags 开发习惯 测试模式 坐标范围 随机等待 图片查找
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/23 00:29 START
用户开发习惯：1. 测试阶段默认注释模块导出，启用直接运行测试代码；2. 不用定义常量与变量区域范围，直接使用数字坐标；3. 默认先添加随机等待时间0.5-2秒；4. 图片查找失败改为8秒内200毫秒查找；5. 在图片范围内随机位置点击而非固定点击；6. 如果首次检测失败则跳过整个函数（例如未找到跳过按钮意味着已注册） --tags 开发习惯 测试模式 坐标范围 随机等待 图片查找 跳过逻辑
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/23 20:48 START
Magic游戏辅助脚本项目完整信息：

【项目概述】
- 项目名称：Magic游戏辅助脚本
- 技术栈：AutoXjs ozobiozobi魔改版v6.5.8.17
- 目标游戏：BigTime (com.winrgames.bigtime)
- 开发环境：雷电模拟器 540x960分辨率 DPI240
- 坐标范围：27,260,506,876 (478*616)

【项目架构】
- 主脚本：脚本/主脚本.js (统一程序入口)
- 核心模块：登陆.js、帐号.js、首次教程.js、检查.js、截图.js
- UI系统：完整的三页面架构(主页/脚本配置页/日志页)
- 配置管理：统一存储到/storage/emulated/0/magic/data/用户设置.json
- 图片匹配：使用AutoXjs的images.findImage()进行精确匹配

【核心功能流程】
- 第1-2步：必执行(游戏启动、随机等待)
- 第3、5、6、7步：前端勾选框控制(帐号登陆、首次教程、检查分数、首次提现教程)
- 第8-12步：游戏循环(检查登陆、每日领奖、游玩裂球、观看广告、检查涨分)

【UI设计特色】
- 紧凑版2x2勾选框网格布局，节省50%垂直空间
- 统一绿色主题#00A843
- 24个UI参数统一保存到用户设置.json文件
- 保存配置和重置配置按钮真正工作

【最新功能】
- MT管理器启动功能：主页橙色按钮，按主页键→延时5秒→全屏查找mt管理器.png→点击启动
- 智能重试机制：所有关键操作都有2-8次重试
- 随机化操作：所有延时和坐标都有随机化，模拟真实操作 --tags Magic游戏辅助 AutoXjs项目 UI架构 核心功能
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/23 20:48 START
用户开发习惯和偏好：

【开发偏好】
- 偏好多路径解决方案，希望提供多种可选的实现方式而不是单一方案
- 偏好不创建额外的工具文件，倾向于简单直接的解决方案，避免不必要的抽象层
- 偏好在主脚本中使用模块导入方式，并创建独立的测试脚本来单独测试各个功能模块
- 偏好先创建设计文档/计划，然后再实施文件更改，而不是直接修改文件

【测试习惯】
- 测试阶段注释掉模块导出并直接执行测试
- 默认操作前随机延迟(0.5-2s)
- 在图像边界内随机点击而不是固定坐标
- 如果首次检测失败则跳过整个函数
- 将初始检测步骤的超时时间从5秒增加到8秒，间隔200ms

【UI设计偏好】
- 偏好紧凑的复选框布局，每行两个复选框以节省空间
- 希望从UI设计中的复选框标签中删除步骤编号
- 偏好不定义坐标范围的常量/变量，使用直接数字

【配置管理要求】
- 要求所有UI参数都保存在一个统一的文件中
- 使用坐标范围27,260,506,876，尺寸478*616
- 故意注释掉模块导出在测试阶段 --tags 用户习惯 开发偏好 测试方式 UI设计
--tags #流程管理 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/23 20:50 START
用户开发习惯和偏好（更新版）：

【开发偏好】
- 偏好单路径解决方案，提供直接有效的实现方式
- 偏好不创建额外的工具文件，倾向于简单直接的解决方案，避免不必要的抽象层
- 偏好在主脚本中使用模块导入方式，并创建独立的测试脚本来单独测试各个功能模块
- 偏好先创建设计文档/计划，然后再实施文件更改，而不是直接修改文件

【测试习惯】
- 测试阶段注释掉模块导出并直接执行测试
- 默认操作前随机延迟(0.5-2s)
- 在图像边界内随机点击而不是固定坐标
- 如果首次检测失败则跳过整个函数
- 将初始检测步骤的超时时间从5秒增加到8秒，间隔200ms

【UI设计偏好】
- 偏好紧凑的复选框布局，每行两个复选框以节省空间
- 希望从UI设计中的复选框标签中删除步骤编号
- 偏好不定义坐标范围的常量/变量，使用直接数字

【配置管理要求】
- 要求所有UI参数都保存在一个统一的文件中
- 使用坐标范围27,260,506,876，尺寸478*616
- 故意注释掉模块导出在测试阶段 --tags 用户习惯 开发偏好 测试方式 UI设计 更新版
--tags #流程管理 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/23 21:44 START
用户的AutoXjs变量命名习惯规范：

1. 图片变量命名规范：
   - 不使用"xxx图片"这样的后缀命名
   - 直接使用功能名或图片内容作为变量名
   - 例如：var 选择分裂球游戏 = "选择分裂球游戏.png"
   - 例如：var 开始游戏 = "开始分裂球游戏.png"
   - 例如：var 跳过 = "跳过.png"

2. 变量命名原则：
   - 以图片功能命名：直接用图片代表的功能作为变量名
   - 以操作命令命名：用操作动作作为变量名
   - 简洁明了：避免冗余的描述性词汇
   - 语义清晰：变量名直接表达用途

3. 实际应用：
   - ❌ 错误：var 分裂球游戏图片、var 开始游戏图片
   - ✅ 正确：var 选择分裂球游戏、var 开始游戏
   - ❌ 错误：var 跳过按钮图片、var 登录界面图片  
   - ✅ 正确：var 跳过、var 登录界面

这种命名方式更符合简洁代码原则，让变量名直接表达功能意图。 --tags 变量命名 图片变量 AutoXjs 编码规范 用户习惯
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/23 21:59 START
用户在AutoXjs+Magic项目中的开发和测试习惯：

1. 截图.js参数使用规范：
   - 优先使用"判断"参数而不是"点击"参数
   - "判断"参数的优势：既有查找又有点击功能，找不到图片时会跳过步骤继续执行
   - "点击"参数的问题：找不到图片时会返回失败，中断后续执行

2. 执行逻辑设计原则：
   - 采用顺序执行模式，不依赖前一步的结果
   - 即使第一步失败或未找到，也要继续执行第二步
   - 避免串行执行（前一步失败就停止）的设计模式
   - 符合"如果找不到则执行下一步"的业务需求

3. 测试阶段开发习惯：
   - 单独测试时：模块导出代码注释掉（// module.exports = {...}）
   - 单独测试时：测试代码保持启用状态（console.log测试代码不注释）
   - 这样便于直接运行文件进行功能测试
   - 测试完成后再恢复模块导出，注释测试代码

4. 代码设计哲学：
   - 容错性优先：程序应该尽可能继续执行，而不是因为单个步骤失败就停止
   - 用户体验优先：即使某个界面元素找不到，也要尝试执行后续功能
   - 实用性优先：代码逻辑要符合实际使用场景的需求 --tags AutoXjs Magic项目 开发习惯 测试模式 执行逻辑 参数使用 容错设计
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/24 15:20 START
AutoXjs截图权限的正确使用方式：

1. 截图权限必须在全局申请，不能在函数内部申请
   - ✅ 正确：在脚本开头全局调用 requestScreenCapture()
   - ❌ 错误：在函数内部调用 requestScreenCapture()

2. 原因分析：
   - 截图权限是系统级权限，需要用户手动授权
   - 权限申请会弹出系统对话框，中断脚本执行流程
   - 在函数内申请会导致每次调用都要重新授权
   - 全局申请一次后，整个脚本运行期间都有效

3. 最佳实践：
   ```javascript
   // ✅ 正确方式：脚本开头全局申请
   if (!requestScreenCapture()) {
       console.log("❌ 截图权限获取失败");
       exit();
   }
   
   function 截图功能() {
       // 直接使用captureScreen()，无需再申请权限
       var img = captureScreen();
   }
   ```

4. 权限管理原则：
   - 一次申请，全局使用
   - 脚本开始时统一申请所有需要的权限
   - 权限申请失败时应该终止脚本执行
   - 避免在循环或函数内重复申请权限 --tags AutoXjs 截图权限 requestScreenCapture 全局权限 最佳实践
--tags #最佳实践 #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/24 16:01 START
AutoXjs OCR性能优化经验：

1. CPU线程数配置原则：
   - 线程数应该匹配设备CPU核心数
   - 2核CPU使用2线程：paddle.ocr(img, 2, true)
   - 4核CPU使用4线程：paddle.ocr(img, 4, true)
   - 超出CPU核心数会导致性能问题甚至崩溃

2. 模拟器环境特殊考虑：
   - 雷电模拟器通常只有2核CPU
   - 需要使用快速模型(true)而不是精准模型(false)
   - 避免复杂的多线程操作
   - 适当增加等待时间让权限生效

3. OCR模型选择：
   - true = ocr_v2_for_cpu(slim) 快速模型，适合低配置设备
   - false = ocr_v2_for_cpu 精准模型，需要更多资源

4. 权限申请最佳实践：
   - 在模拟器环境下使用简单的requestScreenCapture()
   - 避免复杂的自动点击线程操作
   - 权限申请后适当等待(sleep 1000ms)

5. 性能问题排查：
   - 首先检查CPU核心数和线程数匹配
   - 检查内存使用情况
   - 优先使用快速模型进行测试 --tags AutoXjs OCR 性能优化 CPU线程 模拟器 PaddleOCR
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/24 22:22 START
AutoXjs ozobiozobi v6.5.8.17中TessBaseAPI类不存在的问题和解决方案：

问题：脚本中使用importClass(com.googlecode.tesseract.android.TessBaseAPI)导致"TessBaseAPI is not defined"错误

原因：AutoXjs ozobiozobi使用内置OCR模块，不支持直接导入Java的TessBaseAPI类

正确的OCR使用方法：
1. Paddle OCR: paddle.ocr(img) - 返回包含confidence的详细结果
2. Google ML Kit OCR: gmlkit.ocr(img, "zh") - 支持多语言识别
3. 不要使用Java原生的TessBaseAPI类

修复方案：将脚本改为使用AutoXjs内置OCR模块，而不是创建新文件。遇到类似问题时应该修改现有文件，只有在修改难度过大时才考虑创建新文件。 --tags AutoXjs OCR TessBaseAPI 错误修复 内置模块
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 02:41 START
用户在OCR项目中严格禁止保存任何文件或图片功能。如果AI在OCR功能中尝试保存文件，必须向用户提出申请获得明确许可。用户强调这是OCR开发中的重要规范，违反此规定是不被允许的。 --tags OCR开发规范 文件保存禁止 用户习惯 开发约束
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 03:04 START
像素分布分析算法成功解决了OCR数字3和5识别混淆问题。关键技术：分析图像四个区域(左侧、右侧、上部、下部)的像素密度，通过左右差异和上下差异特征指标来区分数字3和5。数字5的关键特征是上部密度高，数字3的特征是右侧开口(右侧密度低)。算法成功将错误识别的"23"纠正为正确的"25"。 --tags OCR识别 数字3和5混淆 像素分布分析 图像处理 智能纠错 成功案例
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 03:22 START
每次完成任务后必须进行全面检查验证。包括：1)语法错误检查(使用diagnostics工具)，2)功能完整性检查(确认所有函数存在且正确)，3)逻辑一致性检查(变量定义、函数调用)，4)性能和错误处理检查。这是确保交付质量的重要习惯，避免遗留问题。 --tags 开发习惯 质量保证 任务完成检查 最佳实践
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/25 05:08 START
AutoXjs OCR开发经验：禁止在测试脚本中使用paddle.ocr()，该API在某些设备上会导致SIGABRT崩溃(libpaddle_light_api_shared.so错误)。应该统一使用Tesseract OCR或与主脚本相同的OCR引擎，避免OCR库冲突导致应用崩溃。测试时直接调用主识别模块而不是重新初始化OCR引擎。 --tags AutoXjs OCR 崩溃 paddle tesseract 兼容性
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 05:11 START
Magic游戏辅助脚本OCR数字识别开发进度：已完成绿色区域识别的关键结构点检测算法实施。问题：OCR识别22被误识别为24（大部分阈值识别为单个2或22，少数识别为24但置信度更高70%）。解决方案：实施了数字2的S型对角线检测（检测右上到左下对角线+水平连接，权重7:3，置信度>0.6才纠错）和数字4的三角形+垂直线检测（检测右侧垂直线+左上三角形开口，权重6:4）。已删除保存图片功能，修复了测试脚本中paddle.ocr()崩溃问题（改用exit()替代return）。下一步：测试绿色区域算法效果，成功后应用到其他三个区域（蓝色、黄色、红色）。 --tags Magic OCR 数字识别 结构检测 绿色区域 开发进度
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 09:20 START
用户开发习惯和偏好：不要定义多余的变量常量，直接在代码中使用数值。例如坐标、尺寸、阈值等参数直接写数字，不要创建var 蓝区_X = 289这样的常量定义。用户偏好简洁直接的代码风格，避免不必要的抽象层。已完成蓝色区域OCR识别脚本开发，使用Tesseract OCR识别数字0-9，包含1-100遍历黑色提取、二值化(127)、中值去噪功能，函数名简洁如识别_蓝区()。 --tags 用户习惯 代码风格 常量定义 OCR开发 蓝色区域
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 09:23 START
用户要求使用Android相对路径，不要使用绝对路径。例如tessdata路径应该使用"./脚本/ocr/tessdata/"而不是"/storage/emulated/0/脚本/magic/脚本/ocr/tessdata/"。这样可以提高代码的可移植性和兼容性，避免硬编码具体的存储路径。 --tags 路径设置 相对路径 Android 可移植性 tessdata
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 10:18 START
OCR数字识别最准确算法配置：1)引擎模式使用LSTM神经网络(tessedit_ocr_engine_mode=1)准确率最高，2)页面分割模式使用PSM 6单一文本块最适合数字识别，3)字符白名单只识别0-9数字，4)清空字符黑名单。LSTM比传统Legacy引擎准确率显著提高，是目前数字识别的最佳选择。 --tags OCR 数字识别 Tesseract LSTM 最佳配置 准确率
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/25 12:42 START
AutoXjs多区域OCR识别架构最佳实践：1)每个区域必须使用独立的OCR实例(本地OCR变量)避免共享冲突；2)每个区域独立屏幕捕获，避免图像资源被意外回收；3)完善资源清理机制，正常和异常情况都要清理OCR实例和图像资源；4)蓝.js模块和su_main.js主程序都要有各自独立的屏幕捕获权限；5)避免使用全局OCR变量，改用函数内局部变量；6)OCR清理使用recycle()方法而不是end()方法 --tags AutoXjs OCR架构 资源管理 最佳实践
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/25 14:44 START
用户明确要求：禁止使用英文，除非用户明确说"确认"才可以使用英文。需要先向用户申请使用英文的权限。 --tags 开发规则 语言要求 用户偏好
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/26 17:57 START
AutoXjs脚本开发重要经验和规范：

1. **问题诊断流程**：
   - 收到脚本问题时，必须先深度检查所有相关文件
   - 使用view工具全面检查函数功能、参数、逻辑
   - 分析问题根本原因，而不是仅根据错误信息修复
   - 检查文件间的依赖关系和调用逻辑

2. **代码完成后的验证流程**：
   - 完成代码编写后必须再次深度检查
   - 验证所有函数功能是否正确实现
   - 检查代码完整性和逻辑一致性
   - 确认模块导出/导入是否正确
   - 验证参数传递和返回值格式

3. **AutoXjs路径规范**：
   - 使用安卓文件系统路径：files.path("../../assets/算数游戏/")
   - 不是项目相对路径，是安卓系统的文件路径
   - 模板文件路径格式：files.path("../../assets/算数游戏/模板名.png")
   - 答案数字路径格式：files.path("../../assets/算数游戏/答案数字/数字.png")

4. **质量保证原则**：
   - 技术准确性第一：所有代码基于官方文档
   - 系统性分析：从问题根源到解决方案的完整分析
   - 双重验证：编写完成后的功能验证和错误检查
   - 深度检查：不仅看表面问题，要分析底层逻辑 --tags AutoXjs 开发规范 问题诊断 质量保证 路径规范
--tags #流程管理 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/26 19:56 START
代码重构和模块导出的重要经验教训：

1. **函数重命名的完整检查流程**：
   - 重命名函数时必须同步检查所有引用位置
   - 特别检查module.exports导出列表
   - 使用搜索功能确认所有旧函数名都已更新
   - 验证导出的每个函数都确实存在

2. **模块导出验证的必要步骤**：
   - 完成代码修改后必须验证导出列表
   - 使用regex搜索确认所有导出函数都有对应定义
   - 检查是否有遗留的旧函数名在导出列表中
   - 运行diagnostics工具检查语法错误

3. **重构过程中的质量控制**：
   - 每次函数重命名都要立即更新所有相关引用
   - 不能只关注函数实现，必须检查接口定义
   - 重构完成后必须进行完整性验证
   - 避免"改了实现忘了接口"的低级错误

4. **错误预防的系统性方法**：
   - 使用工具辅助检查（view、search、diagnostics）
   - 建立检查清单确保不遗漏关键步骤
   - 重构时采用"改一处验证一处"的原则
   - 完成后进行端到端的功能验证

这次错误的根本原因：重构时专注于函数实现，忽略了模块导出接口的同步更新，导致运行时找不到函数定义。 --tags 代码重构 模块导出 函数重命名 质量控制 错误预防
--tags #流程管理 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/26 19:57 START
AutoXjs图片回收错误的深度分析和解决方案：

1. **图片回收错误的根本原因**：
   - 在forEach或for循环中，屏幕图被多次引用但过早回收
   - images.clip()创建的区域图与原图有引用关系，原图回收导致区域图失效
   - matchTemplate操作过程中图片被意外回收
   - 异步操作导致图片在使用过程中被回收

2. **错误的修复尝试**：
   - 仅添加try-catch无法解决根本问题
   - 简单的finally块清理可能在错误时机执行
   - 重构函数逻辑但保留相同的图片操作模式无效

3. **正确的解决方案**：
   - 必须完全避免在循环中重复使用已回收的图片
   - 每次图片操作都要独立完成，不依赖外部图片对象
   - 使用最简单的线性处理，避免复杂的嵌套操作
   - 立即使用立即回收的原则

4. **AutoXjs图片处理的安全模式**：
   - 每个区域单独截图，不依赖全局屏幕图
   - 每个模板单独加载和回收，不批量处理
   - 避免图片对象的长期持有和传递
   - 使用最基础的API，避免复杂的图片处理链 --tags AutoXjs 图片回收 错误分析 解决方案
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/26 20:02 START
AutoXjs图片回收问题的深度分析和根本解决方案：

1. **图片回收错误的真正原因**：
   - images.clip()创建的区域图与原始屏幕图有依赖关系
   - 当原始屏幕图被回收后，所有基于它的区域图都变成无效状态
   - 在循环中继续使用已失效的区域图会抛出"image has been recycled"错误
   - 这不是简单的回收时机问题，而是图片对象依赖关系问题

2. **错误的修复尝试**：
   - 仅添加try-catch无法解决根本问题
   - 调整回收顺序仍然无法解决依赖关系
   - 使用finally块清理可能在错误时机执行
   - 重构函数但保留相同的图片依赖模式无效

3. **正确的解决方案**：
   - 每个区域都独立调用captureScreen()，避免图片依赖
   - 立即使用立即回收原则：区域图处理完立即回收
   - 先回收区域图，再回收对应的屏幕图（顺序很重要）
   - 避免全局屏幕图，每个处理单元都有独立的图片生命周期

4. **AutoXjs图片处理的安全模式**：
   - 一个屏幕图对应一个区域图，一对一关系
   - 处理完成后立即清理，不跨循环持有图片对象
   - 使用for循环替代forEach，更好控制资源生命周期
   - 在finally块中确保资源清理，但要注意清理顺序

5. **问题诊断的重要经验**：
   - 图片回收错误往往是依赖关系问题，不是简单的回收时机问题
   - 必须深度检查images.clip()等函数的依赖关系
   - 不能只看表面的回收代码，要分析整个图片对象的生命周期
   - 修复时要从根本上消除依赖关系，而不是调整回收顺序 --tags AutoXjs 图片回收 依赖关系 根本解决方案 问题诊断
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/26 22:17 START
用户明确禁止我在修改代码时删除或简化原有功能，特别是已经完成的复杂组合策略功能。当遇到需要修改现有复杂功能时，我必须先向用户提问是否需要修改，等待用户明确的确认指令（如"确认修改"、"同意修改"等）后才能执行修改操作。禁止立即修改，必须先征求同意。 --tags 代码修改规则 用户权限 复杂功能保护
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/26 22:20 START
当我需要修改用户的代码时，特别是涉及删除原有功能、简化复杂逻辑、重做现有功能等操作时，我不是被完全禁止，而是必须先向用户提出申请。我需要说明修改的原因、影响范围、替代方案等，然后等待用户的明确确认指令后才能执行。这是一个申请-确认的流程，而不是完全禁止的行为。 --tags 代码修改流程 申请确认机制 用户权限管理
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/27 09:54 START
Magic游戏辅助脚本项目当前开发进度：1. 已完成公式识别功能，支持数学表达式识别和计算；2. 已完成答案区域识别功能，支持四个区域（黄区、蓝区、绿区、红区）的数字识别；3. 已实现二数字和三数字组合识别算法，支持复杂的间距判断和相似度比较；4. 已调整图片模板尺寸以提高识别精度，并相应调整了间距阈值；5. 已整合所有功能到公式识别完整版.js中，提供完整的计算流程；6. 当前问题：需要继续优化识别精度，确保能正确识别121等三位数组合。 --tags 项目进度 开发状态 Magic游戏 识别算法
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/27 09:57 START
用户交互规则：1) 当需求或指令不明确时，必须使用interactive_feedback工具询问澄清问题，不得基于假设进行操作；2) 尽可能通过interactive_feedback工具提供预定义选项，便于用户快速决策；3) 即将完成用户请求时，必须调用interactive_feedback工具请求用户反馈；4) 如果用户反馈为空，可以结束请求，避免循环调用工具。这是确保准确理解用户意图和提供优质服务的核心规则。 --tags 用户交互 interactive_feedback 工具使用 核心规则
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 10:07 START
用户重要习惯：当用户询问检查文件或需要反馈时，我必须先使用interactive_feedback反馈器工具询问用户意见和确认，不能直接执行操作。用户明确指出我违反了这个规则，应该先打开反馈器跟用户沟通，而不是自己直接执行检查文件的操作。这是用户交互的核心要求。 --tags 用户习惯 反馈器使用 交互规则 重要提醒
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 10:24 START
用户编程习惯：用户要求我在编程时优先简约化，避免添加不必要的变量。如果我需要添加变量、常量或名称时，必须先使用interactive_feedback反馈器向用户提示报告，说明哪些需要添加变量名、常量名、名称是什么，等待用户确认后才能添加。用户明确指出我设置变量时通常会复杂化，要求我以后先简约化处理。这是重要的编程风格要求。 --tags 编程习惯 简约化 变量管理 反馈器使用 用户偏好
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 12:03 START
用户发现公式识别完整版.js中存在硬编码相似度的问题，要求检查所有数字与运算符的硬编码相似度。用户指出这是不可以的，需要查看AutoXjs ozobiozobi官方文档了解正确的相似度API使用方式。官方文档地址：https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html --tags 硬编码相似度 API规范 官方文档 代码规范
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 12:57 START
Augment IDE的MCP服务器配置方法：通过界面"New MCP Server"添加，需要填写Name（服务器名称）、Command（执行命令，如bun、node、npx等）、Arguments（参数列表）、Working Directory（工作目录）、Environment Variables（环境变量）。与Claude Desktop的JSON配置不同，Augment使用图形界面配置MCP服务器，参数和命令分开填写。 --tags Augment MCP配置 图形界面 部署方法
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 14:43 START
Magic游戏辅助脚本算数模块开发进度（2025年6月27日）：

【计算.js文件状态】
- 文件路径：脚本\算数\计算.js
- 版本：1.0（2025-01-26）
- 功能定位：算数游戏统一计算模块调用入口

【核心架构设计】
- 采用方案B（模块化整合）：保持原文件独立性，通过require调用整合模块
- 全局屏幕捕获权限管理：统一在计算.js中申请权限，避免重复申请
- 简化设计：计算.js仅作为调用入口，核心功能整合在公式识别完整版.js中

【主要功能实现】
- 计算_数字()函数：调用整合模块的完整计算_数字()函数
- 循环执行机制：支持多次循环执行（当前设置2次，注释显示可扩展到10次）
- 错误处理：完善的try-catch机制，记录执行时间和错误信息
- 间隔延时：循环间随机延时1-3秒，模拟真实操作

【技术特点】
- 模块依赖：require("./公式识别完整版.js")
- 权限管理：requestScreenCapture()统一申请
- 结果格式：返回{成功, 错误, 执行时间}标准格式
- 调试信息：大部分console.log已注释，保持输出简洁

【当前状态】
- 基础架构完成，功能调用正常
- 测试循环设置为2次（可调整为10次）
- 依赖公式识别完整版.js模块提供核心计算功能
- 代码结构清晰，注释完善，便于维护

【下一步计划】
- 根据公式识别完整版.js的优化情况同步更新
- 可能需要调整循环次数和延时参数
- 持续优化错误处理和日志输出 --tags Magic项目 算数模块 计算.js 开发进度 模块化架构
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 20:27 START
AutoXjs算数游戏识别系统开发进度总结：

1. **核心问题解决**：
   - 修复了1+1表达式被错误重构为11+11的问题
   - 创建了简化版脚本(公式2.js)，输出简洁但功能完整
   - 修复了循环逻辑，确保无论成功失败都执行10次

2. **模板系统完善**：
   - 添加了小数字模板：2_1.png, 3_1.png, 4_1.png, 6_1.png
   - 完整的小数字识别包括：_1后缀(2_1,3_1,4_1,6_1)、_2后缀(0_2,1_2)、小运算符(×_2,=2)
   - 实现了多缩放因子算法解决分辨率适配问题

3. **距离分析增强**：
   - 在表达式识别中增加了小数字与运算符距离的详细输出
   - 区分大小数字类型：小数字包括_1、_2后缀，小运算符包括_2后缀和2结尾
   - 发现33+10被误识别为单+双模式的根本原因：小数字间距判断逻辑需要优化

4. **当前待解决问题**：
   - 33表达式中只识别到1个3_1，需要检查为什么第二个3没有被识别
   - 需要为小数字设置专门的组合间距阈值
   - 1+0组合失败(46px间距超过35px阈值)，需要调整数字1的间距阈值

5. **技术架构**：
   - 使用AutoXjs ozobiozobi v6.5.8.17版本
   - 模块化设计：公式识别完整版.js + 计算.js + 公式2.js(简化版)
   - 多缩放因子算法覆盖0.7-1.3倍缩放范围 --tags AutoXjs 算数游戏 OCR识别 模板匹配 距离算法 开发进度
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 20:53 START
成功实施小数字间距分析方案一：修改验证统一距离标准函数，支持所有复数情况（左单右单、左双右双、左双右单、左单右双）。将固定的80-110px标准改为动态判断：包含小数字时使用50-80px专用标准，大数字保持80-110px。修改位置：公式识别完整版.js第218-258行。预期解决33表达式、1+0组合失败、33+10误识别等问题。 --tags 小数字间距 方案一实施 验证统一距离标准 复数支持 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 20:58 START
成功实施小数字间距分析方案二：优化小数字组合阈值。修改普通数字组合函数第1022-1029行，将小数字阈值从25px调整到40px，增加_2后缀支持（0_2、1_2、×_2、=2）。现在支持所有小数字后缀的组合判断，预期解决33表达式只识别1个3_1的问题，第二个3_1现在能被正确识别和组合。 --tags 小数字间距 方案二实施 组合阈值优化 _2后缀支持 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:03 START
成功实施小数字重构判断修复：修改数字相同性检查逻辑，支持大小数字混合判断。在三个关键位置（第115-123行、第510-524行、第738-747行）增加基础值提取逻辑，使用replace(/_[12]$/, '')去除_1、_2后缀。修复智能答案匹配判断函数（第316-346行），使用基础值进行计算。现在33_1表达式能正确进入重构判断流程，与大数字33表达式享受相同的重构逻辑。 --tags 小数字重构 数字相同性检查 基础值提取 智能答案匹配 AutoXjs
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/27 21:05 START
成功修复小数字总长度判断条件：为小数字设置专用的长度阈值。修改三层严格判断中的总长度条件（第177-207行），小数字单×单模式使用160px阈值（比大数字200px小40px），双×双模式使用210px阈值（比大数字250px小40px）。同时修复250px兜底规则（第82-90行），小数字使用210px兜底阈值。现在小数字拥有与大数字完全对等的三层严格判断：距离验证+对称性检查+完全相同性检查，以及专用的总长度分层判断。 --tags 小数字总长度 三层严格判断 长度阈值 兜底规则 AutoXjs
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/27 21:17 START
成功修复普通数字组合逻辑中的关键问题：大小数字混合组合。修改普通数字组合函数（第1056-1102行），将严格的值相等判断改为基础值相等判断，使用replace(/_[12]$/, '')去除后缀。现在3(大数字)和3_1(小数字)能正确识别为相同基础值并组合成33。同时修复了数字1的判断逻辑，使用基础值进行间距阈值判断。这是解决小数字无法组合问题的根本修复。 --tags 普通数字组合 大小数字混合 基础值判断 数字组合修复 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:20 START
正确修复小数字组合问题：在普通数字组合函数中添加小数字专用处理逻辑，而不是修改原有大数字逻辑。新增处理小数字组合函数（第1113-1197行），专门处理大小数字混合组合。使用基础值相等判断（replace(/_[12]$/, '')），支持3和3_1组合成33。保持原有大数字逻辑不变，确保向后兼容。小数字专用阈值：数字1为50px，其他为40px。这是正确的架构设计方式。 --tags 小数字专用处理 大小数字混合 架构设计 向后兼容 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:23 START
成功为间距合理函数添加小数字支持：修改间距合理函数（第1204-1279行），为数字与数字间距规则和数字与符号间距规则都添加了小数字专用判断。小数字间距规则比大数字小约15px：1和1为1-24px，相同数字为1-9px，含1为1-65px，其他为1-85px。小数字-符号间距为20-300px（比大数字30-400px小10-100px）。使用基础值判断（replace(/_[12]$/, '')）支持大小数字混合。这是关键的底层函数，被普通数字组合逻辑调用。 --tags 间距合理函数 小数字支持 底层函数修复 大小数字混合 AutoXjs
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/27 21:32 START
为小数字模板增加详细位置输出：修改公式识别完整版.js第1532-1553行，为所有小数字模板（包含_1、_2后缀和2结尾的符号）增加详细的位置信息输出。现在当识别到3_1、0_2、1_2、×_2、=2等小数字时，会输出具体的位置坐标(x,y)和相似度信息，便于调试第二个3_1是否被识别到。这将帮助确定问题是在模板匹配阶段还是后续的组合阶段。 --tags 小数字位置输出 模板匹配调试 位置信息 相似度输出 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:39 START
为小数字添加全面调试输出：1)启用查找多个图片函数的详细输出(第1400-1431行)，包括原始匹配位置、相似度、负坐标检测、重叠检测、间距分析；2)为小数字模板添加详细的匹配前信息(第1522-1534行)，包括模板详情、文件路径、目标图尺寸；3)为小数字添加匹配后分析(第1572-1599行)，包括未找到原因分析、多个匹配的分布情况、间距计算。这将帮助诊断第二个3_1是否因为重叠、负坐标或间距问题而被过滤。 --tags 小数字全面调试 重叠检测 负坐标检测 间距分析 模板匹配调试 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:42 START
深度分析小数字识别问题：确认matchTemplate只找到1个3_1匹配(位置10,28,相似度0.987)，第二个3_1确实在模板匹配阶段就没有被识别到。为解决此问题，降低小数字相似度阈值：数字从0.8降到0.75，运算符从0.78降到0.73。截图区域57,431,424,118应该能覆盖位置66的第二个3_1。问题可能是第二个3_1相似度在0.75-0.8之间，或者被部分遮挡/模糊，或者屏幕内容与预期不符。 --tags 小数字相似度阈值 模板匹配问题 深度分析 截图区域 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:47 START
全面修复小数字表达式重构功能：1)修复单数字×复数字模式检测(第566-589行)，使用基础值长度而非原值长度进行判断；2)修复生成复数数字函数(第1041-1051行)，提取基础值后再生成复数；3)修复是真正的重复组合函数(第899-956行)，使用基础值进行所有字符串比较和重复验证。这些修复确保3_1×3_1能正确识别为单数字×单数字模式，生成33×33而不是3_13_1×3_13_1。所有小数字表达式重构逻辑现在完全支持大小数字混合。 --tags 小数字表达式重构 基础值处理 单数字复数字模式 生成复数数字 重复组合验证 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:50 START
全面启用调试输出并优化小数字识别：1)启用关键调试输出包括区域尺寸检查、灰度处理、多阈值匹配、间距规则判断等；2)进一步降低小数字相似度阈值：数字从0.75降到0.7，运算符从0.73降到0.68；3)为小数字增加搜索次数从5次提高到10次，提高找到第二个匹配的概率；4)增强小数字专用日志输出，便于精确诊断问题。这些优化将最大化小数字的识别成功率，特别是相似度较低的第二个3_1。 --tags 调试输出启用 小数字相似度优化 搜索次数增加 识别成功率提升 AutoXjs
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/27 21:54 START
修复小数字间距阈值问题：发现两个1_2间距68px超过了所有组合阈值(普通50px，小数字40px)，导致无法组合成11。将小数字专用阈值从40px增加到80px，覆盖68px间距。同时修复普通数字组合(第1084-1093行)和处理小数字组合函数(第1160-1181行)中的阈值设置。现在小数字1的阈值也统一为80px。这将确保两个1_2能正确组合成11，表达式从3+101=变为3+11=。 --tags 小数字间距阈值 68px间距问题 组合阈值修复 1_2组合 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 21:58 START
紧急修复小数字阈值过低问题：发现0.7阈值导致大量误识别，严重影响答案区域精准度。将小数字阈值从0.7调整到0.78（略低于大数字0.8），小运算符从0.68调整到0.75（略低于大数字0.78）。这样既能识别到真正的小数字，又避免过度误识别低质量匹配。保持答案区域的高精准度，确保22×8=176等表达式能正确找到答案。平衡了小数字识别能力和整体系统精准度。 --tags 小数字阈值修复 精准度平衡 误识别问题 答案区域精准度 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 22:02 START
修复答案区域精准度问题：发现蓝区识别成175而不是176，原因是数字8相似度0.777被低阈值0.75接受，导致错误组合。将答案区域阈值从0.75全面提升到0.85，包括数字、matchTemplate、findImage三个参数。同时将小数字阈值也提升到0.85，确保只有高质量的小数字匹配被接受。这将过滤掉相似度0.777等低质量匹配，确保176等正确答案能被准确识别。 --tags 答案区域精准度 阈值0.85 蓝区176修复 低质量匹配过滤 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 22:08 START
修复答案区域间距判断过严问题：发现7+6间距-6px被-5~10px范围拒绝，导致176组合无法形成。将答案区域间距范围从-5~10px调整为-8~10px，包括两数字组合(第2114行)和三数字组合(第2162、2169行)的间距判断。同时更新日志输出中的期望范围显示(第2130、2184-2185行)。现在-6px间距将被接受，1+7+6=176组合应该能正常形成，解决蓝区识别175而非176的问题。 --tags 答案区域间距修复 -6px间距问题 176组合修复 间距范围调整 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 22:15 START
为小数字设置专门的去重条件：发现小数字尺寸小，使用大数字的去重条件(15px×20px)可能导致相近的小数字被误删。修改去重逻辑(第1715-1732行)，为包含小数字的情况设置更严格的去重条件5px×8px，而大数字保持原有15px×20px。这样两个相距10-15px的小数字3_1不会被误认为重复，确保33表达式中的两个3都能被保留。增加详细的小数字去重日志输出，便于调试验证。 --tags 小数字去重条件 去重阈值优化 5px×8px阈值 33表达式修复 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 22:21 START
发现小数字组合逻辑未执行的关键问题：通过分析小数字日志发现，虽然处理小数字组合函数存在且有详细输出，但日志中完全没有相关输出，说明该函数根本没有被调用。在普通数字组合函数(第1124-1129行)和处理小数字组合函数(第1132-1140行)中添加详细的调试输出，包括输入输出元素数量、元素详情等，以便精确定位为什么小数字组合逻辑没有被执行。这是解决33+10=表达式中第二个3缺失问题的关键调试步骤。 --tags 小数字组合逻辑未执行 调试输出增强 函数调用追踪 33表达式问题 AutoXjs
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/27 23:40 START
用户明确禁止创建测试文件，包括demo、示例、测试等非功能性文件。所有开发必须专注于解决实际问题，不得创建任何测试相关的文件。 --tags 开发规范 禁止测试文件 实际问题导向
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 23:44 START
用户要求在每次执行任务时都要在输出内容中写上修改的版本号，以提示确认使用新版本运行程序。这是重要的版本管理和确认机制。 --tags 版本管理 输出规范 确认机制
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 23:55 START
完成v2.2.0版本综合修复：1.数字组合阈值优化(数字1从50px提高到60px，其他数字从15px提高到25px) 2.运算符去重优化(从25×30px降低到5×5px) 3.解决33+10=识别问题，预期从错误的3--3-+101=变为正确的33+10= --tags 版本v2.2.0 数字组合修复 运算符去重 阈值优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:00 START
完成v2.3.0版本数字组合逻辑修复：1.删除数字组合中相同值限制，允许不同数字组合(如1+0=10) 2.统一多缩放因子去重阈值为5×5px 3.解决33+10=识别的根本性逻辑问题，从只能组合相同数字改为可以组合任意相邻数字 --tags 版本v2.3.0 数字组合逻辑修复 去重优化 根本性修复
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:04 START
完成v2.4.0版本运算符去重彻底修复：1.重写运算符去重逻辑，从替换改为直接删除重复项 2.简化冲突处理，直接比较相似度保留最优 3.修复索引管理，正确处理删除后的索引调整 4.解决位置17重复减号、位置140/144/150重复加号等问题 --tags 版本v2.4.0 运算符去重修复 删除重复项 相似度比较
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:09 START
完成v2.5.0版本相同运算符去重修复：1.发现v2.4.0只处理不同运算符冲突，忽略相同运算符重复的根本问题 2.修复相同运算符去重逻辑，区分相同符号和不同符号处理 3.现在能正确处理- vs -、+ vs +等相同运算符重复 4.完善运算符去重的逻辑完整性 --tags 版本v2.5.0 相同运算符去重 逻辑完整性修复 重复处理
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:14 START
完成v2.6.0版本强制相同运算符去重：1.将多缩放因子去重阈值从5×5px强化为1×1px 2.新增最终去重处理，双重保险确保无重复 3.从源头解决运算符重复问题，避免后续复杂处理 4.智能替换机制，始终保留相似度最高的运算符 --tags 版本v2.6.0 强制去重 源头治理 双重保险
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:21 START
完成v2.7.0版本综合修复最终版本：1.调整去重阈值从1×1px到10×10px覆盖实际位置差异 2.修复数字组合逻辑，使用数字组最后元素计算间距 3.强化运算符冲突处理，统一10×10px阈值 4.全面版本标识更新，完善调试信息 5.解决33+10=识别的根本性问题 --tags 版本v2.7.0 综合修复 去重阈值 数字组合逻辑 运算符冲突处理
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:26 START
完成v3.0.0版本运算符判断逻辑完全重构：1.实施位置分组策略，按15px容差对运算符分组 2.每组选择相似度最高的运算符 3.增加智能位置验证(等号在末尾，运算符在中间) 4.删除70多行复杂冲突处理逻辑，简化为分组→选择→验证→合并流程 5.保持原有数字组合和多缩放因子功能 --tags 版本v3.0.0 运算符逻辑重构 位置分组 智能验证 简化流程
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/28 00:37 START
完成v3.1.0版本删除小数字逻辑：1.删除所有小数字模板引用(0_2,1_2,2_1,3_1,4_1,6_1,×_2,=2) 2.简化识别循环，删除小数字判断逻辑 3.统一去重阈值为15×20px 4.删除小数字专用组合处理函数 5.简化代码结构，从23个模板减少到15个 6.解决小数字干扰导致的识别混乱问题 --tags 版本v3.1.0 删除小数字逻辑 模板简化 逻辑统一
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:45 START
完成v3.2.0版本多缩放因子匹配策略：1.实施多缩放因子识别函数，支持100%到40%的缩放范围 2.设置统一阈值：数字0.78，运算符0.85 3.智能跳过过小模板(<10×10px) 4.详细的缩放和匹配过程日志 5.解决不同尺寸数字识别问题，从单一尺寸升级为多尺寸匹配 --tags 版本v3.2.0 多缩放因子匹配 统一阈值 多尺寸识别
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:49 START
完成v3.2.1版本修复图片缩放API问题：1.发现images.resize()参数错误导致INTER_XX错误 2.改用images.scale(模板图, 缩放因子, 缩放因子)正确API 3.优化缩放尺寸计算，获取实际缩放后尺寸 4.保持多缩放因子策略(100%到40%) 5.修复关键API调用问题，确保多缩放因子匹配正常工作 --tags 版本v3.2.1 API修复 图片缩放 images.scale 多缩放因子
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:54 START
完成v3.2.2版本修复matchTemplate返回值处理：1.发现images.matchTemplate()返回单个对象而不是数组，导致forEach失败 2.实现智能返回值检测，自动转换单个对象为数组格式 3.兼容对象和数组两种返回格式 4.保持多缩放因子策略和统一阈值 5.解决forEach方法调用失败问题，确保多缩放因子匹配正常工作 --tags 版本v3.2.2 matchTemplate返回值 forEach错误修复 智能类型检测
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 00:59 START
完成v3.2.3版本调整缩放因子数值：1.将缩放因子从[1.0,0.8,0.6,0.5,0.4]调整为[1.0,0.98,0.96,0.94,0.92,0.90] 2.从100%到90%每2%递减，提供更精细的匹配 3.覆盖常见的轻微缩放范围，避免大跨度遗漏 4.平衡精度和性能，6个缩放级别 5.提高数字识别的准确性和成功率 --tags 版本v3.2.3 缩放因子调整 精细递减 识别精度优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:07 START
完成v3.3.0版本单模板多位置匹配策略：1.基于OpenCV标准做法实现阈值过滤+非最大值抑制(NMS) 2.单模板识别多个重复图片，max从5增加到50 3.实现IoU重叠度计算和30%重叠阈值去重 4.大幅减少计算量，避免复杂的图片缩放 5.解决单个数字在多个位置重复出现的识别问题 --tags 版本v3.3.0 单模板多位置 NMS去重 OpenCV标准 IoU重叠度
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:11 START
完成v3.3.1版本删除统一阈值：1.删除根据类型设置不同阈值的逻辑(数字0.78，运算符0.85) 2.使用统一默认阈值0.8，简化匹配逻辑 3.删除函数类型参数，简化函数调用 4.保持单模板多位置匹配和NMS去重的核心功能 5.简化代码复杂性，提高可维护性 --tags 版本v3.3.1 删除统一阈值 简化逻辑 默认阈值0.8
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:18 START
完成v3.4.0版本在原有代码基础上加入NMS方法：1.在数字去重逻辑中加入NMS判断条件，专门处理相同数字重叠情况 2.添加计算IoU重叠度函数，使用35×45标准数字尺寸 3.智能判断：重叠度>30%认为重复，<30%保留两个数字 4.最小侵入性修改，保持原有所有功能 5.解决33中两个3被误判为重复的问题 --tags 版本v3.4.0 NMS方法 IoU重叠度 最小侵入性 相同数字识别
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:23 START
完成v3.4.1版本调整为多个NMS阈值：1.使用6个NMS阈值(5%-30%范围)进行多数投票决策 2.智能统计每个阈值的判断结果，采用多数原则 3.详细的调试输出显示每个阈值的分析过程 4.避免单一阈值误判，提高相同数字识别稳定性 5.通过多数决策机制更准确识别33中的两个3 --tags 版本v3.4.1 多个NMS阈值 多数投票决策 智能判断 稳定性提升
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:30 START
完成v3.5.0版本删除NMS方法：1.完全删除IoU重叠度计算函数和相关代码 2.移除6个NMS阈值的多数投票机制 3.删除复杂的重叠度分析和决策逻辑 4.恢复原有简单的距离判断去重逻辑 5.简化代码复杂性，提高可维护性和稳定性 --tags 版本v3.5.0 删除NMS方法 简化代码 恢复原有逻辑 提高稳定性
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:39 START
完成v3.5.1版本删除小数字模板：1.删除所有小数字模板(_1,_2后缀和数字后缀) 2.简化符号类型判断，只区分数字和运算符 3.删除小数字专用组合处理函数和相关逻辑 4.统一去重条件，删除小数字特殊阈值 5.大幅简化代码，删除约200行复杂逻辑，提高稳定性和可维护性 --tags 版本v3.5.1 删除小数字模板 简化代码 统一标准 提高稳定性
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:50 START
完成v3.5.2版本添加额外数字3模板（方案B）：1.添加31.png作为3b模板到模板库 2.实现3b到数字3的映射机制 3.增强模板加载和识别过程的日志输出 4.保持现有去重逻辑处理重复数字3 5.提高数字3识别覆盖率，特别是33等重复数字表达式 --tags 版本v3.5.2 额外数字3模板 31.png 3b映射 提高识别覆盖率
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:54 START
完成v3.5.3版本优化数字3识别阈值：1.分析日志发现数字3和3b模板都未找到匹配，阈值0.75过高 2.为数字3和3b设置特殊低阈值0.55（降低27%） 3.增加搜索次数从5次到10次 4.只针对数字3优化，不影响其他模板精度 5.解决数字3识别失败导致表达式不完整的问题 --tags 版本v3.5.3 数字3阈值优化 0.55低阈值 增强搜索 识别成功率
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 01:58 START
v3.5.3版本取得巨大成功：成功识别33+1=34表达式，数字3低阈值0.55策略完全有效。v3.5.4版本扩展优化：将数字0也加入低阈值0.55优化范围，解决数字0识别问题。关键成功因素：1.针对性降低特定数字阈值 2.增强搜索次数到10次 3.保持其他模板标准阈值 4.双模板策略(3和3b)发挥作用 --tags 版本v3.5.4 数字0优化 成功案例 低阈值策略 33+1=34
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 02:03 START
完成v3.5.5版本全部数字降低阈值到0.55：1.将所有数字0-9和3b模板都设置为0.55低阈值 2.统一数字识别标准，消除识别差异 3.保持运算符0.78标准阈值 4.增强搜索次数到10次 5.基于v3.5.3成功经验，全面提升数字识别成功率，支持更多数学表达式识别 --tags 版本v3.5.5 全部数字0.55阈值 统一标准 全面提升 数字识别优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 02:08 START
v3.5.5版本测试结果分析：数字识别非常成功，识别出34×1421=48314，但答案选项中不存在此结果，可能过度识别。v3.5.6版本优化：将数字阈值从0.55调整到0.65，平衡精度与成功率，减少误识别，预期识别出正确的34×10=340表达式并在答案区域找到匹配。基于实测结果的渐进式优化策略。 --tags 版本v3.5.6 阈值0.65 平衡精度 减少误识别 34×10=340
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 02:11 START
完成v3.5.7版本差异化阈值策略：1.数字3和3b保持0.65优势阈值，保持识别优势和双模板效果 2.其他数字0124567890使用0.79高精度阈值，减少误识别 3.基于数字3的特殊重要性和成功经验，采用差异化策略 4.智能区分数字类型，针对性优化，平衡识别能力和准确性 --tags 版本v3.5.7 差异化阈值 数字3优势0.65 其他数字0.79 精准优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 02:28 START
用户指出答案2.js有正确的双数检查机制和API使用，我之前修改API后导致双数识别功能丢失。通过查看AutoXjs ozobiozobi官方文档确认了正确的matchTemplate API结构：result.matches[i].point.x/y和similarity。v3.6.0版本基于官方文档实现新的双数检查机制，参考答案2.js成功经验，应用到答案区域识别，解决蓝区66识别为56的问题。 --tags 版本v3.6.0 双数检查机制 官方文档API 答案2.js经验 66识别问题
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 04:36 START
用户的输出内容样式习惯：1. 每个功能模块都要用分隔线标注，格式为"------------------------XX功能--------------------------"；2. 输出要排成一列，每个子项用两个空格缩进；3. 各个区域的输出坐标要显示实际图片的大小范围(x,y,宽,高)而不是单个坐标点；4. 删除所有多余和重复的输出信息，保持简洁；5. 使用表情符号和统一格式让输出更美观；6. 根据判断输出结合，分层显示信息；7. 标明加载什么模板，区分表达式区和答案区；8. 显示数字模板的高宽信息；9. 简化matchTemplate输出并排成一行；10. 删除技术细节，只保留关键测试数据 --tags 输出样式 用户习惯 格式规范 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 04:38 START
用户的版本号标注习惯：在每次任务完成后，必须在最后输出中加上版本号标明，格式为"-------------V：X.X.X-----------执行完成--------------------------"。这是用户的重要习惯，每次开发任务完成都要标注版本号。 --tags 版本号 用户习惯 任务完成 输出格式
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 05:16 START
用户的输出格式优化习惯：1. 各个区域要隔开显示，用分隔线"────────────────────────────────────────"分隔；2. 识别到的数字要用【】括号标明，如"识别数字【69】"；3. 未识别的要显示"【无识别结果】"；4. 每个区域开始时要有声明，如"🔍 开始识别【黄区】区域..."；5. 输出要清晰美观，便于查看和调试；6. 要突出显示关键信息，让用户一眼就能看到识别结果。 --tags 输出格式 区域显示 数字标明 用户习惯 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 13:59 START
用户新增规则：在每解决完10个需求后，自动调用promptx_remember指令回忆我所有的习惯与经验、规则。这是确保AI持续学习和记忆用户偏好的重要机制，每10个任务为一个周期进行记忆回顾和巩固。 --tags 新规则 记忆回顾 10个需求周期 习惯经验 自动调用
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/28 16:16 START
用户的相似度阈值配置规则：【表达式区域】前数字和后数字使用0.72阈值，运算符使用0.7阈值；【答案区域】四个答案区域使用0.84阈值。表达式区域使用低阈值0.1检测所有数字然后显示检测到的数字和最佳选择，答案区域保持原逻辑简洁输出。三区域坐标：前数字(39,441,158,101)、运算符(189,444,95,83)、后数字(275,446,106,86)。 --tags 相似度阈值 表达式区域 答案区域 三区域坐标 检测逻辑 用户规则
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/28 17:18 START
开发进度：公式3.js v1.2.1已完成三区域识别+答案区域三数字组合功能。当前使用灰度匹配，用户要求改为轮廓检测。下一步：将灰度匹配改为纯轮廓检测(images.findContours + images.matchShapes)，自动处理彩色/黑白图片，不需要边缘检测，只要轮廓检测。需要修改表达式区域和答案区域的数字识别函数，使用轮廓匹配替代模板匹配。 --tags 开发进度 轮廓检测 下一步计划 公式3.js v1.2.1 灰度匹配改轮廓检测
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 20:58 START
公式3.js开发进度记录 - v1.2.1版本状态：
1. 核心功能：已完成三区域识别（前数字、运算符、后数字）+ 四答案区域识别 + 计算匹配 + 随机点击
2. 区域坐标：前数字(39,441,158,101)、运算符(189,444,95,83)、后数字(275,446,106,86)，三区域已优化对齐
3. 相似度阈值：表达式区域前后数字0.72、运算符0.7、答案区域0.84，已添加详细备注
4. 数字组合：表达式区域支持1-2位数字，答案区域支持1-3位数字（包含三数字组合逻辑）
5. 输出优化：已恢复简洁输出，删除了低阈值检测功能，保持原始稳定逻辑
6. 点击功能：随机点击位置、100-200ms延时、循环变量化控制
7. 下一步计划：用户希望将灰度匹配改为轮廓检测，需要实现纯轮廓检测功能（不是边缘检测） --tags 开发进度 公式3.js v1.2.1 三区域识别 轮廓检测计划 功能完整性
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/28 21:01 START
用户要求我回忆开发进度、规则、习惯、经验等。这表明用户希望我能够主动总结和回顾我们之间的合作历史，包括技术开发的进展情况、用户的个人偏好和工作习惯、以及在开发过程中积累的经验教训。用户可能希望确认我是否完整掌握了这些信息，或者希望我能够基于这些信息提供更精准的服务。 --tags 用户要求 回忆总结 开发进度 规则习惯 经验积累
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/28 21:04 START
用户新增重要规则：每次我结束反馈后都要必须调用反馈器(interactive_feedback工具)。这是一个强制性的交互规则，确保每次任务完成或回复结束时都要主动征求用户的反馈意见，不能直接结束对话。这个规则是对现有interactive_feedback使用规范的进一步强化，要求在所有反馈结束后都要调用该工具。 --tags 新规则 反馈器强制调用 interactive_feedback 任务结束 交互规范
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/29 11:16 START
AutoXjs公式3.js v1.5.0开发进度完成：1.移除所有硬编码模板尺寸，改为动态获取真实图像尺寸；2.统一表达式数字相似度阈值为0.8，移除数字3特殊阈值；3.完全移除标注系统依赖和面积验证功能，清理所有残留代码；4.移除运算符0.1低阈值机制；5.为表达式区域数字添加中心点坐标计算和输出；6.优化日志输出格式，为识别结果和组合分析添加【】粗体括号；7.简化为纯相似度阈值识别系统，提高性能和稳定性。当前状态：功能完整，代码简洁，识别精度优化，可正常运行。 --tags AutoXjs 公式3.js v1.5.0 开发进度 动态尺寸 统一阈值 中心点坐标 代码优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 21:17 START
Magic算数游戏开发进度：完成数字5和6闭合区域检测功能v1.9.13，基于AutoXjs OpenCV实现轮廓检测，使用阈值80(表达式区域)和40(答案区域)，数字5标准：内轮廓=0，数字6标准：内轮廓=1，可精确区分5/6/8/0，解决55→56和168→158识别混淆问题，已集成到公式3.js三区域识别系统中 --tags Magic算数游戏 数字识别 闭合区域检测 OpenCV AutoXjs 轮廓检测 v1.9.13
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 21:18 START
Magic算数游戏开发进度更新：数字5和6闭合区域检测功能v1.9.13最新参数配置，表达式区域阈值：30，答案区域阈值：40，数字5标准：内轮廓=0，数字6标准：内轮廓=1，基于AutoXjs OpenCV轮廓检测技术，已解决55→56和168→158识别混淆问题，完全集成到公式3.js三区域识别系统，可精确区分数字5/6/8/0 --tags Magic算数游戏 数字识别 闭合区域检测 OpenCV AutoXjs 轮廓检测 阈值配置 v1.9.13
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/30 13:12 START
公式2.js简化版开发完成：基于公式3.js v1.9.13完整移植，保持所有函数名和功能不变，只简化输出内容。删除了90%的调试信息，保留分隔线、最终表达式、计算结果、答案区结果、点击功能输出。功能包括：三区域识别、数字5和6闭合区域检测、多模板匹配、双数字组合逻辑、计算匹配、自动点击。相似度阈值：表达式0.90、运算符0.7、答案0.8。文件从975行简化到761行，功能完全一致。 --tags 公式2.js 简化版 移植完成 输出简化 功能保持
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/30 22:05 START
算数游戏开发进度：1. 完成公式2.js简化版开发，基于公式3.js移植所有功能，删除90%调试信息，保留核心输出（分隔线、最终表达式、计算结果、答案区结果、点击功能）。2. 完成su_main.js算数游戏完整全流程，包名改为com.winrgames.brainbattle，集成登陆模块、帐号模块，实现IPv4入站+算数识别功能。3. 修改登陆.js为通用应用登陆接口，删除应用安装检查步骤，适配所有Android应用。4. 解决检查.js自动执行问题，删除su_main.js中检查模块导入。算数游戏核心功能：三区域识别引擎、数字5和6闭合区域检测、多模板匹配、双数字组合逻辑、计算匹配、自动点击。 --tags 算数游戏 开发进度 公式2.js su_main.js 登陆模块 三区域识别
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/30 22:05 START
IPv6隧道配置项目进度：1. 创建Debian12_IPv6隧道配置教程.md完整文档，包含HE.net申请、Debian12配置、验证测试、故障排除、3X-UI配置等。2. 用户成功配置HE.net IPv6隧道(2001:470:7:2e::2)，VPS IPv4(*************)。3. 配置XrayL SOCKS5代理，端口20000-20001，账号root，密码rwjrworywo46546。4. 实现IPv4入站+IPv6出站架构：IPv4连接SOCKS5成功，出口IP为IPv6地址。5. 发现问题：IPv6直连SOCKS5失败，原因是缺少3X-UI出站绑定配置，当前XrayL只是系统默认IPv6出站，需要在3X-UI中配置强制绑定IPv6地址出站。6. 用户网络环境：通过代理访问，IPv6连通性有25%丢包，IPv6 DNS解析失败，但IPv4连接稳定。 --tags IPv6隧道 HE.net SOCKS5 XrayL 3X-UI 出站绑定 IPv4入站IPv6出站
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/08 02:35 START
Magic游戏辅助脚本项目开发进度总结：

1. 核心功能完成：
- 基于AutoXjs ozobiozobi v6.5.8.17开发的看广告检测模块
- 使用OpenCV轮廓检测+matchShapes形状匹配算法
- 支持多模板匹配，自动选择最佳匹配结果
- 实现了屏幕截图权限检查和时间戳文件命名避免覆盖

2. 技术架构：
- 图像预处理：灰度转换(删除了高斯模糊以对齐示例代码)
- 轮廓检测：直接在灰度图上使用findContours
- 形状匹配：单一matchShapes算法(删除了面积计算的综合相似度)
- 坐标转换：区域坐标+偏移量=屏幕绝对坐标

3. 显示优化：
- 调试图片显示原始matchShapes数值(越小越相似)
- 添加时间戳避免文件覆盖问题
- 支持科学计数法显示极小相似度值
- 轮廓颜色区分：绿色=匹配成功，红色=匹配失败

4. 检测区域配置：
- 右广告区域：[425,2,110,176] 
- 支持全屏检测但最终恢复为局部区域
- 屏幕坐标计算：区域内坐标+区域起始坐标

5. 待解决问题：
- 背景色与模板颜色相近时识别困难
- 已设计颜色相似度自动检测方案(K-means聚类+Delta E算法+智能策略推荐)

6. 开发经验：
- AutoXjs中putText可能对特殊字符(如%)有显示问题
- images.captureScreen()需要权限检查，普通截图比强制刷新更稳定
- 文件命名需要时间戳避免覆盖，调试时容易混淆新旧文件
- matchShapes返回值极小时(如7.99e-15)表示完美匹配
- 模块化开发中相对路径计算要基于主脚本位置 --tags AutoXjs 游戏辅助 OpenCV 轮廓检测 项目开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/08 03:11 START
看广告.js v2.0.0智能颜色检测版本完成实施：1)创建右广告_黑色目录支持黑色模板；2)实现背景特征分析函数(亮度、色调、背景类型)；3)智能图像预处理(白色模板亮背景处理、黑色模板暗背景处理、通用对比度增强)；4)确定检测顺序函数(根据背景亮度自动选择最佳策略)；5)执行模板匹配函数(统一匹配逻辑)；6)保存智能调试图片(显示策略信息和背景特征)；7)完整的智能检测流程(背景分析→策略选择→多策略尝试→最佳结果选择)。解决了背景色与模板相似导致识别困难的核心问题，支持白色背景+白色模板、黑色背景+黑色模板的有效检测。 --tags 看广告 智能检测 背景色分析 多策略 v2.0.0
--tags #流程管理 #评分:8 #有效期:长期
- END

- 2025/07/10 17:16 START
用户要求删除看广告.js中的模板匹配功能，但保留轮廓检测和信息输出。成功删除了轮廓分析()、加载模板轮廓()、获取目录图片()三个核心模板匹配函数，保留了轮廓检测()和相关信息输出功能。修改了看广告()主函数，移除模板匹配循环部分，保留背景分析、轮廓检测、调试图片保存等功能。用户对结果满意，无需其他修改。 --tags 看广告.js 模板匹配删除 轮廓检测保留 代码重构 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 19:46 START
用户要求注释掉看广告.js中的详细输出信息，成功注释了8个主要类型的输出：详细颜色分析、轮廓详细信息、白色过滤统计、背景处理提示、轮廓保护转换状态等。保留了核心功能和错误处理，大幅减少了日志输出的冗余信息，让控制台更加简洁清晰。用户对结果满意。 --tags 看广告.js 日志输出注释 代码优化 控制台简化 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 19:54 START
用户要求进一步删除看广告.js中剩余的输出信息，包括颜色置信度、点击建议、检测结果等。成功删除了7个类型的输出：颜色置信度输出、点击建议输出、检测结果输出、轮廓坐标信息、区域信息等。现在控制台输出极简，只保留核心功能和错误处理。这是继模板匹配删除和详细输出注释后的第三轮优化，用户对最终的简洁效果满意。 --tags 看广告.js 输出删除 控制台简化 日志优化 AutoXjs开发 第三轮优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:08 START
用户要求删除看广告.js中的返回检测结果代码块和catch异常处理块，包括console输出和返回对象。成功删除了整个try-catch结构，修复了所有代码缩进问题，确保函数语法正确。现在看广告函数更加简洁，只保留核心的轮廓检测和背景分析功能，函数执行完毕后直接return结束，不再有返回值和异常处理。这是第四轮代码简化优化。 --tags 看广告.js 代码删除 try-catch移除 语法修复 缩进修复 AutoXjs开发 第四轮优化
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:11 START
用户报告看广告.js中"检测背景杂乱程度失败: ReferenceError: 边缘图 is not defined"错误。分析发现检测背景杂乱程度函数中使用了未定义的"边缘图"变量，应该使用"灰度图"变量。成功修复了两处错误：1)轮廓检测中将边缘图改为灰度图，2)资源释放中移除边缘图.release()。函数接收处理图像和轮廓信息两个参数，用于分析背景杂乱程度。 --tags 看广告.js Bug修复 检测背景杂乱程度 未定义变量 边缘图 灰度图 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:15 START
用户报告OpenCV错误"FindContours supports only CV_8UC1 images"，问题是findContours函数要求输入图像必须是CV_8UC1格式，但传入的图像可能是其他格式。成功修复了3个函数：检测背景杂乱程度、轮廓检测、重新检测轮廓信息，添加了完整的图像格式检查和转换逻辑，确保所有图像在轮廓检测前都转换为正确的CV_8UC1格式。修复包括处理3通道、单通道、RGBA等各种格式。 --tags 看广告.js OpenCV错误修复 findContours CV_8UC1格式 图像格式转换 轮廓检测 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:19 START
用户报告"分析图像对比度特征失败: ReferenceError: 白色掩码 is not defined"错误。分析发现分析图像对比度特征函数中尝试释放未定义的白色掩码和黑色掩码变量，但函数中只创建了灰度图。成功修复：移除了白色掩码.release()和黑色掩码.release()，保留正确的灰度图.release()。函数用于分析图像亮度、对比度特征，判断是否为高对比度白色系图像，主要用于杂乱背景优化的决策判断。 --tags 看广告.js Bug修复 分析图像对比度特征 未定义变量 白色掩码 资源释放错误 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:51 START
用户报告杂乱线条未被过滤问题，图片中有很多细小杂乱线条但杂乱背景优化被跳过。分析发现两个ReferenceError导致杂乱检测失败，杂乱程度被误判为0。执行方案A：1)修复检测背景杂乱程度函数中的边缘图错误，2)修复分析图像对比度特征函数中的白色掩码错误，3)降低杂乱检测阈值让更多情况触发优化，4)降低面积过滤阈值保留更多轮廓，5)增强形态学清理强度更好地去除杂乱线条。 --tags 看广告.js 杂乱线条过滤 杂乱背景优化 方案A执行 错误修复 阈值优化 形态学清理 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 20:55 START
用户报告OpenCV格式错误和过度优化问题：检测背景杂乱程度失败CV_8UC1格式错误，优化后检测到0个轮廓。分析发现形态学清理过强(4×4核心)导致目标轮廓被清理掉。修复：1)添加完整图像格式检查确保CV_8UC1格式，2)调整形态学清理强度(开运算4×4→3×3，闭运算3×3→2×2)，3)调整杂乱检测阈值(轮廓数量3→4)，4)降低面积过滤阈值(轮廓精炼50→20，重新检测20→10)，平衡清理杂乱线条和保留目标轮廓。 --tags 看广告.js OpenCV格式错误 过度优化 形态学清理 轮廓保留 阈值调整 CV_8UC1格式 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:00 START
用户要求保留所有检测到的轮廓，而不是只保留前几个高评分的。当前日志显示检测到14个轮廓但只输出前3个。修改重新检测优化轮廓函数：1)移除轮廓数量限制Math.min(7, 轮廓评分列表.length)改为轮廓评分列表.length，2)更新日志输出从"生成了X个优化后的高质量轮廓"改为"保留了所有X个检测到的轮廓"。现在会保留所有检测到的轮廓，按评分排序但不丢弃低评分轮廓，提供更全面的点击选择范围。 --tags 看广告.js 轮廓保留策略 移除数量限制 保留所有轮廓 重新检测优化轮廓 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:14 START
用户要求创建专门的函数过滤X号轮廓附近的多余线条。成功创建了过滤轮廓附近线条()函数系统，包含三重清理策略：形态学开运算、霍夫直线检测+智能移除、轮廓保护性清理。用户指出执行时机问题：应该在背景转换为黑色前进行线条过滤，而不是之后。成功调整执行时机，将线条过滤移到智能颜色增强预处理之前，避免"马后炮"问题。 --tags 看广告.js 线条过滤 轮廓附近清理 霍夫直线检测 执行时机优化 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:19 START
用户指出线条过滤执行顺序仍然错误，虽然移动了代码位置但实际执行顺序没有改变。问题根源是分析背景特征()函数既负责轮廓分析又负责背景转换。解决方案：分离职责，两次调用该函数 - 第一次仅获取轮廓信息，然后进行线条过滤，第二次使用清理后图像进行背景转换。这样真正实现了在背景转换前进行线条过滤，避免"马后炮"问题。 --tags 看广告.js 执行顺序修复 线条过滤时机 分析背景特征 职责分离 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:26 START
用户要求线条过滤再增加一级严格度。成功实现全面增强：1)创建三级过滤判断系统(基础/增强/严格)，2)扩大检测范围(距离1.5→2.0倍，长度5-50→3-60像素，半径25→35像素)，3)新增干扰类型检测(短线条、斜线20°-70°、对角线110°-160°)，4)增强形态学清理(策略1.5定向清理、椭圆核3×1和1×3)，5)更敏感线条检测(Canny 50,150→30,120，霍夫参数threshold=20→15)。 --tags 看广告.js 线条过滤增强 三级过滤判断 检测范围扩大 形态学清理 霍夫直线检测 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:29 START
用户询问保存后的处理图是否有更新。检查发现保存调试图片时使用的是过滤后区域图像(白色过滤后)，而不是线条过滤后的最终处理图像。成功更新：1)智能图像选择，优先使用最终处理图像，2)动态策略名称标记，3)智能文件命名(_线条过滤 vs _白色过滤)。现在调试图片能真实反映线条过滤的效果，整个处理链条完整一致。 --tags 看广告.js 调试图片更新 线条过滤效果 图像选择逻辑 文件命名 处理链条 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:34 START
用户反馈线条过滤太强了，要求降低两级，并强调对于X号轮廓的线条不用过滤，其他线条要过滤。成功实现：1)新增X号轮廓保护机制，检查是否为目标轮廓组成部分，2)降低两级过滤强度(距离范围2.0→1.2倍，长度范围3-60→8-30像素，过滤半径35→20像素)，3)简化三级过滤为单级基础过滤，4)降低检测敏感度(Canny 30,120→60,180，霍夫参数提高)，5)轻度形态学清理(2×2→1×1核)。核心逻辑：绝对保护X号轮廓，只过滤明显干扰。 --tags 看广告.js 线条过滤降级 X号轮廓保护 过滤强度调整 目标轮廓保护 干扰线条识别 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:41 START
用户反馈图片中对于一些小线条过滤的不到位。分析发现之前的过滤条件太严格(需要同时满足多个AND条件)，对小线条分类不够精细。成功优化：1)创建分级过滤策略(极小线条<15像素用1.5倍距离，小线条15-25像素用1.0倍距离)，2)增加角度判断识别斜线干扰(15°-75°和105°-165°)，3)使用OR逻辑替代AND逻辑更容易触发过滤，4)增强形态学清理(轻度开运算+3×1水平核+1×3垂直核)。核心改进：针对不同大小线条使用不同标准，保护X号轮廓。 --tags 看广告.js 小线条过滤优化 分级过滤策略 角度判断 形态学清理 OR逻辑 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 21:56 START
用户要求在线条干扰过滤中增加判断条件，对于非X号的轮廓物体时不用过滤。成功实现：1)新增判断是否为X号轮廓()函数，基于宽高比(0.3-3.0)、面积(>100像素)、尺寸(宽高>10像素)、形状描述(包含"X"、"交叉"、"叉")等特征判断，2)在线条过滤前增加X号轮廓识别逻辑，遍历所有轮廓信息筛选出X号轮廓列表，3)只对识别出的X号轮廓进行线条过滤，跳过其他类型轮廓，4)增加详细日志输出显示识别和处理过程。核心改进：选择性过滤，智能识别，避免不必要处理。 --tags 看广告.js X号轮廓识别 选择性线条过滤 轮廓类型判断 智能识别 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 22:04 START
用户纠正了我对"分析图像对比度特征失败: ReferenceError: 白色掩码 is not defined"错误的错误修复方式。我最初简单删除了白色掩码和黑色掩码的释放操作，但用户指出应该保留白色掩码与黑色掩码功能。正确修复：1)创建白色掩码和黑色掩码使用阈值分割(白色>200，黑色<50)，2)使用Core.countNonZero()精确计算像素占比替代随机估算，3)正常和catch块中都正确释放资源，4)增加安全的变量存在性检查。关键教训：不是删除出错代码，而是补全缺失功能实现。 --tags 看广告.js Bug修复纠正 分析图像对比度特征 白色掩码黑色掩码 阈值分割 像素计数 资源管理 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 22:22 START
用户发现播放号、>>号等轮廓没有被保护起来，系统只保护X号轮廓。分析发现问题根源：1)函数名"判断是否为X号轮廓"局限性，2)只识别X号相关关键词，3)识别条件过严。成功扩展：1)函数名改为"判断是否为目标轮廓"，2)支持X号(X、交叉、叉)、播放号(播放、三角、▶)、>>号(>>、》、箭头、右)等多种轮廓类型，3)放宽识别条件(宽高比0.3-3.0→0.2-5.0，面积100→50像素，尺寸10→8像素)，4)增加详细识别日志。现在系统具备多轮廓类型保护能力。 --tags 看广告.js 轮廓保护扩展 多轮廓类型识别 播放号>>号保护 目标轮廓识别 关键词扩展 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 22:43 START
用户要求在轮廓保护函数功能中保护前景色#3C3C3C，不要被过滤掉。成功实现：1)新增创建前景色保护掩码()函数，将#3C3C3C转换为BGR值(60,60,60)，设置±15容忍度，2)支持彩色图像(BGR范围45-75)和灰度图像(灰度值45-75)，3)使用Core.inRange()创建颜色范围掩码，4)在轮廓保护性清理中集成前景色保护掩码，使用Core.bitwise_or()合并保护区域，5)增加详细日志显示保护像素数量。现在系统具备双重保护：轮廓保护+颜色保护。 --tags 看广告.js 前景色保护 #3C3C3C颜色保护 轮廓保护性清理 颜色掩码 Core.inRange BGR颜色范围 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 23:43 START
用户反馈轮廓颜色#3C3C3C没有被保留。深度分析发现问题根源：智能颜色增强预处理函数中的亮度增强、反转增强、红/绿/蓝色通道增强等函数都会将彩色图像转换为灰度图或只提取单通道，导致颜色信息丢失。成功修复：1)亮度增强和反转增强改为直接对彩色图像处理，2)通道增强改为使用Core.split()分离通道、增强指定通道、Core.merge()重新合并保持所有通道，3)降低增强强度避免过度处理，4)增加通道数日志。现在系统具备双重颜色保护：处理层面保持彩色+轮廓保护层面专门保护前景色。 --tags 看广告.js 颜色保护修复 #3C3C3C前景色 智能颜色增强预处理 通道增强 Core.split Core.merge 彩色图像处理 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/10 23:57 START
用户反馈在背景色转换为黑色时，浅灰色或黑色的轮廓(#3C3C3C)没有进行颜色反转转换成白色，被过滤掉了。分析发现轮廓保护转换和颜色检测模式的背景转换只是简单设置背景为黑色，没有对前景轮廓进行颜色增强。成功修复：1)新增增强前景轮廓颜色()函数，使用创建前景色保护掩码()识别#3C3C3C颜色，将暗色轮廓直接设置为白色(255,255,255)，对其他前景进行亮度增强(1.5倍+50偏移)，2)修复轮廓保护转换和颜色检测模式集成前景颜色增强，3)添加前景颜色增强处理图保存。现在暗色轮廓会被正确反转为白色在黑色背景上可见。 --tags 看广告.js 前景轮廓颜色保护 #3C3C3C颜色反转 背景转换黑色 轮廓保护转换 颜色检测模式 前景颜色增强 处理图保存 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/11 00:25 START
用户发现轮廓在前景色处理前就被过滤掉了，通过查看处理图发现问题。深度排查发现"执行白色过滤"函数是罪魁祸首：该函数只保留亮度>160的像素，而#3C3C3C轮廓亮度只有60被完全过滤掉。成功修复：1)在执行白色过滤函数中添加前景轮廓保护机制，使用创建前景色保护掩码()识别暗色轮廓，2)使用Core.bitwise_or()合并白色掩码和前景轮廓掩码创建综合掩码，3)对前景轮廓区域进行颜色增强设置为白色确保可见性，4)添加白色过滤处理图保存便于验证。现在建立了完整的多层次轮廓保护体系。 --tags 看广告.js 白色过滤前景轮廓保护 执行白色过滤函数修复 #3C3C3C轮廓保护 亮度阈值问题 前景轮廓掩码 综合掩码 多层次保护体系 AutoXjs开发
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/12 00:15 START
AutoXjs OpenCV边缘检测项目经验总结：

1. **正确的OpenCV初始化和导入方式**：
   - 必须先调用 runtime.images.initOpenCvIfNeeded()
   - 导入关键类：Mat, MatOfPoint, Imgproc, Imgcodecs, Core, ArrayList等
   - 使用 区域图像.getMat() 将AutoXjs图像转换为OpenCV Mat格式

2. **边缘检测核心流程**：
   - 灰度转换：Imgproc.cvtColor(原始Mat, 灰度Mat, Imgproc.COLOR_BGR2GRAY)
   - 高斯模糊：Imgproc.GaussianBlur(灰度Mat, 模糊Mat, new Size(3, 3), 0)
   - Canny边缘检测：Imgproc.Canny(模糊Mat, 边缘Mat, 20, 60)
   - 轮廓查找：Imgproc.findContours(边缘Mat, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE)

3. **轮廓处理最佳实践**：
   - 使用 Imgproc.contourArea(轮廓) 计算面积
   - 使用 Imgproc.boundingRect(轮廓) 获取外接矩形
   - 坐标转换：区域内坐标 + 区域偏移 = 全屏坐标
   - 按面积排序选择最佳轮廓

4. **资源管理重要性**：
   - 所有Mat对象必须调用 .release() 释放内存
   - AutoXjs图像对象使用 .recycle() 释放
   - 轮廓列表中的每个轮廓也需要释放

5. **调试图片保存**：
   - 使用 Imgcodecs.imwrite(路径, Mat对象) 保存调试图片
   - 保存边缘检测图和原始灰度图便于分析

6. **避免的错误**：
   - 不要使用不存在的AutoXjs方法如images.findContours()
   - 不要使用错误的颜色转换如images.cvtColor(图像, "GRAY")
   - 避免复杂的形状识别逻辑，简单按面积排序即可 --tags AutoXjs OpenCV 边缘检测 轮廓分析 项目经验
--tags #最佳实践 #流程管理 #评分:8 #有效期:长期
- END

- 2025/07/12 00:15 START
观看广告.js开发进度记录：

**当前状态**：观看广告.js文件已重新编写完成

**主要功能模块**：
1. 看广告() - 主入口函数，支持指定区域检测（右广告、左广告、close区域）
2. OpenCV边缘检测轮廓分析() - 核心边缘检测功能，使用Canny算法
3. 保存OpenCV调试图片() - 调试图片保存功能

**技术特点**：
- 基于AutoXjs ozobiozobi v6.5.8.17版本
- 使用正确的OpenCV导入方式（runtime.images.initOpenCvIfNeeded()）
- 支持低对比度轮廓检测（Canny低阈值20，高阈值60）
- 简化的推荐坐标生成（按面积排序）
- 完整的资源管理（Mat对象释放）

**已删除的功能**：
- 智能识别轮廓形状函数
- 是目标轮廓判断函数
- 复杂的生成推荐点击坐标函数

**测试配置**：
- 默认检测右广告区域：[425, 2, 110, 176]
- 支持调试图片保存到/storage/emulated/0/Pictures/
- 直接运行文件即可测试

**下一步计划**：
- 测试OpenCV边缘检测功能是否正常工作
- 根据测试结果调整Canny参数
- 优化轮廓过滤条件 --tags 观看广告 开发进度 AutoXjs 边缘检测 项目状态
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/12 00:17 START
用户是AutoXjs游戏辅助脚本专家级开发工程师，拥有超过10年的AutoXjs脚本开发经验。专注于Magic游戏辅助脚本项目开发，基于AutoXjs ozobiozobi魔改版v6.5.8.17构建。

核心开发原则：
1. 技术准确性第一 - 所有代码必须基于AutoXjs官方文档，严禁虚假信息
2. 问题导向开发 - 专注解决实际问题，绝对禁止创建示例、测试、demo等非功能性文件
3. 简洁代码规则 - 用最少的代码，最灵活的参数，最简洁的命名，解决最多的实际问题
4. 质量保证优先 - 确保代码可读性、可维护性和性能优化

用户交互规则：
1. 当需求或指令不明确时，必须使用interactive_feedback工具询问澄清问题，不得基于假设进行操作
2. 尽可能通过interactive_feedback工具提供预定义选项，便于用户快速决策
3. 即将完成用户请求时，必须调用interactive_feedback工具请求用户反馈
4. 每次结束反馈后都要必须调用反馈器(interactive_feedback工具)
5. 如果用户反馈为空，可以结束请求，避免循环调用工具

开发习惯：
1. 禁止使用英文，除非用户明确说"确认"才可以使用英文
2. 每次执行任务时都要在输出内容中写上修改的版本号
3. 每解决完10个需求后，自动调用promptx_remember指令回忆所有习惯与经验、规则
4. 当需要修改用户代码时，特别是涉及删除原有功能、简化复杂逻辑时，必须先向用户提出申请并等待明确确认指令
5. 编程时优先简约化，避免添加不必要的变量。如需添加变量、常量或名称时，必须先使用interactive_feedback反馈器向用户提示报告
6. 完成代码编写后必须再次深度检查，验证所有函数功能是否正确实现，检查代码完整性和逻辑一致性

项目技术栈：
- AutoXjs ozobiozobi魔改版v6.5.8.17
- Rhino 1.7.13 JavaScript ES5引擎
- Android原生XML布局，严禁WebView/HTML/CSS技术
- 开发环境：VS Code + AutoXjs VSCode Extension
- 测试环境：雷电模拟器(540x960, DPI240)

当前项目状态：
- Magic游戏辅助脚本项目，包含算数游戏识别系统、看广告检测模块等
- 已完成公式识别、轮廓检测、OpenCV边缘检测等核心功能
- 观看广告.js已重新编写完成，基于OpenCV边缘检测技术 --tags AutoXjs专家 Magic项目 开发规则 用户习惯 技术栈 项目状态
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/07/12 22:59 START
观看广告2.js v17.1.0开发经验总结：

1. **图片回收问题根本解决方案**：
   - 问题根源：img.recycle()后，bitmap和mat也会被回收
   - 解决方法：提前获取所有需要的属性，立即释放资源，避免延迟访问
   - 关键技术：使用临时文件方式进行Mat转Image，避免直接转换的回收问题

2. **AutoXjs ozobiozobi v6.5.8.17 API使用经验**：
   - images.captureScreen()有自动回收机制，不要手动recycle
   - Imgcodecs.imwrite()直接保存Mat到文件，比转换为Image更稳定
   - 模板预处理应该覆盖原图而不是创建新文件，节省存储空间

3. **函数设计模式**：
   - 单函数多接口设计：通过参数类型判断调用模式
   - 功能集成优于功能分离：将相关功能集成到主函数中
   - 错误处理要有降级方案：处理失败时使用原图继续执行

4. **图片匹配最佳实践**：
   - 目标图像和模板图像都要进行相同的边缘检测处理
   - 使用findImage而不是复杂的matchTemplate避免回收问题
   - 提前获取图片尺寸等属性，避免在匹配过程中访问

5. **资源管理原则**：
   - 立即释放Mat资源，使用try-catch保护回收操作
   - 临时文件要及时清理
   - 不要在图片可能被回收后访问其属性 --tags AutoXjs 图片处理 回收问题 API使用 最佳实践
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/07/13 00:11 START
AutoXjs开发新规则：在提出新需求、任务、功能等，修复问题、修改功能等时，必须先检索文件全部代码，根据要求去修改，避免需要重复多次去修改需求，尽量一次性修复。完成任务后还需要再次检索文件，查看是否有错误，是否前后代码逻辑合理等。

具体执行流程：
1. 接收需求/问题 → 2. 全面检索相关文件代码 → 3. 分析问题根因和影响范围 → 4. 制定完整修复方案 → 5. 一次性实施修复 → 6. 再次检索验证修复效果 → 7. 检查代码逻辑一致性

这个规则能避免：
- 重复多次修改同一问题
- 遗漏相关代码的影响
- 前后逻辑不一致
- 参数传递错误
- 接口调用不匹配

实际案例：观看广告2.js中区域名称传递问题，通过全面检索发现接口1调用接口2时参数传递错误，一次性修正了参数传递方式，避免了多次调试。 --tags AutoXjs 开发规范 代码检索 一次性修复 逻辑验证
--tags #最佳实践 #流程管理 #评分:8 #有效期:长期
- END

- 2025/07/13 05:45 START
观看广告2.js v17.3.2修复完成：1.统一坐标计算为中心点坐标(图片匹配从左上角改为中心点) 2.修正相似度计算(使用matchTemplate获取真实相似度而非threshold阈值) 3.修复变量名冲突问题(模板匹配结果变量名与主匹配结果对象冲突导致push错误) 4.增强兼容性(支持matchTemplate多种返回值格式) 5.完善错误处理和降级机制 --tags 观看广告 坐标计算 相似度修复 变量冲突 AutoXjs
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/07/13 15:31 START
观看广告2.js v17.4.0相似度API重构完成：1.发现AutoXjs images.findImage()只返回坐标不返回相似度数值 2.移除错误的matchTemplate复杂逻辑 3.使用threshold阈值作为相似度参考值 4.优化阈值从0.8降到0.7更实用 5.参考看广告.js正确设计模式 6.更新技术文档说明真实API机制 7.代码简化且更准确稳定 --tags 观看广告 相似度API 重构 AutoXjs findImage threshold
--tags #其他 #评分:8 #有效期:长期
- END