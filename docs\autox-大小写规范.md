# AutoX.js 大小写规范与编码标准

> 基于AutoX.js v6.5.8 (ozobi魔改版) 官方文档编写
> 官方文档：https://ozobiozobi.github.io/Autox_ozobi_Docs/
> 项目地址：https://github.com/ozobiozobi/Autoxjs_v6_ozobi

## 📋 目录

1. [前端XML控件大小写规范](#一前端xml控件大小写规范)
2. [后端API大小写规范](#二后端api大小写规范)
3. [常见错误与修复](#三常见错误与修复)
4. [最佳实践建议](#四最佳实践建议)

---

## 一、前端XML控件大小写规范

### 1.1 布局容器（全部小写）

| 正确用法 | 错误用法 | 说明 | 官方确认 |
|---------|---------|------|----------|
| `<horizontal>` | `<Horizontal>` | 水平布局 | ✅ |
| `<vertical>` | `<Vertical>` | 垂直布局 | ✅ |
| `<linear>` | `<Linear>` | 线性布局 | ✅ |
| `<frame>` | `<Frame>` | 帧布局 | ✅ |
| `<relative>` | `<Relative>` | 相对布局 | ✅ |
| `<grid>` | `<Grid>` | 网格布局 | ✅ |
| `<drawer>` | `<Drawer>` | 抽屉布局 | ✅ |

### 1.2 基础控件（全部小写）

| 正确用法 | 错误用法 | 说明 | 官方确认 |
|---------|---------|------|----------|
| `<button>` | `<Button>` | 按钮控件 | ✅ |
| `<text>` | `<Text>` | 文本控件 | ✅ |
| `<input>` | `<Input>` | 输入框控件 | ✅ |
| `<img>` | `<Img>` | 图片控件 | ✅ |
| `<checkbox>` | `<Checkbox>` | 复选框控件 | ✅ |
| `<radio>` | `<Radio>` | 单选框控件 | ✅ |
| `<radiogroup>` | `<RadioGroup>` | 单选框组控件 | ✅ |
| `<switch>` | `<Switch>` | 开关控件 | ✅ |
| `<progressbar>` | `<ProgressBar>` | 进度条控件 | ✅ |
| `<seekbar>` | `<SeekBar>` | 拖动条控件 | ✅ |
| `<spinner>` | `<Spinner>` | 下拉菜单控件 | ✅ |

### 1.3 高级控件（全部小写）

| 正确用法 | 错误用法 | 说明 | 官方确认 |
|---------|---------|------|----------|
| `<timepicker>` | `<TimePicker>` | 时间选择器 | ✅ |
| `<datepicker>` | `<DatePicker>` | 日期选择器 | ✅ |
| `<fab>` | `<FAB>` | 浮动按钮 | ✅ |
| `<toolbar>` | `<Toolbar>` | 标题栏 | ✅ |
| `<card>` | `<Card>` | 卡片 | ✅ |
| `<tab>` | `<Tab>` | 标签页 | ✅ |

### 1.4 特殊控件（首字母大写）

| 正确用法 | 错误用法 | 说明 | 官方确认 |
|---------|---------|------|----------|
| `<View>` | `<view>` | 视图控件 | ✅ |
| `<ScrollView>` | `<scrollview>` | 滚动视图 | ✅ |


在AutoXjs中，应该使用 ui.控件ID 直接访问控件，而不是 ui.控件ID.findOne()。

AutoXjs中的图片控件使用的是src属性，而不是@drawable/格式。@drawable/格式是Android原生的资源引用方式，但在AutoXjs中不被支持。

margin属性的格式有严格要求：
单个值：margin="10" - 四周都是10dp
两个值：margin="10 20" - 水平10dp，垂直20dp
四个值：margin="10 20 30 40" - 左上右下分别是10dp 20dp 30dp 40dp
但是我们的代码中使用了 margin="0,0,0,16dp"，这是错误的格式！应该使用空格分隔，而不是逗号。

### 1.5 XML布局示例

```xml
<!-- ✅ 正确示例 -->
<vertical>
    <horizontal background="#00A843">
        <button text="菜单" style="Widget.AppCompat.Button.Borderless"/>
        <text text="标题" layout_weight="1" gravity="center"/>
    </horizontal>

    <ScrollView layout_weight="1">
        <vertical padding="16dp">
            <card cardCornerRadius="8dp">
                <text text="内容"/>
            </card>
        </vertical>
    </ScrollView>

    <View h="1dp" background="#E0E0E0"/>
</vertical>

<!-- ❌ 错误示例 -->
<Vertical>
    <Horizontal background="#00A843">
        <Button text="菜单" background="transparent"/>
        <Text text="标题" layout_weight="1" gravity="center"/>
    </Horizontal>

    <scrollview layout_weight="1">
        <Vertical padding="16dp">
            <Card cardCornerRadius="8dp">
                <Text text="内容"/>
            </Card>
        </Vertical>
    </scrollview>

    <view h="1dp" background="#E0E0E0"/>
</Vertical>
```

## 二、后端API大小写规范

### 2.1 全局对象和模块（全部小写）

| 正确用法 | 错误用法 | 说明 | 官方确认 |
|---------|---------|------|----------|
| `java` | `Java` | Java对象操作 | ✅ |
| `android` | `Android` | Android API访问 | ✅ |
| `ui` | `UI` | 用户界面相关 | ✅ |
| `app` | `App` | 应用操作 | ✅ |
| `files` | `Files` | 文件操作 | ✅ |
| `http` | `HTTP` | 网络请求 | ✅ |
| `threads` | `Threads` | 线程操作 | ✅ |
| `floaty` | `Floaty` | 悬浮窗 | ✅ |
| `console` | `Console` | 控制台输出 | ✅ |
| `events` | `Events` | 事件监听 | ✅ |
| `device` | `Device` | 设备操作 | ✅ |
| `dialogs` | `Dialogs` | 对话框 | ✅ |
| `engines` | `Engines` | 脚本引擎 | ✅ |
| `images` | `Images` | 图像处理 | ✅ |
| `colors` | `Colors` | 颜色处理 | ✅ |
| `media` | `Media` | 多媒体 | ✅ |
| `sensors` | `Sensors` | 传感器 | ✅ |
| `storages` | `Storages` | 本地存储（注意复数） | ✅ |
| `timers` | `Timers` | 定时器 | ✅ |
| `keys` | `Keys` | 按键模拟 | ✅ |
| `shell` | `Shell` | Shell命令 | ✅ |
| `auto` | `Auto` | 无障碍服务 | ✅ |

### 2.2 常用方法调用示例

```javascript
// ✅ 正确示例
app.launch("com.tencent.mm");           // 启动微信
files.read("./config.json");            // 读取文件
http.get("https://api.example.com");    // HTTP请求
console.log("调试信息");                 // 控制台输出
threads.start(function() { ... });     // 启动线程
device.width;                           // 获取设备宽度
ui.run(function() { ... });            // UI线程执行
storages.create("myStorage");           // 创建存储

// ❌ 错误示例
App.launch("com.tencent.mm");           // 错误：首字母大写
Files.read("./config.json");            // 错误：首字母大写
HTTP.get("https://api.example.com");    // 错误：全部大写
Console.log("调试信息");                 // 错误：首字母大写


### 2.3 UI相关API（小写）

```javascript
// ✅ 正确示例
ui.layout(布局);                        // 设置UI布局
ui.run(() => { ... });                 // UI线程执行
ui.post(() => { ... });                // UI线程延迟执行
ui.inflate(布局);                       // 动态创建布局
ui.findView("id");                      // 查找控件

// ❌ 错误示例
UI.layout(布局);                        // 错误：全部大写
Ui.run(() => { ... });                 // 错误：首字母大写
```

### 2.4 权限和系统API（小写）

```javascript
// ✅ 正确示例
auto();                                 // 申请无障碍权限
floaty.checkPermission();               // 检查悬浮窗权限
requestScreenCapture();                 // 申请截图权限

// ❌ 错误示例
Auto();                                 // 错误：首字母大写
Floaty.checkPermission();               // 错误：首字母大写
```

## 三、常见错误与修复

### 3.1 XML布局常见错误

| 错误代码 | 正确代码 | 错误原因 |
|---------|---------|----------|
| `background="transparent"` | `style="Widget.AppCompat.Button.Borderless"` | AutoXjs不支持transparent属性 |
| `<view>` | `<View>` | view标签需要首字母大写 |
| `<Button>` | `<button>` | 大部分控件标签需要全部小写 |
| `<Text>` | `<text>` | 文本控件标签需要全部小写 |
| `margin="0,0,0,16dp"` | `margin="0 0 0 16dp"` | margin属性值必须用空格分隔，不能用逗号 |

### 3.2 后端API常见错误

| 错误代码 | 正确代码 | 错误原因 |
|---------|---------|----------|
| `App.launch()` | `app.launch()` | 模块名需要全部小写 |
| `Console.log()` | `console.log()` | 模块名需要全部小写 |
| `Files.read()` | `files.read()` | 模块名需要全部小写 |
| `UI.layout()` | `ui.layout()` | 模块名需要全部小写 |

### 3.3 错误修复示例

**修复前（错误）：**
```xml
<Vertical>
    <Button background="transparent" text="按钮" margin="0,0,0,16dp"/>
    <view h="1dp"/>
</Vertical>
```

**修复后（正确）：**
```xml
<vertical>
    <button style="Widget.AppCompat.Button.Borderless" text="按钮" margin="0 0 0 16dp"/>
    <View h="1dp"/>
</vertical>
```

### 3.4 margin属性格式规范

AutoXjs中margin属性必须使用**空格分隔**，不能使用逗号：

| 格式 | 说明 | 示例 |
|------|------|------|
| 单个值 | 四周相同间距 | `margin="16dp"` |
| 两个值 | 水平 垂直 | `margin="16dp 8dp"` |
| 四个值 | 左 上 右 下 | `margin="0 0 0 16dp"` |

**❌ 错误格式：**
```xml
margin="0,0,0,16dp"    <!-- 使用逗号分隔 -->
margin="0, 0, 0, 16dp" <!-- 使用逗号和空格混合 -->
```

**✅ 正确格式：**
```xml
margin="0 0 0 16dp"    <!-- 使用空格分隔 -->
margin="16dp"          <!-- 单个值 -->
margin="16dp 8dp"      <!-- 两个值 -->
```

## 四、最佳实践建议

### 4.1 开发规范

1. **严格遵循大小写规范**：避免运行时错误
2. **使用官方推荐的样式**：如`Widget.AppCompat.Button.Borderless`替代`transparent`
3. **保持代码一致性**：团队内统一编码风格
4. **及时更新文档**：发现新规范及时记录

### 4.2 调试技巧

1. **查看错误日志**：关注`ClassNotFoundException`等错误
2. **对比官方示例**：参考官方文档的正确写法
3. **逐步排查**：从简单布局开始测试
4. **使用IDE提示**：利用VSCode插件的自动补全

### 4.3 版本兼容性

- **AutoX.js v6.5.8+**：支持所有本文档提到的规范
- **向下兼容**：新版本保持对旧版本的兼容
- **及时升级**：建议使用最新版本获得最佳体验

## 五、AutoXjs API 使用规范 (v********)

### 5.1 推荐使用的新API

| 功能 | 推荐用法 | 说明 |
|------|---------|------|
| 页面布局 | `ui.layout(布局对象)` | 替代 `ui.setContentView()` |
| 控件属性设置 | `ui.控件名.attr("属性名", 值)` | 替代直接方法调用 |
| 事件绑定 | `ui.控件名.on("事件名", 回调函数)` | 标准事件绑定方式 |

### 5.2 避免使用的过时API

| 过时API | 推荐替代 | 原因 |
|---------|---------|------|
| `ui.setContentView()` | `ui.layout()` | 在v********中可能不稳定 |
| `控件.setText()` | `控件.attr("text", 值)` | 新版本推荐用法 |
| `控件.setVisibility()` | `控件.attr("visibility", "visible/gone/invisible")` | 统一属性设置方式 |
| `控件.setTextColor()` | `控件.attr("textColor", 颜色值)` | 统一颜色设置方式 |
| `控件.setChecked()` | `控件.attr("checked", 布尔值)` | 统一状态设置方式 |

### 5.3 控件属性设置标准

```javascript
// ✅ 推荐用法
ui.文本控件.attr("text", "新文本内容");
ui.按钮控件.attr("visibility", "visible");  // visible, gone, invisible
ui.开关控件.attr("checked", true);
ui.文本控件.attr("textColor", "#FF0000");
ui.控件.attr("background", "#00A843");

// ❌ 避免使用
ui.文本控件.setText("新文本内容");           // 可能在新版本中失效
ui.按钮控件.setVisibility(0);               // 数字常量不够直观
ui.开关控件.setChecked(true);               // 直接方法调用
ui.文本控件.setTextColor(colors.parseColor("#FF0000"));
```

### 5.4 页面切换标准

```javascript
// ✅ 推荐用法 - 使用ui.layout()
function 切换到主页() {
    var 主页布局 = require('./UI主页页面.js');
    ui.layout(主页布局.主页布局);

    // 初始化页面逻辑
    var 主页逻辑 = require('./主页逻辑.js');
    if (主页逻辑.初始化主页逻辑) {
        主页逻辑.初始化主页逻辑();
    }
}

// ❌ 避免使用 - ui.setContentView()可能不稳定
function 切换到主页_错误示例() {
    var 主页布局 = require('./UI主页页面.js');
    ui.setContentView(主页布局.主页布局);  // 可能导致错误
}
```

### 5.5 控件安全访问模式

```javascript
// ✅ 推荐用法 - 先检查控件是否存在
function 安全更新控件() {
    if (ui.日志列表) {
        ui.日志列表.removeAllViews();
    }
    if (ui.空状态区域) {
        ui.空状态区域.attr("visibility", "visible");
    }
    if (ui.日志计数) {
        ui.日志计数.attr("text", "0 条记录");
    }
}

// ❌ 避免使用 - 直接访问可能导致空指针异常
function 不安全更新控件() {
    ui.日志列表.removeAllViews();           // 如果控件不存在会报错
    ui.空状态区域.setVisibility(0);         // 过时API + 不安全访问
}
```

---

## 📚 参考资料

### 官方文档
- [AutoX.js官方文档](https://ozobiozobi.github.io/Autox_ozobi_Docs/) - 主要参考
- [UI控件文档](https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/basic/ui/) - UI开发指导
- [AutoX.js GitHub仓库](https://github.com/ozobiozobi/Autoxjs_v6_ozobi) - 源码和问题追踪

### 开发工具
- [VSCode AutoX.js插件](https://marketplace.visualstudio.com/items?itemName=aaroncheng.auto-js-vsce-fixed) - 开发环境
- [AutoX.js调试工具](https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/modification/other/appFunction.html) - 调试指南

---

## 📞 联系与支持

如果在使用过程中遇到问题或有改进建议，请：

1. **查阅官方文档**：首先查看AutoX.js官方文档
2. **搜索已知问题**：在项目issue中搜索类似问题
3. **提交问题报告**：详细描述问题和复现步骤
4. **参与讨论**：在QQ群1014521824进行技术讨论

**文档维护**：本文档由开发团队共同维护，定期更新

**当前版本**：v3.0
**更新日期**：2024-12-19
**文档状态**：✅ 活跃维护中

---

## 快速参考表

### API对照表

| 功能分类 | 正确API | 错误API | 使用示例 |
|---------|---------|---------|----------|
| 页面布局 | `ui.layout()` | `ui.setContentView()` | `ui.layout(布局对象)` |
| 文本设置 | `控件.attr("text", 值)` | `控件.setText()` | `ui.标题.attr("text", "新标题")` |
| 可见性 | `控件.attr("visibility", "visible")` | `控件.setVisibility(0)` | `ui.按钮.attr("visibility", "gone")` |
| 颜色设置 | `控件.attr("textColor", 颜色)` | `控件.setTextColor()` | `ui.文本.attr("textColor", "#FF0000")` |
| 状态设置 | `控件.attr("checked", 布尔值)` | `控件.setChecked()` | `ui.开关.attr("checked", true)` |
| 事件绑定 | `控件.on("事件", 回调)` | `控件.setOnClickListener()` | `ui.按钮.on("click", function(){})` |

### XML属性格式表

| 属性名 | 正确格式 | 错误格式 | 说明 |
|--------|---------|---------|------|
| padding | `padding="8dp 4dp"` | `padding="8dp,4dp"` | 空格分隔，不用逗号 |
| margin | `margin="0 0 0 16dp"` | `margin="0,0,0,16dp"` | 空格分隔，不用逗号 |
| stroke | `stroke="1dp #E0E0E0"` | `stroke="1dp,#E0E0E0"` | 空格分隔，不用逗号 |
| background | `background="#00A843"` | `background="transparent"` | 使用颜色值，不用transparent |
| style | `style="Widget.AppCompat.Button.Borderless"` | `background="transparent"` | 使用官方样式替代透明背景 |

### 控件可见性值表

| 可见性状态 | 推荐值 | 过时值 | 效果 |
|-----------|--------|--------|------|
| 可见 | `"visible"` | `0` | 控件可见且占用空间 |
| 隐藏 | `"gone"` | `8` | 控件不可见且不占用空间 |
| 不可见 | `"invisible"` | `4` | 控件不可见但占用空间 |

### 模块导出标准

| 导出类型 | 推荐属性名 | 调用方式 | 示例 |
|---------|-----------|---------|------|
| XML布局 | `布局` | `模块.布局` | `主页模块.布局` |
| 初始化函数 | `初始化逻辑` | `模块.初始化逻辑()` | `主页逻辑.初始化逻辑()` |
| 工具函数 | `函数名` | `模块.函数名()` | `工具模块.获取配置()` |
| 状态对象 | `状态` | `模块.状态` | `应用模块.状态` |

### 调试工具对照

| 调试需求 | 推荐工具 | 使用方法 | 说明 |
|---------|---------|---------|------|
| 控制台输出 | `console.log()` | `console.log("调试信息")` | 基础调试输出 |
| 错误捕获 | `try-catch` | `try{...}catch(e){console.error(e)}` | 异常处理 |
| 控件检查 | `if(ui.控件名)` | `if(ui.按钮){ui.按钮.click()}` | 安全访问控件 |
| 页面调试 | `ui.run()` | `ui.run(function(){...})` | UI线程执行 |
| 权限检查 | `auto.service` | `if(auto.service){...}` | 检查无障碍权限 |

*本文档基于AutoX.js官方文档编写，旨在提供准确的大小写规范和编码标准指导。*