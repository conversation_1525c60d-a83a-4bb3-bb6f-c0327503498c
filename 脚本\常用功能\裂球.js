/**
 * 裂球游戏模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 */

// 导入截图模块
var 截图模块 = require('./截图.js');

/**
 * 通用点击验证功能
 * @param {string} 图片路径 - 要点击的图片路径
 * @param {string} 验证图片路径 - 用于验证的图片路径
 * @param {string} 步骤名称 - 步骤描述
 * @returns {boolean} 成功返回true，失败返回false
 */
function 通用点击验证(图片路径, 验证图片路径, 步骤名称) {
    try {
        // 随机延时0.2-0.7秒
        var 随机延时 = random(200, 700);
        console.log("⏳ 随机等待 " + 随机延时 + " 毫秒");
        sleep(随机延时);

        // 查找并点击图片
        console.log("🔍 " + 步骤名称 + "：在全屏范围内查找图片");
        var 点击结果 = 截图模块.查找_图片(图片路径, undefined, undefined, undefined, undefined, 8, 200, "判断");

        if (点击结果) {
            console.log("✅ 成功点击" + 步骤名称);

            // 等待1秒后验证
            sleep(1000);
            console.log("🔍 验证是否还在界面");

            var 验证结果 = 截图模块.查找_图片(验证图片路径, undefined, undefined, undefined, undefined, 2, 200, "查找");

            if (!验证结果) {
                console.log("✅ " + 步骤名称 + "完成：界面已切换");
                return true;
            } else {
                console.log("⚠️ 仍在原界面，" + 步骤名称 + "点击可能无效");
                return false;
            }
        } else {
            console.log("⚠️ 未找到" + 步骤名称 + "图片");
            return false;
        }

    } catch (error) {
        console.error("❌ " + 步骤名称 + "功能发生错误: " + error);
        return false;
    }
}

/**
 * 游玩裂球功能
 * 第一步：点击进入分裂球游戏，第二步：点击开始分裂球游戏
 */
function 游玩_裂球() {
    try {
        console.log("🎮 开始游玩裂球功能");

        // 图片路径
        var 选择分裂球游戏 = "/storage/emulated/0/magic/assets/通用模板/选择分裂球游戏.png";
        var 开始游戏 = "/storage/emulated/0/magic/assets/通用模板/开始分裂球游戏.png";

        // 第一步：点击—进入分裂球游戏
        var 第一步结果 = 通用点击验证(选择分裂球游戏, 选择分裂球游戏, "第一步：进入分裂球游戏");

        if (第一步结果) {
            console.log("✅ 第一步成功，继续执行第二步");
        } else {
            console.log("⚠️ 第一步失败或未找到，继续执行第二步");
        }

        // 第二步：点击—开始分裂球游戏（无论第一步是否成功都执行）
        var 第二步结果 = 通用点击验证(开始游戏, 选择分裂球游戏, "第二步：开始分裂球游戏");

        if (第二步结果) {
            console.log("🎉 游玩裂球功能完成：第二步执行成功");
            return true;
        } else {
            console.log("⚠️ 游玩裂球功能未完全成功：第二步失败或未找到");
            return false;
        }

    } catch (error) {
        console.error("❌ 游玩裂球功能发生错误: " + error);
        return false;
    }
}

// 导出模块
// module.exports = {
//     游玩_裂球: 游玩_裂球
// };

// 直接运行测试（单独运行时取消注释下面几行）
console.log("🧪 开始测试游玩裂球功能");
var 结果 = 游玩_裂球();
console.log("🎯 测试结果: " + (结果 ? "✅ 成功" : "❌ 失败"));