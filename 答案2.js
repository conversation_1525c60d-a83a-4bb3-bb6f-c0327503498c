// 答案区域数字识别
// 四个答案区域：黄、蓝、绿、红
// 数字模板：3.png、4.png、5.png、6.png

// 请求屏幕捕获权限
auto.waitFor();
if (!requestScreenCapture()) {
    console.log("❌ 屏幕捕获权限获取失败");
    exit();
}
console.log("✅ 屏幕捕获权限获取成功");

// 通用的查找多个图片函数（使用matchTemplate）
function 查找多个图片(目标图, 模板图, 最大次数) {
    try {
        var 匹配结果 = images.matchTemplate(目标图, 模板图, {
            threshold: 0.8,
            max: 最大次数 || 5,
            level: -1
        });

        var 结果列表 = [];
        if (匹配结果 && 匹配结果.matches) {
            匹配结果.matches.forEach(function(match) {
                结果列表.push({
                    x: match.point.x,
                    y: match.point.y,
                    similarity: match.similarity
                });
            });
        }

        return 结果列表;

    } catch (e) {
        // 回退到findImage方法
        var 结果列表 = [];
        var 位置 = images.findImage(目标图, 模板图, {
            threshold: 0.8
        });

        if (位置) {
            结果列表.push(位置);
        }

        return 结果列表;
    }
}

// 答案位置识别函数
function 答案位置() {
    console.log("🎯 开始识别答案区域数字...");

    try {
        // 截取屏幕
        var 屏幕图 = captureScreen();
        if (!屏幕图) {
            console.log("❌ 截图失败");
            return {};
        }

        // 定义四个答案区域
        var 答案区域列表 = [
            {名称: "黄区", 坐标: [103, 648, 136, 68], 颜色: "🟨"},
            {名称: "蓝区", 坐标: [303, 650, 136, 66], 颜色: "🟦"},
            {名称: "绿区", 坐标: [103, 788, 131, 70], 颜色: "🟩"},
            {名称: "红区", 坐标: [303, 785, 131, 71], 颜色: "🟥"}
        ];

        // 加载数字模板（包含尺寸信息）- 完整版0-9
        var 数字模板 = {
            "0": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/0.png")),
                尺寸: {宽: 41, 高: 35}
            },
            "1": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/1.png")),
                尺寸: {宽: 35, 高: 35}
            },
            "2": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/2.png")),
                尺寸: {宽: 35, 高: 33}
            },
            "3": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/3.png")),
                尺寸: {宽: 36, 高: 41}
            },
            "4": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/4.png")),
                尺寸: {宽: 35, 高: 40}
            },
            "5": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/5.png")),
                尺寸: {宽: 36, 高: 35}
            },
            "6": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/6.png")),
                尺寸: {宽: 36, 高: 36}
            },
            "7": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/7.png")),
                尺寸: {宽: 36, 高: 41}
            },
            "8": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/8.png")),
                尺寸: {宽: 33, 高: 33}
            },
            "9": {
                图片: images.read(files.path("../../assets/算数游戏/答案数字/9.png")),
                尺寸: {宽: 37, 高: 38}
            }
        };

        // 检查模板是否加载成功
        var 模板加载失败 = false;
        for (var 数字 in 数字模板) {
            if (!数字模板[数字].图片) {
                console.log("❌ 数字" + 数字 + "模板加载失败");
                模板加载失败 = true;
            } else {
                console.log("✅ 数字" + 数字 + "模板加载成功，尺寸: " + 数字模板[数字].尺寸.宽 + "×" + 数字模板[数字].尺寸.高);
            }
        }

        if (模板加载失败) {
            return {};
        }

        console.log("✅ 数字模板加载成功");

        // 识别结果
        var 识别结果 = {};

        // 遍历每个答案区域
        答案区域列表.forEach(function(区域) {
            console.log("\n🔍 识别" + 区域.颜色 + 区域.名称 + "...");

            // 截取区域图片
            var 区域图 = images.clip(屏幕图, 区域.坐标[0], 区域.坐标[1], 区域.坐标[2], 区域.坐标[3]);
            var 灰度图 = images.grayscale(区域图);

            // 在该区域查找所有数字（紧密排列版）
            var 找到的数字 = [];

            // 先收集所有可能的匹配
            var 所有匹配 = [];

            for (var 数字 in 数字模板) {
                var 模板信息 = 数字模板[数字];
                var 模板灰度 = images.grayscale(模板信息.图片);

                // 使用matchTemplate找到所有匹配位置
                try {
                    var 匹配结果 = images.matchTemplate(灰度图, 模板灰度, {
                        threshold: 0.8,
                        max: 5,
                        level: -1
                    });

                    if (匹配结果 && 匹配结果.matches) {
                        匹配结果.matches.forEach(function(match) {
                            所有匹配.push({
                                数字: 数字,
                                x坐标: match.point.x,
                                y坐标: match.point.y,
                                相似度: match.similarity,
                                模板尺寸: 模板信息.尺寸
                            });
                            console.log("  找到数字" + 数字 + "，位置: " + match.point.x + "," + match.point.y + " 相似度: " + match.similarity.toFixed(3));
                        });
                    }
                } catch (e) {
                    // 回退到findImage
                    var 位置 = images.findImage(灰度图, 模板灰度, {
                        threshold: 0.8
                    });

                    if (位置) {
                        所有匹配.push({
                            数字: 数字,
                            x坐标: 位置.x,
                            y坐标: 位置.y,
                            相似度: 0.85,
                            模板尺寸: 模板信息.尺寸
                        });
                        console.log("  找到数字" + 数字 + "，位置: " + 位置.x + "," + 位置.y + " 相似度: 0.850");
                    }
                }

                模板灰度.recycle();
            }

            // 按x坐标排序
            所有匹配.sort(function(a, b) {
                return a.x坐标 - b.x坐标;
            });

            // 智能去重：区分紧密数字(1-3px)和分离数字(15-20px)
            for (var i = 0; i < 所有匹配.length; i++) {
                var 当前匹配 = 所有匹配[i];
                var 保留 = true;

                for (var j = 0; j < 找到的数字.length; j++) {
                    var 已有匹配 = 找到的数字[j];
                    var x距离 = Math.abs(当前匹配.x坐标 - 已有匹配.x坐标);
                    var y距离 = Math.abs(当前匹配.y坐标 - 已有匹配.y坐标);

                    // 只有非常接近的位置才认为是重复（重叠识别）
                    if (x距离 < 3 && y距离 < 3) {
                        // 完全重叠，只保留相似度高的
                        if (当前匹配.相似度 <= 已有匹配.相似度) {
                            保留 = false;
                            console.log("    去重：位置(" + 当前匹配.x坐标 + "," + 当前匹配.y坐标 + ")数字" + 当前匹配.数字 + "被去除，保留数字" + 已有匹配.数字);
                            break;
                        } else {
                            // 当前匹配更好，移除已有的
                            console.log("    去重：位置(" + 已有匹配.x坐标 + "," + 已有匹配.y坐标 + ")数字" + 已有匹配.数字 + "被替换为数字" + 当前匹配.数字);
                            找到的数字.splice(j, 1);
                            j--;
                        }
                    }
                    // 1-3像素间距：紧密数字，保留
                    // 15-20像素间距：分离数字，保留
                    // 只有<3像素才认为是重复识别
                }

                if (保留) {
                    找到的数字.push(当前匹配);
                }
            }

            // 处理识别结果（优化间距算法）
            var 完整数字 = "";

            if (找到的数字.length === 0) {
                console.log("  未找到任何数字");
            } else {
                // 按x坐标重新排序
                找到的数字.sort(function(a, b) {
                    return a.x坐标 - b.x坐标;
                });

                // 分析所有可能的数字组合，找到最佳的
                var 最佳组合 = null;
                var 最高总相似度 = 0;

                // 尝试单个数字
                for (var s = 0; s < 找到的数字.length; s++) {
                    var 单个数字 = 找到的数字[s];
                    if (单个数字.相似度 > 最高总相似度) {
                        最高总相似度 = 单个数字.相似度;
                        最佳组合 = [单个数字];
                    }
                }

                // 尝试两个数字的组合（基于真实间距规律）
                for (var i = 0; i < 找到的数字.length - 1; i++) {
                    for (var j = i + 1; j < 找到的数字.length; j++) {
                        var 第一个 = 找到的数字[i];
                        var 第二个 = 找到的数字[j];

                        // 正确计算间距：第一个数字右边缘到第二个数字左边缘的距离
                        var 第一个右边缘 = 第一个.x坐标 + 第一个.模板尺寸.宽;
                        var 真实间距 = 第二个.x坐标 - 第一个右边缘;

                        // 根据数字类型判断合理间距
                        var 间距合理 = false;

                        if (第一个.数字 === "1" || 第二个.数字 === "1") {
                            // 包含数字1：间距应该 < 37像素（新增条件）
                            间距合理 = (真实间距 >= -5 && 真实间距 < 37);
                        } else {
                            // 其他数字组合：间距应该 < 10像素
                            间距合理 = (真实间距 >= 1 && 真实间距 < 10);
                        }

                        if (间距合理) {
                            var 总相似度 = 第一个.相似度 + 第二个.相似度;
                            var 间距类型 = (第一个.数字 === "1" || 第二个.数字 === "1") ? "含1" : "普通";

                            console.log("  组合分析: " + 第一个.数字 + "(" + 第一个.x坐标 + "," + 第一个.相似度.toFixed(3) + ") + " + 第二个.数字 + "(" + 第二个.x坐标 + "," + 第二个.相似度.toFixed(3) + ") 真实间距:" + 真实间距 + "px(" + 间距类型 + ") 总相似度:" + 总相似度.toFixed(3));

                            if (总相似度 > 最高总相似度) {
                                最高总相似度 = 总相似度;
                                最佳组合 = [第一个, 第二个];
                                console.log("    -> 更新最佳组合");
                            }
                        } else {
                            console.log("  间距不合理: " + 第一个.数字 + "+" + 第二个.数字 + " 真实间距:" + 真实间距 + "px (期望: " + (第一个.数字 === "1" || 第二个.数字 === "1" ? "-5~37px" : "1~10px") + ")");
                        }
                    }
                }

                // 使用最佳组合
                if (最佳组合) {
                    最佳组合.forEach(function(数字) {
                        完整数字 += 数字.数字;
                    });
                    console.log("  最佳组合: " + 完整数字 + " (总相似度: " + 最高总相似度.toFixed(3) + ")");
                }
            }

            if (完整数字) {
                // 计算数字在屏幕上的实际坐标范围
                var 实际坐标 = null;
                if (最佳组合 && 最佳组合.length > 0) {
                    // 找到最左和最右的数字位置
                    var 最左x = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标; }));
                    var 最右x = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.x坐标 + 数字.模板尺寸.宽; }));
                    var 最上y = Math.min.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标; }));
                    var 最下y = Math.max.apply(Math, 最佳组合.map(function(数字) { return 数字.y坐标 + 数字.模板尺寸.高; }));

                    // 转换为屏幕绝对坐标
                    实际坐标 = {
                        x: 区域.坐标[0] + 最左x,
                        y: 区域.坐标[1] + 最上y,
                        宽: 最右x - 最左x,
                        高: 最下y - 最上y
                    };
                }

                识别结果[区域.名称] = {
                    数字: 完整数字,
                    区域: 区域.坐标,
                    中心点: [
                        区域.坐标[0] + 区域.坐标[2] / 2,
                        区域.坐标[1] + 区域.坐标[3] / 2
                    ],
                    坐标: 实际坐标
                };

                if (实际坐标) {
                    console.log("✅ " + 区域.颜色 + 区域.名称 + " 识别到: " + 完整数字 + "，坐标: (" + 实际坐标.x + "," + 实际坐标.y + "," + 实际坐标.宽 + "," + 实际坐标.高 + ")，1和其他数字的组合间距要小于37像素");
                } else {
                    console.log("✅ " + 区域.颜色 + 区域.名称 + " 识别到: " + 完整数字 + "，1和其他数字的组合间距要小于37像素");
                }
            } else {
                console.log("❌ " + 区域.颜色 + 区域.名称 + " 未识别到数字");
            }

            // 清理资源
            区域图.recycle();
            灰度图.recycle();
        });

        // 清理模板资源
        for (var 数字 in 数字模板) {
            数字模板[数字].图片.recycle();
        }

        console.log("\n📊 识别完成，结果:");
        for (var 区域名 in 识别结果) {
            var 结果 = 识别结果[区域名];
            if (结果.坐标) {
                console.log("  " + 区域名 + ": " + 结果.数字 + " (中心: " + 结果.中心点[0] + "," + 结果.中心点[1] + ") (坐标: " + 结果.坐标.x + "," + 结果.坐标.y + "," + 结果.坐标.宽 + "," + 结果.坐标.高 + ")");
            } else {
                console.log("  " + 区域名 + ": " + 结果.数字 + " (中心: " + 结果.中心点[0] + "," + 结果.中心点[1] + ")");
            }
        }

        return 识别结果;

    } catch (e) {
        console.log("❌ 答案识别失败: " + e);
        return {};
    }
}

// 导出函数
// module.exports = {
//     答案位置: 答案位置
// };

// 测试代码
console.log("🧪 测试答案区域识别...");
var 结果 = 答案位置();
console.log("🎯 最终结果:", JSON.stringify(结果, null, 2));