/**
 * 通用应用登陆模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 * 适配所有应用的通用登陆接口
 */
//通用应用登陆功能
function 登陆_游戏(游戏包名, 游戏名称, 等待时间, 检查间隔) {
    try {
        // 设置默认参数
        游戏包名 = 游戏包名 || "com.winrgames.bigtime";
        游戏名称 = 游戏名称 || "BigTime";
        等待时间 = 等待时间 || 5000;
        检查间隔 = 检查间隔 || 200;

        console.log("🚀 开始应用登陆流程: " + 游戏名称);

        // 第一步：检查应用是否已在活动中
        var 当前包名 = currentPackage();
        if (当前包名 === 游戏包名) {
            console.log("✅ 应用已在运行: " + 游戏名称);
            return true;
        }
        console.log("📱 当前应用: " + 当前包名);

        // 第二步：启动应用
        console.log("🚀 正在启动应用: " + 游戏名称);
        var 启动结果 = app.launch(游戏包名);

        if (!启动结果) {
            console.log("❌ 应用启动失败: " + 游戏名称);
            return false;
        }

        // 第三步：等待应用启动完成
        console.log("⏳ 等待应用启动完成...");
        var 开始时间 = new Date().getTime();
        var 超时时间 = 开始时间 + 等待时间;

        while (new Date().getTime() < 超时时间) {
            var 当前应用 = currentPackage();
            if (当前应用 === 游戏包名) {
                var 耗时 = new Date().getTime() - 开始时间;
                console.log("✅ 应用启动成功: " + 游戏名称 + " (耗时: " + 耗时 + "ms)");
                return true;
            }
            sleep(检查间隔);
        }

        // 第四步：超时处理
        console.log("⚠️ 应用启动超时: " + 游戏名称 + " (等待时间: " + 等待时间 + "ms)");
        return false;

    } catch (error) {
        console.error("❌ 登陆应用时发生错误: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    登陆_游戏: 登陆_游戏
};


