<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754207232252_ecn1txbno" time="2025/08/03 15:47">
    <content>
      Magic游戏辅助脚本项目开发进度总结：
    
      ## 项目概况
      - 项目名称：Magic游戏辅助脚本
      - 技术栈：AutoXjs ozobiozobi魔改版v6.5.8.17
      - 开发环境：雷电模拟器(6核+6G)，分辨率540x960，DPI240
      - 设备信息：华为HMA-AL00，Android 9，ARM64架构
    
      ## 核心功能开发完成
      1. **OCR符号识别引擎**：完成单线程三区域识别架构
      - 上区域[2,2,536,120]、中区域[427,145,110,264]、下区域[1,767,418,185]
      - 支持原图、二值化、反向二值化、图片模板匹配4种识别方式
      - 性能提升80.3%（从16.6秒降到3.3秒）
    
      2. **关键技术突破**：
      - 解决PaddleOCR多线程崩溃问题（SIGABRT native库崩溃）
      - 实现三区域优化，面积减少77%但保持识别精度
      - 完善的智能结果合并算法，支持去重和置信度比较
    
      ## 当前开发状态
      - 核心识别引擎：✅ 完成且稳定
      - 符号点击功能：✅ 完成，包含2秒延时和状态反馈
      - 界面检测：✅ 完成游戏开始界面检测
      - 错误处理：✅ 完善的异常捕获和资源管理
    
      ## 技术经验积累
      1. **PaddleOCR限制**：不支持多线程，必须单线程顺序执行
      2. **性能优化策略**：区域裁剪比多线程更有效且稳定
      3. **资源管理**：及时释放图像资源，避免内存泄漏
      4. **调试优化**：使用traceLog()和详细的console.log输出
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754207260234_ycw69dbvi" time="2025/08/03 15:47">
    <content>
      用户编码习惯和开发规则总结：
    
      ## 编码风格偏好
      1. **语言使用**：中文变量名和函数名，中文注释
      2. **代码结构**：简洁优先，避免过度抽象和复杂设计模式
      3. **函数命名**：动词_名词格式（如：查找_图片、登陆_帐号）
      4. **参数设计**：全部可调，不硬编码，提供合理默认值
    
      ## 开发原则
      1. **功能导向**：绝对禁止创建示例、测试、demo等非功能性文件
      2. **问题解决**：专注解决实际问题，每个函数都要解决具体使用场景
      3. **性能优化**：及时释放资源，避免内存泄漏，使用高效API
      4. **错误处理**：完善的try-catch机制，详细的错误日志
    
      ## 技术要求
      1. **严格遵循官方文档**：所有代码基于AutoXjs官方文档，禁止虚假信息
      2. **兼容性优先**：确保Android 9+系统兼容，适配雷电模拟器环境
      3. **稳定性第一**：宁可牺牲性能也要确保稳定运行，避免崩溃
      4. **调试友好**：使用traceLog()详细日志，便于问题定位
    
      ## 开发流程
      1. **需求确认**：多轮确认需求，拆解为具体技术实现点
      2. **代码检查**：修改前必须检查相关文件，理解现有逻辑
      3. **方案设计**：提供双方案（标准实现+替代方案）
      4. **质量保证**：代码覆盖率80%+，关键函数100%注释
    
      ## 特殊要求
      1. **禁用WebView**：严格禁止HTML/CSS/JS前端技术
      2. **原生UI优先**：使用Android原生XML布局和AutoXjs内置组件
      3. **中文优先**：文档和注释使用中文，保持自然语言描述
      4. **实用性**：每个功能都要有实际应用价值，避免冗余代码
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754207285601_49v3teum1" time="2025/08/03 15:48">
    <content>
      Magic项目关键技术经验和解决方案：
    
      ## 重大技术突破
      1. **PaddleOCR多线程崩溃解决**：
      - 问题：SIGABRT native库崩溃，libpaddle_light_api_shared.so
      - 根因：PaddleOCR不支持多线程，资源竞争导致native崩溃
      - 解决：改为单线程顺序执行，完全避免多线程OCR调用
      - 结果：100%稳定，无崩溃
    
      2. **三区域识别优化**：
      - 策略：将全屏识别改为三个关键区域识别
      - 区域：上区域[2,2,536,120]、中区域[427,145,110,264]、下区域[1,767,418,185]
      - 效果：面积减少77%，性能提升80.3%（16.6秒→3.3秒）
      - 优势：保持识别精度，大幅提升速度
    
      ## 核心技术架构
      1. **统一识别符号引擎**：
      - 4种识别方式：原图、二值化、反向二值化、图片模板匹配
      - 智能结果合并：去重、置信度比较、坐标验证
      - 性能监控：详细的耗时统计和分析
    
      2. **资源管理策略**：
      - 及时释放：每个区域图像使用后立即recycle()
      - 内存控制：避免图像对象累积导致内存泄漏
      - 错误处理：完善的try-catch和资源清理
    
      ## 环境适配经验
      1. **雷电模拟器优化**：
      - CPU核心：从4核心改为2核心，避免过载
      - 内存配置：6G内存环境下的最佳实践
      - 分辨率：540x960，DPI240的精确适配
    
      2. **设备兼容性**：
      - 华为HMA-AL00，Android 9，ARM64架构
      - AutoXjs ozobiozobi v6.5.8.17版本特性
      - 权限管理和前台服务配置
    </content>
    <tags>#最佳实践</tags>
  </item>
</memory>