/**
 * 通用图标管理器
 * 为所有页面提供统一的FontAwesome图标应用功能
 * 支持自动字体加载、语义化颜色
 */

var FA图标库 = require('./FontAwesome图标库.js');
var 字体管理器 = require('./字体管理器.js');
var 全局样式 = require('./公共样式.js');

// 图标应用状态
var 图标状态 = {
    FontAwesome字体: null,
    Roboto字体: null,
    RobotoBold字体: null,
    字体已加载: false,
    Roboto字体已加载: false,
    RobotoBold字体已加载: false,
    应用队列: []
};

/**
 * 通用图标管理器
 */
var 通用图标管理器 = {
    
    /**
     * 初始化图标系统
     * @param {Function} 回调函数
     */
    初始化: function(回调函数) {
        console.log("初始化通用图标系统...");

        // 加载FontAwesome字体
        字体管理器.获取FontAwesome字体(function(fontAwesome, 错误信息) {
            if (fontAwesome) {
                图标状态.FontAwesome字体 = fontAwesome;
                图标状态.字体已加载 = true;
                console.log("FontAwesome字体加载成功");
            } else {
                console.warn("FontAwesome字体加载失败:", 错误信息);
                图标状态.字体已加载 = false;
            }

            // 加载Roboto字体
            字体管理器.获取Roboto字体(function(roboto, roboto错误信息) {
                if (roboto) {
                    图标状态.Roboto字体 = roboto;
                    图标状态.Roboto字体已加载 = true;
                    console.log("Roboto字体加载成功");
                } else {
                    console.warn("Roboto字体加载失败:", roboto错误信息);
                    图标状态.Roboto字体已加载 = false;
                }

                // 加载RobotoBold字体
                字体管理器.获取RobotoBold字体(function(robotoBold, robotoBold错误信息) {
                    if (robotoBold) {
                        图标状态.RobotoBold字体 = robotoBold;
                        图标状态.RobotoBold字体已加载 = true;
                        console.log("RobotoBold字体加载成功");
                    } else {
                        console.warn("RobotoBold字体加载失败:", robotoBold错误信息);
                        图标状态.RobotoBold字体已加载 = false;
                    }

                    // 处理待应用的图标队列
                    通用图标管理器.处理应用队列();

                    var 成功消息 = [];
                    if (图标状态.字体已加载) 成功消息.push("FontAwesome");
                    if (图标状态.Roboto字体已加载) 成功消息.push("Roboto");
                    if (图标状态.RobotoBold字体已加载) 成功消息.push("RobotoBold");

                    if (成功消息.length > 0) {
                        if (回调函数) 回调函数(true, 成功消息.join("、") + "字体加载成功");
                    } else {
                        if (回调函数) 回调函数(false, "所有字体加载失败");
                    }
                });
            });
        });
    },

    /**
     * 应用图标到UI控件
     * @param {String} 控件ID 
     * @param {String} 图标分类 
     * @param {String} 图标名称 
     * @param {Object} 选项 
     */
    应用图标: function(控件ID, 图标分类, 图标名称, 选项) {
        选项 = 选项 || {};
        
        var 图标配置 = {
            控件ID: 控件ID,
            图标分类: 图标分类,
            图标名称: 图标名称,
            颜色: 选项.颜色,
            大小: 选项.大小 || "16sp",
            前缀文本: 选项.前缀文本 || "",
            后缀文本: 选项.后缀文本 || ""
        };

        if (图标状态.字体已加载) {
            this.立即应用图标(图标配置);
        } else {
            // 添加到队列，等待字体加载完成
            图标状态.应用队列.push(图标配置);
        }
    },

    /**
     * 立即应用图标
     * @param {Object} 图标配置 
     */
    立即应用图标: function(图标配置) {
        try {
            var 控件 = ui[图标配置.控件ID];
            if (!控件) {
                // 静默处理控件不存在的情况，避免控制台警告
                return;
            }

            var 图标字符, 图标颜色;

            if (图标状态.FontAwesome字体) {
                // 使用FontAwesome图标
                var 图标信息 = FA图标库.获取完整图标(图标配置.图标分类, 图标配置.图标名称);
                图标字符 = 图标信息.字符;
                图标颜色 = 图标配置.颜色 || 图标信息.颜色;

                // 设置字体
                控件.setTypeface(图标状态.FontAwesome字体);
            } else {
                // FontAwesome字体未加载，跳过图标应用
                console.warn("FontAwesome字体未加载，跳过图标应用:", 图标配置.控件ID);
                return;
            }

            // 组合完整文本
            var 完整文本 = 图标配置.前缀文本 + 图标字符 + 图标配置.后缀文本;
            
            // 应用到控件
            控件.setText(完整文本);
            控件.setTextColor(colors.parseColor(图标颜色));
            
            if (图标配置.大小) {
                var 大小值 = parseInt(图标配置.大小);
                控件.setTextSize(大小值);
            }

        } catch (e) {
            console.error("应用图标失败:", e);
        }
    },

    /**
     * 处理应用队列
     */
    处理应用队列: function() {
        while (图标状态.应用队列.length > 0) {
            var 图标配置 = 图标状态.应用队列.shift();
            this.立即应用图标(图标配置);
        }
    },

    /**
     * 批量应用导航栏图标
     * @param {Object} 导航配置 
     */
    应用导航栏图标: function(导航配置) {
        // 脚本配置图标
        if (导航配置.脚本图标) {
            this.应用图标("脚本图标", "工具", "设置", {
                颜色: 导航配置.脚本图标.颜色 || "#CCFFFFFF",
                大小: "20sp"
            });
        }

        // 主页图标
        if (导航配置.主页图标) {
            this.应用图标("主页图标", "导航", "主页", {
                颜色: 导航配置.主页图标.颜色 || 全局样式.颜色主题.白色,
                大小: "20sp"
            });
        }

        // 日志图标
        if (导航配置.日志图标) {
            this.应用图标("日志图标", "文件", "文档", {
                颜色: 导航配置.日志图标.颜色 || "#CCFFFFFF",
                大小: "20sp"
            });
        }

        // 菜单图标
        if (导航配置.菜单图标) {
            this.应用图标("菜单按钮", "导航", "菜单", {
                颜色: 导航配置.菜单图标.颜色 || 全局样式.颜色主题.白色,
                大小: "22sp"
            });
        }
    },

    /**
     * 批量应用操作按钮图标
     * @param {Object} 按钮配置 
     */
    应用操作按钮图标: function(按钮配置) {
        // 运行按钮
        if (按钮配置.运行按钮) {
            this.应用图标("运行按钮", "媒体", "播放", {
                颜色: "#FFFFFF",  // 白色图标在绿色背景上可见
                后缀文本: " 运行"
            });
        }

        // 停止按钮
        if (按钮配置.停止按钮) {
            this.应用图标("停止按钮", "媒体", "停止", {
                颜色: "#FFFFFF",  // 白色图标在绿色背景上可见
                后缀文本: " 停止"
            });
        }

        // 刷新按钮
        if (按钮配置.刷新按钮) {
            this.应用图标("刷新按钮", "操作", "刷新", {
                颜色: "#4CAF50",  // 绿色图标，无背景
                大小: "16sp"
            });
        }

        // 删除按钮
        if (按钮配置.删除按钮) {
            this.应用图标("删除数据按钮", "操作", "删除", {
                颜色: "#4CAF50",  // 绿色图标，无背景
                大小: "16sp"
            });
        }

        // 保存按钮
        if (按钮配置.保存按钮) {
            this.应用图标("保存配置", "操作", "保存", {
                颜色: "#FFFFFF",  // 白色图标在绿色背景上可见
                前缀文本: "",
                后缀文本: " 保存配置"
            });
        }

        // 重置按钮
        if (按钮配置.重置按钮) {
            this.应用图标("重置配置", "操作", "撤销", {
                颜色: "#FFFFFF",  // 白色图标在绿色背景上可见
                前缀文本: "",
                后缀文本: " 重置配置"
            });
        }
    },

    /**
     * 应用抽屉页面图标
     * @param {Object} 抽屉配置
     */
    应用抽屉页面图标: function(抽屉配置) {
        // 无障碍权限图标
        if (抽屉配置.无障碍图标) {
            this.应用图标("无障碍图标", "安全", "盾牌", {
                颜色: "#FFFFFF",
                大小: "24sp"
            });
        }

        // 悬浮窗权限图标
        if (抽屉配置.悬浮窗图标) {
            this.应用图标("悬浮窗图标", "设备", "手机", {
                颜色: "#FFFFFF",
                大小: "24sp"
            });
        }

        // 前台服务权限图标
        if (抽屉配置.前台服务图标) {
            this.应用图标("前台服务图标", "安全", "盾牌", {
                颜色: "#FFFFFF",
                大小: "24sp"
            });
        }

        // 存储权限图标
        if (抽屉配置.存储权限图标) {
            this.应用图标("存储权限图标", "文件", "文件夹", {
                颜色: "#FFFFFF",
                大小: "24sp"
            });
        }

        // 通知访问权限图标
        if (抽屉配置.通知访问图标) {
            this.应用图标("通知访问图标", "通信", "铃铛", {
                颜色: "#FFFFFF",
                大小: "24sp"
            });
        }

        // 截图权限图标
        if (抽屉配置.截图图标) {
            this.应用图标("截图图标", "媒体", "相机", {
                颜色: "#FFFFFF",
                大小: "26sp"
            });
        }
    },

    /**
     * 应用日志页面图标
     * @param {Object} 日志配置
     */
    应用日志页面图标: function(日志配置) {
        // 清空日志图标
        if (日志配置.清空图标) {
            this.应用图标("清空日志", "操作", "删除", {
                大小: "20sp"
            });
        }

        // 导出日志图标
        if (日志配置.导出图标) {
            this.应用图标("导出日志", "操作", "下载", {
                大小: "20sp"
            });
        }
    },

    /**
     * 应用悬浮球图标
     * @param {Object} 悬浮球配置
     */
    应用悬浮球图标: function(悬浮球配置) {
        // 主悬浮球生化危险图标
        if (悬浮球配置.主球图标) {
            this.应用图标("生化危险图标", "安全", "生化危险", {
                颜色: "#FFFFFF",
                大小: "20sp"
            });
        }

        // 开始按钮图标
        if (悬浮球配置.开始图标) {
            this.应用图标("开始图标", "媒体", "播放", {
                颜色: "#333333",
                大小: "16sp"
            });
        }

        // 重启按钮图标
        if (悬浮球配置.重启图标) {
            this.应用图标("重启图标", "操作", "刷新", {
                颜色: "#333333",
                大小: "16sp"
            });
        }

        // 主页按钮图标
        if (悬浮球配置.主页图标) {
            this.应用图标("主页图标", "导航", "主页", {
                颜色: "#333333",
                大小: "16sp"
            });
        }

        // 日志按钮图标
        if (悬浮球配置.日志图标) {
            this.应用图标("日志图标", "文件", "文档", {
                颜色: "#333333",
                大小: "16sp"
            });
        }
    },

    /**
     * 为悬浮球控件应用图标 (支持floaty.window控件)
     * @param {Object} 悬浮球窗口实例
     * @param {String} 控件ID
     * @param {String} 图标分类
     * @param {String} 图标名称
     * @param {Object} 选项
     */
    应用悬浮球控件图标: function(悬浮球窗口, 控件ID, 图标分类, 图标名称, 选项) {
        选项 = 选项 || {};

        try {
            // 检查悬浮球窗口和控件是否存在
            if (!悬浮球窗口 || !悬浮球窗口[控件ID]) {
                console.warn("悬浮球控件不存在:", 控件ID);
                return;
            }

            var 控件 = 悬浮球窗口[控件ID];
            var 图标字符, 图标颜色;

            if (图标状态.FontAwesome字体) {
                // 使用FontAwesome图标
                var 图标信息 = FA图标库.获取完整图标(图标分类, 图标名称);
                图标字符 = 图标信息.字符;
                图标颜色 = 选项.颜色 || 图标信息.颜色;

                // 设置字体
                控件.setTypeface(图标状态.FontAwesome字体);
            } else {
                console.warn("FontAwesome字体未加载，无法应用悬浮球图标");
                return;
            }

            // 组合完整文本
            var 前缀文本 = 选项.前缀文本 || "";
            var 后缀文本 = 选项.后缀文本 || "";
            var 完整文本 = 前缀文本 + 图标字符 + 后缀文本;

            // 应用到控件
            控件.setText(完整文本);
            控件.setTextColor(colors.parseColor(图标颜色));

            if (选项.大小) {
                var 大小值 = parseInt(选项.大小);
                控件.setTextSize(大小值);
            }

            console.log("悬浮球图标应用成功:", 控件ID, 图标分类 + "." + 图标名称);

        } catch (e) {
            console.error("应用悬浮球控件图标失败:", e);
        }
    },

    /**
     * 应用粗体字体到导航栏标题 (备用方法，现在使用XML的textStyle="bold")
     * @param {Array} 标题控件ID列表
     */
    应用导航栏字体: function(标题控件ID列表) {
        // 现在使用XML中的textStyle="bold"，此方法保留备用
        console.log("字体已在XML中设置为粗体，无需动态应用");
    },

    /**
     * 获取图标状态
     */
    获取状态: function() {
        return {
            字体已加载: 图标状态.字体已加载,
            Roboto字体已加载: 图标状态.Roboto字体已加载,
            RobotoBold字体已加载: 图标状态.RobotoBold字体已加载,
            队列长度: 图标状态.应用队列.length
        };
    },

    /**
     * 清理资源
     */
    清理资源: function() {
        try {
            // 清空应用队列 - 使用更高效的方式
            图标状态.应用队列.length = 0;

            // 重置字体加载状态
            图标状态.字体已加载 = false;
            图标状态.Roboto字体已加载 = false;
            图标状态.RobotoBold字体已加载 = false;

            // 清理字体引用
            图标状态.FontAwesome字体 = null;
            图标状态.Roboto字体 = null;
            图标状态.RobotoBold字体 = null;

            console.log("图标管理器资源已清理");
        } catch (e) {
            console.error("清理图标管理器资源失败:", e);
        }
    }
};

// 导出模块
module.exports = {
    通用图标管理器: 通用图标管理器,
    
    // 便捷方法
    初始化图标系统: function(回调函数) {
        通用图标管理器.初始化(回调函数);
    },
    
    应用图标: function(控件ID, 图标分类, 图标名称, 选项) {
        通用图标管理器.应用图标(控件ID, 图标分类, 图标名称, 选项);
    },
    
    应用导航栏图标: function(导航配置) {
        通用图标管理器.应用导航栏图标(导航配置);
    },
    
    应用操作按钮图标: function(按钮配置) {
        通用图标管理器.应用操作按钮图标(按钮配置);
    },
    
    应用抽屉页面图标: function(抽屉配置) {
        通用图标管理器.应用抽屉页面图标(抽屉配置);
    },
    
    应用日志页面图标: function(日志配置) {
        通用图标管理器.应用日志页面图标(日志配置);
    },

    应用悬浮球图标: function(悬浮球配置) {
        通用图标管理器.应用悬浮球图标(悬浮球配置);
    },

    应用悬浮球控件图标: function(悬浮球窗口, 控件ID, 图标分类, 图标名称, 选项) {
        通用图标管理器.应用悬浮球控件图标(悬浮球窗口, 控件ID, 图标分类, 图标名称, 选项);
    },

    应用导航栏字体: function(标题控件ID列表) {
        通用图标管理器.应用导航栏字体(标题控件ID列表);
    },

    清理资源: function() {
        通用图标管理器.清理资源();
    }
};
