/**
 * 算数游戏新手教程模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

// 导入截图功能模块
var 截图模块 = require('../常用功能/截图.js');

function 算数首次教程() {
    try {
        // console.log("🎓 开始算数首次教程");

        // 延时2秒
        console.log("⏳ 延时2秒后开始算数首次教程...");
        sleep(2000);

        // 第一步：判断点击感兴趣按钮
        console.log("📝 第一步：判断点击感兴趣按钮");

        // 感兴趣按钮图片路径（相对于主脚本su_main.js位置）
        var 感兴趣按钮 = "../../assets/算数游戏/新手教程图片/感兴趣箭头按钮.png";
        console.log("🔍 图片路径: " + 感兴趣按钮);

        // 检查截图权限
        console.log("🔐 检查截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 截图权限申请失败");
            return false;
        }
        console.log("✅ 截图权限申请成功");

        // 等待权限生效
        sleep(2000);

        // 在指定屏幕范围内判断点击感兴趣按钮
        console.log("🔍 开始执行第一步，查找感兴趣按钮...");
        var 点击结果 = 截图模块.查找_图片(感兴趣按钮, 204, 630, 298, 216, 5, 1000, "判断点击", 3, 0.8);

        if (点击结果) {
            console.log("✅ 第一步完成：成功点击感兴趣按钮");
        } else {
            console.log("⚠️ 第一步未找到按钮：可能已经点击过了，继续执行第二步");
        }

        // 第二步：延迟4秒后判断点击答案按钮
        console.log("📝 第二步：延迟4秒后点击答案按钮");
        console.log("⏳ 延迟4秒...");
        sleep(4000);

        // 确保截图权限仍然有效
        console.log("🔐 第二步前检查截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 第二步截图权限申请失败，跳过第二步");
        } else {
            console.log("✅ 第二步截图权限确认成功");
            sleep(1000); // 等待权限生效

            var 答案按钮 = "../../assets/算数游戏/新手教程图片/点击答案.png";
            console.log("🔍 开始执行第二步，查找答案按钮...");
            var 答案结果 = 截图模块.查找_图片(答案按钮, 81, 776, 153, 88, 5, 1000, "判断点击", 1, 0.8);

            if (答案结果) {
                console.log("✅ 第二步完成：成功点击答案按钮");
            } else {
                console.log("⚠️ 第二步未找到按钮：可能已经完成教程了");
            }
        }

        console.log("🎉 新手教程流程执行完毕");
        return true;

    } catch (error) {
        // 注意：这里的错误处理是正常的容错机制，不是程序bug
        // 作用：防止图片文件缺失、权限问题等导致整个脚本崩溃
        // 效果：教程失败时程序会继续执行后续功能，而不是停止运行
        console.error("❌ 正常的容错机制，不是程序bug: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    算数首次教程: 算数首次教程
};