// 数字5和6闭合区域检测.js - 专门解决数字5和6识别混淆问题
// 基于AutoXjs内置OpenCV 3.4.3，通过分析数字轮廓特征来区分5和6
// 数字6：有明显的内部闭合区域（洞）
// 数字5：完全开放，没有闭合区域
// 参考：牙叔教程 - AutoXjs查找图片相似轮廓

// 初始化OpenCV
runtime.images.initOpenCvIfNeeded();

// 导入必要的OpenCV类
importClass(org.opencv.core.MatOfByte);
importClass(org.opencv.core.Scalar);
importClass(org.opencv.core.Point);
importClass(org.opencv.core.CvType);
importClass(java.util.List);
importClass(java.util.ArrayList);
importClass(org.opencv.imgproc.Imgproc);
importClass(org.opencv.core.Core);
importClass(org.opencv.core.Mat);
importClass(org.opencv.core.MatOfPoint);
importClass(org.opencv.core.Size);

/**
 * 数字5和6闭合区域检测函数
 * @param {Image} 数字图像 - 单个数字的图像
 * @param {string} 识别结果 - 模板匹配的识别结果（"5"或"6"）
 * @param {number} 相似度 - 模板匹配的相似度
 * @param {number} 自定义阈值 - 可选的自定义二值化阈值，默认表达式区80
 * @returns {Object} 验证结果 {是否通过: boolean, 置信度: number, 检测详情: string}
 */
function 数字5和6闭合区域检测(数字图像, 识别结果, 相似度, 自定义阈值) {
    try {
        // console.log("🔍 启动数字5和6闭合区域检测: " + 识别结果);

        // 1. 获取图像的Mat对象
        var 图像Mat = 数字图像.getMat();
        if (!图像Mat) {
            return {是否通过: true, 置信度: 相似度, 检测详情: "无法获取Mat对象，跳过验证"};
        }

        // 2. 转换为灰度图像
        var 灰度Mat = new Mat();
        if (图像Mat.channels() > 1) {
            Imgproc.cvtColor(图像Mat, 灰度Mat, Imgproc.COLOR_BGR2GRAY);
        } else {
            图像Mat.copyTo(灰度Mat);
        }

        // 3. 高斯模糊处理（减少噪声）
        var 模糊Mat = new Mat();
        Imgproc.GaussianBlur(灰度Mat, 模糊Mat, new Size(3, 3), 0);
        
        // 4. 二值化处理和轮廓检测（支持自定义阈值）
        var 使用阈值 = 自定义阈值 || 30; // 默认阈值30，表达式闭合阈值

        // 二值化处理
        var 二值Mat = new Mat();
        Imgproc.threshold(模糊Mat, 二值Mat, 使用阈值, 255, Imgproc.THRESH_BINARY);

        // 轮廓检测
        var 轮廓列表 = new ArrayList();
        var 层次结构 = new Mat();
        Imgproc.findContours(二值Mat, 轮廓列表, 层次结构, Imgproc.RETR_TREE, Imgproc.CHAIN_APPROX_SIMPLE);

        // 清理临时Mat
        二值Mat.release();
        层次结构.release();

        // 检查是否找到轮廓
        if (轮廓列表.size() === 0) {
            模糊Mat.release();
            灰度Mat.release();
            return {是否通过: true, 置信度: 相似度, 检测详情: "未检测到轮廓，跳过验证"};
        }

        // 分析轮廓特征
        var 轮廓分析结果 = 分析轮廓特征OpenCV(轮廓列表);

        // console.log("    🔍 阈值" + 使用阈值 + "检测结果: 总轮廓:" + 轮廓分析结果.总轮廓数量 +
        //            " 外轮廓:" + 轮廓分析结果.外轮廓数量 + " 内轮廓:" + 轮廓分析结果.内轮廓数量 +
        //            " 闭合:" + 轮廓分析结果.闭合区域数量);
        
        // 5. 根据识别结果进行验证
        var 验证结果 = 验证数字类型(识别结果, 轮廓分析结果, 相似度, 使用阈值);

        // 6. 清理资源
        模糊Mat.release();
        灰度Mat.release();

        // console.log("  📊 闭合区域检测结果: " + 验证结果.检测详情);
        return 验证结果;

    } catch (e) {
        console.error("❌ 闭合区域检测异常: " + e.toString());
        return {是否通过: true, 置信度: 相似度, 检测详情: "检测异常，跳过验证"};
    }
}

/**
 * 分析轮廓特征 - OpenCV版本
 * @param {ArrayList} 轮廓列表 - OpenCV findContours返回的轮廓列表
 * @returns {Object} 轮廓特征 {外轮廓数量: number, 内轮廓数量: number, 最大轮廓面积: number, 闭合区域数量: number}
 */
function 分析轮廓特征OpenCV(轮廓列表) {
    var 外轮廓数量 = 0;
    var 内轮廓数量 = 0;
    var 最大轮廓面积 = 0;
    var 闭合区域数量 = 0;
    var 总轮廓数量 = 轮廓列表.size();

    for (var i = 0; i < 总轮廓数量; i++) {
        var 轮廓 = 轮廓列表.get(i);

        // 计算轮廓面积
        var 面积 = Imgproc.contourArea(轮廓);
        if (面积 > 最大轮廓面积) {
            最大轮廓面积 = 面积;
        }

        // 判断轮廓类型 - 使用多级面积阈值
        if (面积 > 20) { // 过滤掉最小的噪声轮廓
            // 检查轮廓是否闭合（OpenCV中轮廓默认是闭合的）
            闭合区域数量++;

            // 根据面积大小判断是外轮廓还是内轮廓
            // 使用相对面积比例而不是绝对值
            if (最大轮廓面积 > 0) {
                var 面积比例 = 面积 / 最大轮廓面积;
                if (面积比例 > 0.3 || 面积 > 150) {
                    外轮廓数量++;
                } else if (面积 > 30) { // 中等面积的轮廓可能是内轮廓
                    内轮廓数量++;
                }
            } else {
                // 如果最大轮廓面积为0，使用绝对面积判断
                if (面积 > 150) {
                    外轮廓数量++;
                } else if (面积 > 30) {
                    内轮廓数量++;
                }
            }
        }
    }

    return {
        外轮廓数量: 外轮廓数量,
        内轮廓数量: 内轮廓数量,
        最大轮廓面积: 最大轮廓面积,
        闭合区域数量: 闭合区域数量,
        总轮廓数量: 总轮廓数量
    };
}

/**
 * 分析轮廓特征 - 原版本（已弃用）
 * @param {Array} 轮廓列表 - findContours返回的轮廓数组
 * @returns {Object} 轮廓特征 {外轮廓数量: number, 内轮廓数量: number, 最大轮廓面积: number, 闭合区域数量: number}
 */
function 分析轮廓特征(轮廓列表) {
    var 外轮廓数量 = 0;
    var 内轮廓数量 = 0;
    var 最大轮廓面积 = 0;
    var 闭合区域数量 = 0;
    
    for (var i = 0; i < 轮廓列表.length; i++) {
        var 轮廓 = 轮廓列表[i];
        
        // 计算轮廓面积
        var 面积 = images.contourArea(轮廓);
        if (面积 > 最大轮廓面积) {
            最大轮廓面积 = 面积;
        }
        
        // 判断轮廓类型 - 使用多级面积阈值
        if (面积 > 20) { // 过滤掉最小的噪声轮廓
            // 检查轮廓是否闭合
            var 是否闭合 = images.isContourClosed(轮廓);
            if (是否闭合) {
                闭合区域数量++;
            }

            // 根据面积大小判断是外轮廓还是内轮廓
            // 使用相对面积比例而不是绝对值
            var 面积比例 = 面积 / 最大轮廓面积;
            if (面积比例 > 0.3 || 面积 > 150) {
                外轮廓数量++;
            } else if (面积 > 30) { // 中等面积的轮廓可能是内轮廓
                内轮廓数量++;
            }
        }
    }
    
    return {
        外轮廓数量: 外轮廓数量,
        内轮廓数量: 内轮廓数量,
        最大轮廓面积: 最大轮廓面积,
        闭合区域数量: 闭合区域数量,
        总轮廓数量: 轮廓列表.length
    };
}

/**
 * 验证数字类型
 * @param {string} 识别结果 - "5"或"6"
 * @param {Object} 轮廓特征 - 轮廓分析结果
 * @param {number} 原始相似度 - 模板匹配的相似度
 * @param {number} 使用阈值 - 使用的二值化阈值
 * @returns {Object} 验证结果
 */
function 验证数字类型(识别结果, 轮廓特征, 原始相似度, 使用阈值) {
    var 是否通过 = true;
    var 置信度 = 原始相似度;
    var 检测详情 = "";
    
    if (识别结果 === "5") {
        // 数字5的标准特征：内轮廓绝对值等于0
        var 符合数字5特征 = 轮廓特征.内轮廓数量 === 0;

        if (!符合数字5特征) {
            // 内轮廓不等于0，可能是数字6被误识别为5
            是否通过 = false;
            置信度 = 原始相似度 * 0.6; // 降低置信度
            检测详情 = "数字5标准特征：内轮廓=0，当前内轮廓=" + 轮廓特征.内轮廓数量 + "，疑似误识别";
        } else {
            检测详情 = "数字5标准特征验证通过：内轮廓=" + 轮廓特征.内轮廓数量;
        }
    } else if (识别结果 === "6") {
        // 数字6的标准特征：内轮廓绝对等于1（一个开口，区分数字5/8/0）
        var 符合数字6特征 = 轮廓特征.内轮廓数量 === 1;

        if (!符合数字6特征) {
            // 内轮廓不等于1，可能是数字5/8/0被误识别为6
            是否通过 = false;
            置信度 = 原始相似度 * 0.6; // 降低置信度
            if (轮廓特征.内轮廓数量 === 0) {
                检测详情 = "数字6标准特征：内轮廓=1，当前内轮廓=" + 轮廓特征.内轮廓数量 + "，疑似数字5误识别";
            } else if (轮廓特征.内轮廓数量 > 1) {
                检测详情 = "数字6标准特征：内轮廓=1，当前内轮廓=" + 轮廓特征.内轮廓数量 + "，疑似数字8/0误识别";
            } else {
                检测详情 = "数字6标准特征：内轮廓=1，当前内轮廓=" + 轮廓特征.内轮廓数量 + "，疑似误识别";
            }
        } else {
            检测详情 = "数字6标准特征验证通过：内轮廓=" + 轮廓特征.内轮廓数量 + "（一个开口）";
        }
    } else {
        // 其他数字不需要验证
        检测详情 = "非5/6数字，跳过验证";
    }
    
    return {
        是否通过: 是否通过,
        置信度: 置信度,
        检测详情: 检测详情,
        轮廓信息: "外:" + 轮廓特征.外轮廓数量 + ",内:" + 轮廓特征.内轮廓数量
    };
}

/**
 * 快速检测函数 - 专门用于数字5和6的快速验证
 * @param {Image} 数字图像 - 单个数字的图像
 * @param {string} 数字值 - 实际数字值（"5"或"6"）
 * @returns {boolean} 是否为正确的数字类型
 */
function 快速检测数字5和6(数字图像, 数字值) {
    if (数字值 !== "5" && 数字值 !== "6") {
        return true; // 非5/6数字直接通过
    }
    
    var 检测结果 = 数字5和6闭合区域检测(数字图像, 数字值, 1.0);
    return 检测结果.是否通过;
}

// 导出函数
module.exports = {
    数字5和6闭合区域检测: 数字5和6闭合区域检测,
    快速检测数字5和6: 快速检测数字5和6
};
