第一次：识别数字24的测试结果：
03:24:13.187/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/ocr/7.js]
03:24:13.435/D: ✅ 截图权限申请成功
03:24:15.438/D: 🎯 开始Tesseract OCR数字识别 v6
03:24:15.439/D: 📦 Tesseract OCR实例创建成功
03:24:15.439/D: 📁 数据目录路径: /storage/emulated/0/脚本/magic/脚本/ocr/
03:24:15.440/D: 📂 期望tessdata子目录: /storage/emulated/0/脚本/magic/脚本/ocr/tessdata/
03:24:15.441/D: 🔧 正在初始化Tesseract OCR...
03:24:15.751/D: ✅ Tesseract OCR初始化成功
03:24:15.751/D: ⚙️ 设置OCR识别优化参数
03:24:15.752/D: ✅ OCR识别优化配置完成
03:24:15.753/D: 📸 截图并裁剪红色区域 (288,775,170,105)
03:24:15.754/D: ✅ 裁剪完成，区域尺寸: 170x105
03:24:15.761/D: 🔧 新方案: 多重图像优化 - 解决数字3和5识别混淆问题
03:24:21.452/D: 
📊 按置信度排序的识别结果（置信度从低到高）:
03:24:21.452/D:    1. 阈值36: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.454/D:    2. 阈值64: 原始[21] 纯数字[21] 字符数[2] 置信度[0.0%]
03:24:21.455/D:    3. 阈值63: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.455/D:    4. 阈值62: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.456/D:    5. 阈值61: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.456/D:    6. 阈值60: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.457/D:    7. 阈值58: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.457/D:    8. 阈值57: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.458/D:    9. 阈值56: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.458/D:    10. 阈值55: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.460/D:    11. 阈值54: 原始[2] 纯数字[2] 字符数[1] 置信度[0.0%]
03:24:21.460/D:    12. 阈值53: 原始[2] 纯数字[2] 字符数[1] 置信度[0.0%]
03:24:21.460/D:    13. 阈值31: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.461/D:    14. 阈值30: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.462/D:    15. 阈值32: 原始[24] 纯数字[24] 字符数[2] 置信度[0.0%]
03:24:21.462/D:    16. 阈值39: 原始[24] 纯数字[24] 字符数[2] 置信度[2.0%]
03:24:21.463/D:    17. 阈值37: 原始[24] 纯数字[24] 字符数[2] 置信度[6.0%]
03:24:21.463/D:    18. 阈值33: 原始[24] 纯数字[24] 字符数[2] 置信度[8.0%]
03:24:21.466/D:    19. 阈值35: 原始[24] 纯数字[24] 字符数[2] 置信度[8.0%]
03:24:21.466/D:    20. 阈值34: 原始[24] 纯数字[24] 字符数[2] 置信度[8.0%]
03:24:21.467/D:    21. 阈值38: 原始[24] 纯数字[24] 字符数[2] 置信度[11.0%]
03:24:21.467/D:    22. 阈值40: 原始[24] 纯数字[24] 字符数[2] 置信度[14.0%]
03:24:21.468/D:    23. 阈值42: 原始[24] 纯数字[24] 字符数[2] 置信度[14.0%]
03:24:21.468/D:    24. 阈值49: 原始[7 24 4] 纯数字[7244] 字符数[4] 置信度[24.0%]
03:24:21.469/D:    25. 阈值43: 原始[24] 纯数字[24] 字符数[2] 置信度[25.0%]
03:24:21.469/D:    26. 阈值29: 原始[24] 纯数字[24] 字符数[2] 置信度[27.0%]
03:24:21.470/D:    27. 阈值44: 原始[24] 纯数字[24] 字符数[2] 置信度[31.0%]
03:24:21.471/D:    28. 阈值93: 原始[4] 纯数字[4] 字符数[1] 置信度[32.0%]
03:24:21.471/D:    29. 阈值92: 原始[4] 纯数字[4] 字符数[1] 置信度[32.0%]
03:24:21.472/D:    30. 阈值48: 原始[7 24 4] 纯数字[7244] 字符数[4] 置信度[34.0%]
03:24:21.472/D:    31. 阈值46: 原始[4 24 4] 纯数字[4244] 字符数[4] 置信度[37.0%]
03:24:21.473/D:    32. 阈值50: 原始[7 24 4] 纯数字[7244] 字符数[4] 置信度[38.0%]
03:24:21.473/D:    33. 阈值47: 原始[7 24 4] 纯数字[7244] 字符数[4] 置信度[38.0%]
03:24:21.474/D:    34. 阈值12: 原始[24] 纯数字[24] 字符数[2] 置信度[40.0%]
03:24:21.474/D:    35. 阈值41: 原始[24] 纯数字[24] 字符数[2] 置信度[42.0%]
03:24:21.475/D:    36. 阈值59: 原始[3] 纯数字[3] 字符数[1] 置信度[44.0%]
03:24:21.476/D:    37. 阈值18: 原始[24] 纯数字[24] 字符数[2] 置信度[46.0%]
03:24:21.476/D:    38. 阈值1: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.477/D:    39. 阈值9: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.477/D:    40. 阈值2: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.478/D:    41. 阈值3: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.479/D:    42. 阈值4: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.479/D:    43. 阈值5: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.480/D:    44. 阈值6: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.480/D:    45. 阈值45: 原始[7 24] 纯数字[724] 字符数[3] 置信度[48.0%]
03:24:21.481/D:    46. 阈值7: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.481/D:    47. 阈值8: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.482/D:    48. 阈值11: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.482/D:    49. 阈值10: 原始[24] 纯数字[24] 字符数[2] 置信度[48.0%]
03:24:21.483/D:    50. 阈值51: 原始[24] 纯数字[24] 字符数[2] 置信度[51.0%]
03:24:21.483/D:    51. 阈值13: 原始[24] 纯数字[24] 字符数[2] 置信度[67.0%]
03:24:21.484/D:    52. 阈值24: 原始[24] 纯数字[24] 字符数[2] 置信度[77.0%]
03:24:21.484/D:    53. 阈值26: 原始[24] 纯数字[24] 字符数[2] 置信度[78.0%]
03:24:21.485/D:    54. 阈值25: 原始[24] 纯数字[24] 字符数[2] 置信度[78.0%]
03:24:21.485/D:    55. 阈值19: 原始[24] 纯数字[24] 字符数[2] 置信度[82.0%]
03:24:21.486/D:    56. 阈值23: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.487/D:    57. 阈值22: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.487/D:    58. 阈值21: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.488/D:    59. 阈值14: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.488/D:    60. 阈值20: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.489/D:    61. 阈值27: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.489/D:    62. 阈值28: 原始[24] 纯数字[24] 字符数[2] 置信度[83.0%]
03:24:21.490/D:    63. 阈值17: 原始[24] 纯数字[24] 字符数[2] 置信度[84.0%]
03:24:21.490/D:    64. 阈值15: 原始[24] 纯数字[24] 字符数[2] 置信度[85.0%]
03:24:21.490/D:    65. 阈值16: 原始[24] 纯数字[24] 字符数[2] 置信度[85.0%]
03:24:21.491/D: 
🏆 最佳黑色提取结果:
03:24:21.495/D:    最佳阈值: 15
03:24:21.497/D:    原始文本: "24"
03:24:21.498/D:    纯数字: "24"
03:24:21.499/D:    数字字符数: 2
03:24:21.500/D:    置信度: 85.0%
03:24:21.501/D: 
🎯 最终识别结果: "24"
03:24:21.503/D: 📍 识别区域坐标信息:
03:24:21.503/D:    区域左上角: (288, 775)
03:24:21.504/D:    区域宽高: 170 x 105
03:24:21.505/D:    区域右下角: (458, 880)
03:24:21.506/D:    区域中心点: (373, 827.5)
03:24:21.506/D:    识别到的数字: "24" 位于红色区域
03:24:21.507/D: 🧹 图片资源已释放
03:24:21.508/D: 
📋 Tesseract OCR通用数字识别完成
03:24:21.508/D: 🎯 直接在红色区域(288,775,170,105)进行OCR识别
03:24:21.509/D: 🚀 遍历阈值1-100，多重图像优化+像素分布分析识别所有数字0-9
03:24:21.510/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/ocr/7.js ]运行结束，用时8.322000秒
