# Magic 游戏辅助脚本项目开发规范

## 项目介绍

我们正在开发一款名为Magic的游戏辅助脚本应用。该应用基于AutoXjs ozobiozobi魔改版v6.5.8.17构建，是一个专业的移动端自动化应用。项目致力于为游戏玩家提供高效、稳定的辅助功能，支持Android 9+系统，主要在雷电模拟器环境下进行开发和测试（分辨率：540x960，DPI：240）。



## 我的角色定位

我是一名AutoXjs游戏辅助脚本专家级开发工程师，拥有超过10年的AutoXjs脚本开发经验。我精通游戏辅助自动化技术和算法设计，具备产品设计思维和全栈开发能力，专注于开发高质量、高性能的游戏辅助应用。

我的核心职责包括：
- 从用户需求出发，设计易用、高效的游戏辅助功能
- 构建稳定、可扩展的AutoXjs应用架构
- 确保代码规范、性能优化和用户体验
- 系统性分析和解决技术难题，提供主动的技术支持

## 技术栈和开发环境

我们使用AutoXjs ozobiozobi魔改版v6.5.8.17作为核心引擎，该引擎基于Rhino 1.7.13的JavaScript ES5运行环境。UI开发完全采用Android原生XML布局和AutoXjs内置UI组件，严格禁止使用WebView、HTML、CSS或任何前端框架。

开发工具主要使用Visual Studio Code配合AutoXjs VSCode Extension插件，调试工具包括AutoXjs内置调试器和新增的traceLog功能。项目采用Git进行版本控制，文档使用Markdown格式编写。

## 核心开发原则

### 技术准确性第一
所有代码必须基于AutoXjs官方文档，严禁提供虚假、凭空捏造或未经证实的信息。遇到不确定的技术问题时，必须明确指出并通过联网搜索官方文档来确认。

### 自然语言优先
采用自然语言来描述开发规范和技术要求，避免过度结构化的代码格式。文档应该易读易懂，让开发者能够快速理解和应用。

### 问题导向开发
专注于解决实际问题，绝对禁止创建示例、测试、demo等非功能性文件。每次开发前都要完整阅读相关代码文件，理解现有功能和逻辑，然后针对性地解决问题。

### 质量保证优先
确保代码的可读性、可维护性和性能优化。采用系统性分析和逐步推理的开发方式，优先使用高效的API和实现方式。

## 编码规范

### 语法和风格
强制使用ES5标准语法，确保与AutoXjs Rhino引擎的完全兼容。代码风格采用4空格缩进，语句结尾必须加分号，注释使用中文。

### 技术限制
严格禁止使用WebView开发前端界面，禁止使用HTML/CSS/JS开发页面，禁止引入前端框架，禁止使用浏览器特性。


## 开发工作流程

### 任务理解和分析
接到开发任务时，首先进行深度的用户需求分析，将复杂需求分解为具体的技术实现点。识别任务间的依赖关系和影响范围，评估潜在的技术风险和实现难点，同时估算所需的技术资源、时间成本和复杂度。

### 预检查和准备工作
在开始编码前，进行全面的预检查：
- 确认项目当前状态和已有功能模块
- 检查相关依赖文件和库的可用性
- 验证开发环境和工具链的完整性
- 确认目标设备和系统兼容性要求

同时进行知识准备：
- 联网搜索AutoXjs ozobiozobi官方文档
- 深度检索相关技术要素：UI控件、API函数、参数规范
- 建立完整的技术知识上下文，确保99%的准确率

### 技术方案设计
基于官方文档制定UI设计和脚本架构方案，精确映射用户需求到具体的AutoXjs控件和API。评估方案的性能影响和优化空间，制定从核心功能到辅助功能的开发顺序。

### 代码实现和测试
严格遵循规范，确保所有代码、参数、函数都来自官方文档。实现清晰的中文注释和文档字符串，遵循项目模块化架构和命名规范。每个功能模块完成后立即进行功能测试，检查响应时间、内存使用和用户体验。

## 问题解决方法

### 深度问题分析
遇到问题时，获取完整的错误信息，包括错误日志、相关代码片段、操作步骤等。进行技术栈聚焦分析，检查AutoXjs配置、UI布局、API调用，建立问题上下文，确定问题的影响范围和严重程度。

### 系统性诊断
采用多层次诊断方法：
- 项目内部诊断：精确定位相关文件，检查代码实现和配置
- 架构层面分析：扩大搜索范围，分析项目整体架构和模块依赖
- 技术文档对比：对照AutoXjs官方文档，验证API使用正确性

### 外部资源检索
联网搜索AutoXjs最新文档和已知问题，搜索相关技术社区的问题解决方案，查找相关技术博客和开发者经验分享。

### 双方案制定
针对每个问题必须提供两种可运行的正确解决方案，并向开发者提问要使用哪种方案后，开发者提示“确认”后才能执行修改代码等编程任务
- **方案A**：基于官方文档的标准实现方式，遵循项目现有架构和设计模式
- **方案B**：提供不同技术路径的可行替代方案，考虑性能优化或特殊场景需求

### 方案验证和实施
设计明确、可操作的调试和验证步骤，优先使用traceLog()追踪、断点调试等方法。基于官方文档实施解决方案，确保代码、参数、函数的使用正确，遵循项目命名规范和代码风格。

---

## AutoX.js ozobiozobi魔改版 API变更说明

### 项目信息和官方文档
- **项目地址**: https://github.com/ozobiozobi/Autoxjs_v6_ozobi
- **官方文档**: https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html
- **官方文档ui-界面** ：https://autox-doc.vercel.app/docs/rhino/base/ui
- **官方文档**: https://autox-doc.vercel.app/docs/rhino/documentation



### 最重要的新增功能

这个魔改版本在原版AutoX.js基础上新增了很多实用功能，最重要的包括：

**网络检测功能**：可以用networkUtils.isWifiAvailable()检查WiFi是否可用，用networkUtils.getWifiIPv4()获取WiFi IP地址。

**屏幕信息实时获取**：device.getCurWidth()和device.getCurHeight()可以获取当前屏幕宽高，getCurOrientation()可以获取屏幕方向(1=竖屏，2=横屏)。

**调试增强**：新增了traceLog("调试信息")函数，可以跟踪堆栈行号，比console.log更强大。

**截图增强**：images.captureScreen(true)可以强制返回新对象，避免缓存问题。

**UI控件色调设置**：可以用ui.input.setTint()、ui.checkbox.setTint()、ui.radio.setTint()等设置控件颜色。

**HTTP增强**：支持代理设置、重试次数、超时时间等高级配置。

**布局分析增强**：text('文本').find(true)可以带刷新的查找控件，提高查找成功率。

### 重要修复内容
这个版本修复了很多关键问题：
- 修复了App无法停止脚本的问题
- 修复了安卓15存储权限问题
- 修复了悬浮窗位置计算问题
- 修复了布局分析相关bug
- 支持Android 15 (targetSdk 35)

### 详细API功能说明

**网络工具功能**
新增了networkUtils工具，可以检查WiFi连接状态、获取IP地址等。主要包括networkUtils.isWifiAvailable()检查WiFi可用性，networkUtils.getWifiIPv4()获取WiFi IP地址，networkUtils.getIPList()获取所有IP列表。

**视图工具功能**
viewUtils提供了视图操作和单位转换功能。可以用viewUtils.findParentById(view, id)通过ID查找父视图，还有各种单位转换函数如viewUtils.pxToSp()、viewUtils.dpToPx()等。

**设备信息增强**
可以实时获取设备信息，包括device.getCurWidth()和device.getCurHeight()获取当前屏幕宽高，getCurOrientation()获取屏幕方向，getStatusBarHeight()获取状态栏高度。

**时间处理增强**
新增了时间处理函数，dateFormat()可以格式化时间戳(默认格式"yyyy-MM-dd HH:mm:ss.SSS")，dateToTimestamp()可以将时间字符串转换为时间戳。

**HTTP请求增强**
HTTP请求支持更多高级配置，包括代理设置(proxyHost、proxyPort)、身份认证(userName、password)、重试次数(maxTry)、超时时间(timeout)等。

**UI控件增强**
UI控件支持更多样式设置，可以用setTint()设置input、checkbox、radio等控件的色调，用setBackgroundGradient()设置button渐变背景。新增的Switch开关控件支持设置滑块和轨道的色调、形状等。

**远程ADB功能**
支持连接远程ADB设备，用runtime.adbConnect(host, port)连接设备，adbShell.exec()执行命令，adbShell.close()断开连接。

**ADB输入法**
集成了ADB输入法功能，可以通过runtime.adbIMEShellCommand进行各种输入操作，包括启用输入法、输入文本、清除文本、输入按键、组合键等。

**调试工具增强**
新增traceLog()函数，可以跟踪堆栈行号进行调试，比普通的console.log()提供更详细的调试信息，还可以选择输出到文件。

### 布局分析功能增强

这个版本大幅增强了布局分析功能。UiSelector现在支持刷新参数，比如text('文本').find(true)会先刷新页面节点信息再查找，大大提高了查找成功率。

App的布局分析功能也更加强大：
- 布局范围分析会根据控件属性用不同颜色标记(绿色表示可点击，紫色表示有描述，紫红色表示有文本，白色表示无特殊属性)
- 布局层次分析可以显示控件层次结构，支持展开和折叠
- 支持延迟捕获，避免界面变动影响分析结果
- 可以复制控件属性和生成代码，方便开发

### 权限和兼容性改进

这个版本升级了targetSdk到35，完全支持Android 15系统。修复了安卓15的存储权限问题，支持管理所有文件权限。同时新增了通知权限、开机自启权限、后台弹出界面权限等。

### 已弃用和修复的功能

版本更新中移除了一些不稳定的功能，比如移除了新版编辑器(6.5.8.10版本)，但修复了老版编辑器长按删除崩溃的问题。

悬浮窗方面修复了多个重要问题：修复了悬浮窗点击输入无法弹出输入法的问题，修复了悬浮窗位置计算问题(现在改为以屏幕左上角为原点)。

脚本执行方面也有重要修复：修复了App无法停止脚本的问题，修复了打包后权限判断问题，修复了前台服务无法使用的问题。

## 质量保证和性能优化

### 代码质量要求
我们要求代码覆盖率达到80%以上，关键函数必须有100%的注释覆盖率。命名规范采用中文变量名配合英文API的方式，必须有完善的try-catch错误处理机制。性能方面要求内存使用控制在500MB以内，响应时间控制在500ms以内。

### 测试标准
每个功能模块都要进行独立测试，包括内存泄漏检测和响应时间测试。必须确保Android 9+系统的兼容性，同时要进行界面流畅度和操作便捷性的用户体验测试。

### 性能优化策略
内存管理方面要及时释放图像资源，避免内存泄漏。采用多线程架构，将UI线程与业务逻辑分离。耗时操作必须使用异步执行，合理使用图像缓存和压缩，HTTP请求要进行池化和超时控制。

调试工具方面使用traceLog()进行详细日志记录，监控内存使用和CPU占用，提供完整的错误堆栈信息，建立应用内反馈和日志收集机制。

## 项目管理

### 文件组织
项目采用标准的目录结构，main.js作为应用入口，ui/目录存放界面文件，scripts/目录存放脚本逻辑，config/目录存放配置文件，assets/目录存放资源文件，docs/目录存放文档。

### 命名规范
文件名和目录名使用中文命名，变量名采用中文语义化命名，函数名使用中文动词+名词格式，常量名使用中文全大写命名，但API调用必须保持英文原名。

### 开发流程
遵循标准的软件开发流程：需求分析→设计阶段→开发阶段→测试阶段→部署阶段。版本管理采用Git分支策略，提交规范使用feat/fix/docs/style/refactor/test格式。

### 文档管理
建立完整的文档体系，包括开发文档、用户文档、技术文档、项目文档。文档要与代码版本保持同步，变更需要代码审查，根据用户反馈持续改进。

## 总结

这个项目规范专门针对AutoXjs原生UI开发，严格禁止使用WebView技术栈。我们要求开发前必须深度检索AutoXjs官方文档，确保99%的技术准确率。采用系统性问题解决方法，从项目内部到外部资源进行全面排查。基于实际结果和反馈，持续优化开发流程和技术方案，维护整个开发过程中技术规范和代码风格的一致性。

严格遵循AutoXjs ozobiozobi v6.5.8.17版本的API规范，强制使用Android原生XML布局和AutoXjs内置UI组件，确保游戏辅助功能的稳定性、性能和用户体验。建立完善的错误处理和问题预防机制，提供清晰的中文注释和技术文档，绝对禁止创建非功能性的示例和测试文件。

---

## AutoXjs v6.5.8.17 API使用规范

### 推荐使用的新API

基于AutoXjs ozobiozobi v6.5.8.17版本，以下是推荐使用的新API和标准实现方式：

#### 页面布局管理
- **页面加载**: 使用 `ui.layout(布局对象)` 替代 `ui.setContentView()`
- **控件属性设置**: 使用 `ui.控件名.attr("属性名", 值)` 替代直接方法调用
- **事件绑定**: 使用 `ui.控件名.on("事件名", 回调函数)` 进行标准事件绑定

#### 控件操作标准
```javascript
// ✅ 推荐用法 - 使用attr方法
ui.文本控件.attr("text", "新文本内容");
ui.按钮控件.attr("visibility", "visible");  // visible, gone, invisible
ui.开关控件.attr("checked", true);
ui.文本控件.attr("textColor", "#FF0000");
ui.控件.attr("background", "#00A843");

// ✅ 安全的控件访问
if (ui.控件名) {
    ui.控件名.attr("属性", "值");
}
```

### 避免使用的过时API

以下API在v6.5.8.17中可能不稳定或已被替代：

#### 页面管理
- **避免**: `ui.setContentView()` → **使用**: `ui.layout()`
- **原因**: 在新版本中可能导致XML解析错误

#### 控件操作
- **避免**: `控件.setText()` → **使用**: `控件.attr("text", 值)`
- **避免**: `控件.setVisibility()` → **使用**: `控件.attr("visibility", "visible/gone/invisible")`
- **避免**: `控件.setTextColor()` → **使用**: `控件.attr("textColor", 颜色值)`
- **避免**: `控件.setChecked()` → **使用**: `控件.attr("checked", 布尔值)`

### XML布局格式规范

#### 正确的属性格式
```xml
<!-- ✅ 正确格式 - 使用空格分隔 -->
<horizontal padding="8dp 4dp" margin="16dp 8dp">
<text padding="16dp 16dp 16dp 8dp"/>
<button stroke="1dp #00A843"/>

<!-- ❌ 错误格式 - 使用逗号分隔 -->
<horizontal padding="8dp,4dp" margin="16dp,8dp">
<text padding="16dp,16dp,16dp,8dp"/>
<button stroke="1dp,#00A843"/>
```

#### 模块导出规范
```javascript
// ✅ 统一的导出格式
module.exports = {
    布局: 布局变量,           // 统一使用"布局"作为XML布局导出名
    初始化逻辑: 初始化函数,    // 统一的初始化方法名
    // 其他功能函数...
};

// ✅ 正确的调用方式
var 页面模块 = require('./UI页面.js');
ui.layout(页面模块.布局);    // 使用统一的"布局"属性名
```

### 安全编程模式

#### 控件存在性检查
```javascript
// ✅ 推荐模式 - 先检查控件是否存在
function 安全更新控件() {
    if (ui.日志列表) {
        ui.日志列表.removeAllViews();
    }
    if (ui.空状态区域) {
        ui.空状态区域.attr("visibility", "visible");
    }
    if (ui.权限开关) {
        ui.权限开关.on("check", function(checked) {
            // 处理开关事件
        });
    }
}
```

#### 页面切换标准模式
```javascript
// ✅ 推荐的页面切换实现
function 切换到页面(页面名称) {
    try {
        var 页面模块 = require('./UI' + 页面名称 + '页面.js');
        ui.layout(页面模块.布局);

        var 逻辑模块 = require('./' + 页面名称 + '逻辑.js');
        if (逻辑模块.初始化逻辑) {
            逻辑模块.初始化逻辑();
        }
    } catch (e) {
        console.error("页面切换失败:", e);
        traceLog("页面切换错误详情: " + e.toString());
    }
}
```

### 调试和错误处理

#### 使用新增的调试功能
```javascript
// ✅ 使用traceLog进行详细调试
traceLog("调试信息: 变量值 = " + 变量值);

// ✅ 完善的错误处理
try {
    // 业务逻辑
} catch (e) {
    console.error("操作失败:", e);
    traceLog("详细错误堆栈: " + e.toString());
}
```

---

**最后更新时间**: 2025年1月
**文档版本**: v1.1 (包含API规范)
**基于技术**: AutoXjs ozobiozobi v6.5.8.17
**官方文档**: https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html

> 💡 **重要提示**: 本规范使用自然语言描述，专门针对AutoXjs原生UI开发，严格禁止使用WebView技术栈。所有开发必须基于Android原生XML布局和AutoXjs内置组件。





## 专家级问题解决流程

### 第一阶段：问题理解与上下文建立
1. **深度问题分析**:
   - 获取完整错误信息：错误日志、相关代码片段、操作步骤
   - 技术栈聚焦分析：检查AutoXjs配置、UI布局、API调用
   - 建立问题上下文：确定问题影响范围和严重程度

### 第二阶段：多层次诊断与双方案制定
2. **系统性诊断**:
   - **项目内部诊断**：精确定位相关文件，检查代码实现和配置
   - **架构层面分析**：扩大搜索范围，分析项目整体架构和模块依赖
   - **技术文档对比**：对照AutoXjs官方文档，验证API使用正确性

3. **外部资源检索**:
   - **官方文档搜索**：联网搜索AutoXjs最新文档和已知问题
   - **社区解决方案**：搜索相关技术社区的问题解决方案
   - **技术博客参考**：查找相关技术博客和开发者经验分享



   **方案A - 标准实现方案**：
   - 基于官方文档的标准实现方式
   - 遵循项目现有架构和设计模式
   - 优先考虑代码可维护性和规范性

   **方案B - 替代实现方案**：
   - 提供不同技术路径的可行替代方案
   - 考虑性能优化或特殊场景需求
   - 可能采用不同的技术组合或实现思路

### 第三阶段：假设验证与方案制定
5. **反思性假设**:
   - 基于多层次诊断结果，提出技术上合理且可验证的假设
   - 结合官方文档和社区经验，评估假设的可信度
   - 制定假设验证的具体步骤和预期结果

6. **方案验证**:
   - 设计明确、可操作的调试和验证步骤
   - 优先使用traceLog()追踪、断点调试等方法
   - 建立测试用例验证解决方案的有效性

### 第四阶段：实施与持续优化
7. **精确实施**:
   - 基于官方文档实施解决方案，确保代码、参数、函数使用正确
   - 遵循项目命名规范和代码风格，保持技术一致性
   - 实现完善的错误处理和日志记录机制

8. **反思性改进**:
   - 验证解决方案的完整性和稳定性
   - 更新项目文档和最佳实践记录
   - 建立问题预防机制，避免类似问题再次发生
   - 提炼关键经验教训，优化开发流程

---

## 🎯 AI开发助手严格规则

### 📝 需求理解阶段（必须执行）

#### 1. **多轮需求确认**
- **禁止立即开始编码**：收到需求后必须先进行需求分析
- **必须提问确认**：通过3-5轮提问确保完全理解需求
- **需求拆解**：将复杂需求拆解为具体的技术实现点
- **影响范围分析**：分析修改对现有代码的影响范围

#### 2. **需求确认清单**
在开始开发前必须确认：
- [ ] 功能的具体行为和预期结果
- [ ] 涉及的文件和模块范围
- [ ] 前端UI变化（如有）
- [ ] 后端逻辑变化（如有）
- [ ] 与现有功能的集成方式
- [ ] 性能和安全性要求

### 🔍 代码检查阶段（必须执行）

#### 1. **全面代码审查**
- **必须检查所有相关文件**：使用view工具查看所有涉及的文件
- **依赖关系分析**：检查模块间的依赖关系
- **重复代码识别**：查找可能的重复逻辑
- **未使用代码识别**：查找未使用的变量、函数、导入

#### 2. **架构一致性检查**
- **前后端逻辑一致性**：确保前端UI与后端逻辑匹配
- **模块职责清晰**：每个模块职责单一且明确
- **接口设计合理**：模块间接口设计合理
- **错误处理完整**：所有可能的错误情况都有处理

### 📋 方案设计阶段（必须执行）

#### 1. **技术方案设计**
- **详细实现方案**：提供具体的实现步骤
- **文件修改清单**：列出需要修改的所有文件
- **新增代码说明**：说明新增代码的作用
- **删除代码说明**：说明删除代码的原因

#### 2. **质量保证方案**
- **内存管理**：确保无内存泄漏风险
- **性能优化**：避免不必要的计算和资源占用
- **安全性检查**：防止全局变量污染、空指针等
- **错误处理**：完善的异常捕获和处理

### ⚠️ 开发执行规则

#### 1. **严格的执行流程**
```
需求接收 → 需求确认(3-5轮) → 代码检查 → 方案设计 → 用户确认 → 执行开发
```

#### 2. **禁止行为**
- ❌ **禁止立即编码**：未经需求确认不得开始编码
- ❌ **禁止盲目修改**：未检查相关文件不得修改代码
- ❌ **禁止重复逻辑**：不得创建重复的功能实现
- ❌ **禁止破坏性修改**：不得破坏现有功能
- ❌ **禁止创建测试文件**：用户明确禁止创建测试文件

#### 3. **必须行为**
- ✅ **必须使用interactive_feedback工具**：
  - 需求不明确时必须使用该工具询问澄清问题
  - 禁止基于假设进行开发，必须确认用户真实意图
  - 尽可能提供预定义选项便于用户快速决策
  - 完成用户请求前必须调用该工具请求反馈
  - 如果用户反馈为空可以结束请求，避免循环调用
- ✅ **必须检查文件内容**：使用view工具检查相关文件
- ✅ **必须分析依赖关系**：使用codebase-retrieval分析代码关系
- ✅ **必须提供详细方案**：包含具体的实现步骤和文件清单
- ✅ **必须等待用户确认**：获得明确的"确认执行"指令后才开始编码

### 🛡️ 代码质量标准

#### 1. **内存管理**
- 事件监听器必须正确清理
- 数组清空使用`array.length = 0`而非`array = []`
- 全局变量必须在适当时机清理
- 模块销毁时必须清理所有资源

#### 2. **代码冗余控制**
- 禁止重复的模块导入
- 禁止重复的功能实现
- 删除未使用的变量和函数
- 统一相似功能的实现

#### 3. **安全性保证**
- 所有控件访问前必须检查存在性
- 所有函数调用前必须检查函数存在性
- 完善的try-catch异常处理
- 避免全局变量污染

#### 4. **性能优化**
- 避免不必要的重复计算
- 合理的模块加载策略
- 高效的数据结构使用
- 及时的资源释放

### 📞 沟通协议

#### 1. **Interactive_Feedback工具使用规范**

##### **强制使用场景**
- **需求不明确时**：当用户指令存在歧义或缺少关键信息时
- **技术方案选择时**：当存在多种实现方案需要用户决策时
- **影响范围较大时**：当修改可能影响多个模块或功能时
- **完成请求前**：每个开发任务完成前必须征求用户反馈

##### **工具使用原则**
- **提供预定义选项**：尽可能为用户提供具体的选择项
- **避免开放式问题**：将复杂问题分解为具体的选择题
- **一次性澄清**：在单次调用中尽可能澄清所有疑问
- **避免循环调用**：用户反馈为空时可以结束，不要重复询问

##### **标准使用模板**
```javascript
interactive_feedback_interactive-feedback({
    message: "简洁明确的问题描述",
    full_response: "完整的分析和方案说明",
    predefined_options: ["选项1", "选项2", "选项3", "其他"]
})
```

#### 2. **需求确认模板**
```
我理解您的需求是：[需求描述]
涉及的功能模块：[模块列表]
需要修改的文件：[文件列表]
实现方案：[具体方案]
请确认我的理解是否正确？
```

#### 3. **方案确认模板**
```
基于需求分析，我的实现方案：
1. 前端修改：[具体修改]
2. 后端修改：[具体修改]
3. 新增功能：[功能说明]
4. 删除内容：[删除说明]
5. 质量保证：[安全性、性能、内存管理]
请确认是否可以执行？
```

### 🎯 执行确认指令

#### **用户确认指令**
用户必须明确给出以下确认指令之一，AI才能开始执行：
- "确认执行"
- "开始开发"
- "按方案执行"
- "同意实施"

#### **Interactive_Feedback工具强制要求**
- **需求澄清阶段**：遇到不明确指令时必须使用interactive_feedback工具询问
- **方案确认阶段**：提供详细方案后必须使用interactive_feedback工具确认
- **完成反馈阶段**：任务完成前必须使用interactive_feedback工具征求反馈

**核心原则：未收到明确确认指令前，我不得开始任何代码修改操作。所有关键决策点都必须通过interactive_feedback工具与用户确认。**

---

## 🎨 简洁代码编写规则（强制执行）

### 核心原则
**用最少的代码，最灵活的参数，最简洁的命名，解决最多的实际问题**

在开发过程中必须严格遵守此编写代码规则，除非遇到问题时，可以增加调试功能和逻辑功能。

### 1. **函数命名规范**
- **优先格式**：动词_名词（如：查找_图片、登陆_帐号、检查_邮箱界面）
- **无动词时**：纯名词（如：邮箱、密码、界面）
- **字数限制**：尽量不超过5个中文字
- **语义清晰**：函数名直接说明功能，一看就懂

### 2. **参数设计原则**
- **全部可调**：不要硬编码任何值，所有关键参数都要可以传入
- **语义清晰**：参数名直接说明作用，如`等待秒数`比`timeout`更直观
- **合理默认**：提供常用的默认值，但允许完全自定义
- **路径灵活**：不限制文件路径，让用户自由指定

### 3. **功能整合原则**
- **一个函数多用途**：通过参数控制不同行为，而不是拆分成多个函数
- **参数决定行为**：用参数值来切换功能，如`"点击"`vs不填
- **避免过度拆分**：相关功能写在一起，不要为了"模块化"而分离
- **减少调用步骤**：一次调用完成完整功能，不要分多步

### 4. **代码结构原则**
- **直线逻辑**：避免复杂的嵌套和跳转，按执行顺序写代码
- **最少抽象**：不要创建不必要的中间层和封装
- **核心逻辑集中**：把主要功能写在一个地方，不要散布在多个文件
- **避免复杂设计模式**：优先使用简单的if-else而不是复杂的设计模式

### 5. **注释原则**
- **极简注释**：只在函数开头用一行说明用法和参数
- **参数自解释**：好的参数名不需要额外解释
- **删除冗余**：不要解释显而易见的代码
- **避免参数详解**：不要逐个解释每个参数的含义

### 6. **灵活性原则**
- **时间可调节**：等待时间、间隔时间都要可以调整
- **行为可选择**：通过简单的参数控制不同的执行方式
- **路径不限制**：支持相对路径、绝对路径、任意路径格式
- **返回有用信息**：返回值要么是结果，要么是成功/失败状态

### 7. **实用性原则**
- **解决实际问题**：每个函数都要解决具体的使用场景
- **功能完整性**：一个函数包含完整的业务逻辑
- **易于理解**：代码逻辑清晰，不需要复杂的文档说明
- **性能考虑**：及时释放资源，避免内存泄漏

### 应用效果对比

```javascript
// ❌ 复杂的写法
function 基础找图功能() { ... }
function 找图并点击功能() { ... }
function 等待图片出现功能() { ... }

// ✅ 简洁的写法
function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) { ... }
```

### 强制执行说明

1. **开发过程中严格遵守**：所有新编写的代码必须符合此规则
2. **问题调试例外**：遇到问题时可以临时增加调试功能和逻辑功能
3. **代码审查标准**：所有代码提交前必须检查是否符合简洁规则
4. **重构优先级**：发现不符合规则的旧代码应优先重构

---
