/**
 * Magic游戏辅助脚本 - 主脚本
 * 基于AutoXjs ozobiozobi v6.5.8.17
 */

// 全局屏幕捕获权限
if (!requestScreenCapture()) {
    console.log("❌ 屏幕捕获权限获取失败");
    exit();
}
console.log("✅ 屏幕捕获权限获取成功");

// 导入登陆模块
var 登陆模块 = require('./常用功能/登陆.js');
var 帐号模块 = require('./常用功能/帐号.js');
var 首次教程模块 = require('./常用功能/首次教程.js');
var 检查模块 = require('./常用功能/检查.js');
var 裂球模块 = require('./常用功能/裂球.js');

function 游戏_程序() {
    try {
        console.log("🎯 启动Magic游戏辅助程序");

        // 第一步：检查游戏状态并启动
        console.log("📋 步骤1: 检查游戏状态");

        // 先检查游戏是否已经在运行
        var 当前包名 = currentPackage();
        if (当前包名 === "com.winrgames.bigtime") {
            console.log("✅ 游戏已在运行，直接执行后续功能");
        } else {
            console.log("📋 游戏未运行，尝试启动游戏");
            var 登陆成功 = 登陆模块.登陆_游戏("com.winrgames.bigtime", "BigTime", 5000, 200);

            if (!登陆成功) {
                console.log("⚠️ 游戏启动失败，但继续执行后续功能（可能游戏已在后台）");
                // 不直接返回false，继续执行后续功能
            } else {
                console.log("✅ 游戏启动成功");
            }
        }

        // 第二步：随机等待时间（4秒-6秒）
        var 随机等待时间 = random(4000, 6000);
        console.log("⏳ 随机等待 " + 随机等待时间 + " 毫秒后执行下一个功能");
        sleep(随机等待时间);

        // 第三步：执行帐号登陆
        console.log("📋 步骤2: 执行帐号登陆");
        var 登陆 = 帐号模块.登陆_帐号();

        if (!登陆) {
            console.log("⚠️ 帐号登陆未找到跳过按钮，继续执行后续功能");
        } else {
            console.log("✅ 帐号登陆成功，已点击跳过按钮");
        }

        // 第四步：随机等待时间（500毫秒-2.5秒）
        var 随机等待时间2 = random(500, 2500);
        console.log("⏳ 随机等待 " + 随机等待时间2 + " 毫秒后执行首次教程");
        sleep(随机等待时间2);

        // 第五步：执行首次教程
        console.log("📋 步骤3: 执行首次教程");
        var 首次教程 = 首次教程模块.首次_教程();

        if (!首次教程) {
            console.log("⚠️ 首次教程未找到或未完成，继续执行后续功能");
        } else {
            console.log("✅ 首次教程完成成功");
        }

        // 第六步：检查分数
        console.log("📋 步骤6: 检查分数");
        var 检查分数成功 = 检查模块.检查_分数();

        if (!检查分数成功) {
            console.log("⚠️ 检查分数失败，但继续执行后续功能");
        } else {
            console.log("✅ 检查分数完成成功");
        }

        // 第七步：待开发功能（预留）
        // console.log("📋 步骤7: 待开发功能");
        // TODO: 在这里添加第七步的具体功能

        // 第八步：待开发功能（预留）
        // console.log("📋 步骤8: 待开发功能");
        // TODO: 在这里添加第八步的具体功能

        // 第九步：待开发功能（预留）
        // console.log("📋 步骤9: 待开发功能");
        // TODO: 在这里添加第九步的具体功能

        // 第十步：游玩裂球
        console.log("📋 步骤10: 游玩裂球");
        var 游玩裂球成功 = 裂球模块.游玩_裂球();

        if (!游玩裂球成功) {
            console.log("⚠️ 游玩裂球失败，但继续执行后续功能");
        } else {
            console.log("✅ 游玩裂球完成成功");
        }

        console.log("🎉 游戏程序执行完成");
        return true;

    } catch (error) {
        console.error("❌ 游戏程序执行错误: " + error);
        return false;
    }
}

// 主程序入口
if (typeof module !== 'undefined' && module.exports) {
    // 作为模块导出
    module.exports = {
        游戏_程序: 游戏_程序
    };
} else {
    // 直接运行
    游戏_程序();
}