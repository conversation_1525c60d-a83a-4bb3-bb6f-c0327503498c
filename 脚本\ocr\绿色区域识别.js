/**
 * 绿色区域数字识别脚本
 * 使用Tesseract OCR识别所有数字(0-9)
 * 绿色区域范围：(89,773,170,105)
 * 参考2.js方案3：黑色数字直接提取
 */

// 导入Tesseract OCR类
importClass(com.googlecode.tesseract.android.TessBaseAPI);

// 申请截图权限
if (!requestScreenCapture(false)) {
    console.log("❌ 截图权限申请失败");
    exit();
}
console.log("✅ 截图权限申请成功");
sleep(2000);  // 等待权限生效

console.log("🎯 开始绿色区域数字识别");

try {
    // 新建OCR实例
    var tessocr = new TessBaseAPI();
    console.log("📦 Tesseract OCR实例创建成功");
    
    // 设置tessdata父目录路径
    var dataPath = files.path("./");
    console.log("📁 数据目录路径: " + dataPath);
    
    // 初始化tessocr - 使用英文模式
    console.log("🔧 正在初始化Tesseract OCR...");
    var ok = tessocr.init(dataPath, "eng");
    
    if (ok) {
        console.log("✅ Tesseract OCR初始化成功");
        
        // OCR识别优化配置
        console.log("⚙️ 设置OCR识别优化参数");
        
        // PSM 6: 单一文本块识别
        tessocr.setPageSegMode(6);
        
        // 设置字符白名单，只识别数字0-9
        tessocr.setVariable("tessedit_char_whitelist", "0123456789");
        
        // 设置OCR引擎模式为LSTM引擎
        tessocr.setVariable("tessedit_ocr_engine_mode", "1");
        
        console.log("✅ OCR识别优化配置完成");

        // 截图并裁剪指定区域
        console.log("📸 截图并裁剪绿色区域 (89,773,170,105)");
        var img = captureScreen();

        if (!img) {
            console.log("❌ 截图失败");
            exit();
        }

        // 裁剪绿色数字区域
        var 裁剪区域 = images.clip(img, 89, 773, 170, 105);
        console.log("✅ 裁剪完成，区域尺寸: " + 裁剪区域.getWidth() + "x" + 裁剪区域.getHeight());

        // 释放原始截图
        img.recycle();

        // 完全参考2.js方案3：遍历所有黑色阈值，识别所有数字
        console.log("🔧 方案3: 黑色数字直接提取 (识别所有数字0-9)");
        
        // 遍历所有黑色到灰色范围，每1个灰度都测试
        var 黑色范围列表 = [];
        for (var k = 1; k <= 100; k++) {  // 从1到100，每1个灰度值都测试
            var 上界值 = k.toString(16).padStart(2, '0');  // 01, 02, 03, ..., 64
            var 上界颜色 = "#" + 上界值 + 上界值 + 上界值;
            黑色范围列表.push({
                名称: "黑色范围" + k,
                下界: "#000000",
                上界: 上界颜色
            });
        }
        console.log("📊 将测试 " + 黑色范围列表.length + " 个黑色范围，识别所有数字0-9");

        var 最佳黑色提取 = {范围: 0, 结果: "", 数字数: 0, 置信度: 0, 上界: ""};
        var 全部识别结果 = [];
        var 所有结果 = [];  // 收集所有有效结果
        var 已输出纠错信息 = false;  // 标记是否已输出过纠错信息
        var 已输出轮廓纠错信息 = false;  // 标记是否已输出过轮廓纠错信息

        黑色范围列表.forEach(function(范围, 索引) {
            try {
                var k = 索引 + 1;

                // 先将图片转为灰度图
                var 灰度图 = images.grayscale(裁剪区域);

                // 使用当前阈值进行黑色提取
                var 黑色提取图 = images.threshold(灰度图, k, 255, "BINARY");

                // 多重图像优化：解决数字3和5识别混淆问题

                // 简化处理：直接使用黑色提取结果
                var 最终图 = 黑色提取图;

                tessocr.setImage(最终图.getBitmap());
                var 原始识别结果 = tessocr.getUTF8Text().trim();

                // 像素分布分析：通过分析上下左右像素分布区分3和5
                var 识别结果1 = 像素分布检测(最终图, 原始识别结果);

                // 新增：基于频次的投票纠错
                var 识别结果 = 频次投票纠错(所有结果, 识别结果1);
                var 数字字符数 = (识别结果.match(/\d/g) || []).length;
                var 纯数字 = 识别结果.replace(/[^\d]/g, '');

                // 获取识别置信度
                var 置信度 = 0;
                try {
                    if (typeof tessocr.meanConfidence === 'function') {
                        置信度 = tessocr.meanConfidence();
                    } else if (typeof tessocr.getMeanConfidence === 'function') {
                        置信度 = tessocr.getMeanConfidence();
                    } else {
                        置信度 = 数字字符数 * 25;
                    }
                } catch (e) {
                    置信度 = 数字字符数 * 25;
                }

                // 收集所有有效结果（不显示实时输出，避免重复）
                if (数字字符数 > 0) {
                    var 清理后文本 = 识别结果.replace(/\n/g, ' ').replace(/\s+/g, ' ');
                    所有结果.push({
                        阈值: k,
                        原始文本: 清理后文本,
                        纯数字: 纯数字,
                        数字字符数: 数字字符数,
                        置信度: 置信度
                    });
                    全部识别结果.push({方案: 范围.名称 + "提取", 结果: 识别结果, 数字数: 数字字符数, 置信度: 置信度});
                }

                // 记录黑色提取的最佳结果（优先选择置信度最高的结果）
                var 是更好结果 = false;
                if (数字字符数 >= 1) {
                    if (最佳黑色提取.数字数 === 0) {
                        是更好结果 = true;
                    } else if (置信度 > 最佳黑色提取.置信度) {
                        是更好结果 = true;
                    }
                }

                if (是更好结果) {
                    最佳黑色提取 = {范围: k, 结果: 纯数字, 数字数: 数字字符数, 置信度: 置信度, 上界: 范围.上界};
                    console.log("   ⭐ 更好结果！");
                }

                // 释放处理后的图片资源
                灰度图.recycle();
                黑色提取图.recycle();
                去噪图.recycle();
                模糊图.recycle();
                增强图.recycle();
                if (最终图 !== 增强图) {
                    最终图.recycle();
                }

            } catch (e) {
                console.log("⚠️ " + 范围.名称 + "提取失败: " + e);
            }
        });

        // 按置信度从低到高排序并显示结果
        所有结果.sort(function(a, b) {
            return a.置信度 - b.置信度;  // 升序排列
        });

        console.log("\n📊 按置信度排序的识别结果（置信度从低到高）:");
        所有结果.forEach(function(结果, 索引) {
            console.log("   " + (索引 + 1) + ". 阈值" + 结果.阈值 + ": 原始[" + 结果.原始文本 + "] 纯数字[" + 结果.纯数字 + "] 字符数[" + 结果.数字字符数 + "] 置信度[" + 结果.置信度.toFixed(1) + "%]");
        });

        // 显示黑色提取的最佳结果
        if (最佳黑色提取.数字数 > 0) {
            console.log("🏆 最佳结果: 黑色范围" + 最佳黑色提取.范围 + " (" + 最佳黑色提取.上界 + ") → \"" + 最佳黑色提取.结果 + "\" (数字:" + 最佳黑色提取.数字数 + "个, 置信度:" + 最佳黑色提取.置信度.toFixed(1) + "%)");
        } else {
            console.log("❌ 黑色提取无有效识别结果");
        }

        console.log("\n🎯 最终识别结果: \"" + 最佳黑色提取.结果 + "\"");

        // 输出识别区域的坐标信息
        if (最佳黑色提取.数字数 > 0) {
            console.log("📍 识别区域坐标信息:");
            console.log("   区域左上角: (89, 773)");
            console.log("   区域宽高: 170 x 105");
            console.log("   区域右下角: (" + (89 + 170) + ", " + (773 + 105) + ")");
            console.log("   区域中心点: (" + (89 + 170/2) + ", " + (773 + 105/2) + ")");
            console.log("   识别到的数字: \"" + 最佳黑色提取.结果 + "\" 位于绿色区域");
        }

        // 释放图片资源
        裁剪区域.recycle();
        console.log("🧹 图片资源已释放");
        
    } else {
        console.log("❌ Tesseract OCR初始化失败");
        console.log("💡 可能原因:");
        console.log("   1. tessdata目录不存在");
        console.log("   2. eng.traineddata文件缺失");
    }
    
} catch (e) {
    console.log("❌ Tesseract OCR运行出错: " + e);
    console.log("💡 可能原因:");
    console.log("   1. tessdata目录不存在");
    console.log("   2. eng.traineddata文件缺失");
    console.log("   3. 截图或图像处理失败");
}

console.log("\n📋 绿色区域数字识别完成");
console.log("🎯 绿色区域范围: (89,773,170,105)");
console.log("🚀 遍历阈值1-100，多重图像优化+像素分布分析识别所有数字0-9");

// 静默版像素分布分析：只在第一次检测到纠错时输出信息
function 像素分布检测(图像, 原始文本) {
    if (!原始文本) return 原始文本;

    var 纯数字 = 原始文本.replace(/[^\d]/g, '');

    // 只对包含3或5的数字进行分析
    if (纯数字.includes('3') || 纯数字.includes('5')) {
        try {
            // 获取图像尺寸
            var 图像宽度 = 图像.getWidth();
            var 图像高度 = 图像.getHeight();

            // 静默执行像素分布分析
            var 像素分布分析 = 分析像素分布_静默版(图像, 图像宽度, 图像高度);

            // 静默执行数字特征分析
            var 分布分析结果 = 分析数字特征_静默版(像素分布分析, 纯数字);

            // 只在第一次需要纠错时输出信息，避免重复
            if (分布分析结果.建议修正 && !已输出纠错信息) {
                console.log("   🔧 数字纠错: [" + 原始文本 + "] → [" + 分布分析结果.修正结果 + "] (原因: " + 分布分析结果.简要原因 + ")");
                已输出纠错信息 = true;  // 标记已输出，避免重复
            }

            if (分布分析结果.建议修正) {
                return 分布分析结果.修正结果;
            }

            return 原始文本;

        } catch (e) {
            return 原始文本;
        }
    }

    return 原始文本;
}

// 静默版像素分布分析 - 不输出调试信息
function 分析像素分布_静默版(图像, 图像宽度, 图像高度) {
    // 定义四个分析区域
    var 区域配置 = {
        左侧: { x起始: 0, x结束: Math.floor(图像宽度 * 0.4), y起始: Math.floor(图像高度 * 0.2), y结束: Math.floor(图像高度 * 0.8) },
        右侧: { x起始: Math.floor(图像宽度 * 0.6), x结束: 图像宽度, y起始: Math.floor(图像高度 * 0.2), y结束: Math.floor(图像高度 * 0.8) },
        上部: { x起始: Math.floor(图像宽度 * 0.2), x结束: Math.floor(图像宽度 * 0.8), y起始: 0, y结束: Math.floor(图像高度 * 0.4) },
        下部: { x起始: Math.floor(图像宽度 * 0.2), x结束: Math.floor(图像宽度 * 0.8), y起始: Math.floor(图像高度 * 0.6), y结束: 图像高度 }
    };

    var 分布结果 = {};

    // 静默分析每个区域
    for (var 区域名 in 区域配置) {
        var 区域 = 区域配置[区域名];
        var 黑色像素数 = 0;
        var 总像素数 = 0;

        try {
            for (var y = 区域.y起始; y < 区域.y结束; y++) {
                for (var x = 区域.x起始; x < 区域.x结束; x++) {
                    if (x >= 0 && x < 图像宽度 && y >= 0 && y < 图像高度) {
                        var 像素颜色 = 图像.pixel(x, y);
                        var 红色 = (像素颜色 >> 16) & 0xFF;
                        var 绿色 = (像素颜色 >> 8) & 0xFF;
                        var 蓝色 = 像素颜色 & 0xFF;
                        var 灰度值 = (红色 + 绿色 + 蓝色) / 3;

                        if (灰度值 < 100) {
                            黑色像素数++;
                        }
                        总像素数++;
                    }
                }
            }

            var 密度 = 总像素数 > 0 ? (黑色像素数 / 总像素数) * 100 : 0;
            分布结果[区域名 + "密度"] = 密度;

        } catch (e) {
            分布结果[区域名 + "密度"] = 0;
        }
    }

    return 分布结果;
}

// 静默版数字特征分析 - 只返回结果，不输出调试信息
function 分析数字特征_静默版(像素分布, 当前数字) {
    var 左侧密度 = 像素分布.左侧密度;
    var 右侧密度 = 像素分布.右侧密度;
    var 上部密度 = 像素分布.上部密度;
    var 下部密度 = 像素分布.下部密度;

    // 计算特征指标
    var 左右差异 = 右侧密度 - 左侧密度;  // 正值表示右侧密度高

    var 建议修正 = false;
    var 修正结果 = 当前数字;
    var 简要原因 = "";

    // 判断逻辑
    if (Math.abs(左右差异) > 2.0) {  // 左右差异显著
        if (左右差异 < -2.0) {
            // 左侧密度高，右侧密度低 → 数字3特征
            if (当前数字.includes('5')) {
                建议修正 = true;
                修正结果 = 当前数字.replace(/5/g, '3');
                简要原因 = "右侧开口特征";
            }
        } else {
            // 右侧密度高，左侧密度低 → 数字5特征
            if (当前数字.includes('3')) {
                建议修正 = true;
                修正结果 = 当前数字.replace(/3/g, '5');
                简要原因 = "左侧开口特征";
            }
        }
    }

    // 结合上下分布进一步判断数字5
    if (上部密度 > 下部密度 + 3.0) {
        if (当前数字.includes('3') && !建议修正) {
            建议修正 = true;
            修正结果 = 当前数字.replace(/3/g, '5');
            简要原因 = "上部密度高特征";
        } else if (建议修正 && 简要原因 === "左侧开口特征") {
            简要原因 = "左侧开口+上部密度高";
        }
    }

    return {
        建议修正: 建议修正,
        修正结果: 修正结果,
        简要原因: 简要原因
    };
}

// 基于频次的投票纠错算法
function 频次投票纠错(所有结果, 当前最佳结果) {
    if (!所有结果 || 所有结果.length === 0) {
        return 当前最佳结果;
    }

    // 统计所有识别结果的频次
    var 结果统计 = {};
    var 总次数 = 0;

    所有结果.forEach(function(项) {
        if (项.纯数字 && 项.纯数字.length > 0) {
            var 数字 = 项.纯数字;
            if (结果统计[数字]) {
                结果统计[数字].次数++;
                结果统计[数字].总置信度 += 项.置信度 || 0;
            } else {
                结果统计[数字] = {
                    次数: 1,
                    总置信度: 项.置信度 || 0,
                    平均置信度: 项.置信度 || 0
                };
            }
            总次数++;
        }
    });

    // 计算平均置信度
    for (var 数字 in 结果统计) {
        结果统计[数字].平均置信度 = 结果统计[数字].总置信度 / 结果统计[数字].次数;
    }

    console.log("   📊 频次投票统计:");
    for (var 数字2 in 结果统计) {
        var 统计 = 结果统计[数字2];
        var 频次百分比 = (统计.次数 / 总次数 * 100).toFixed(1);
        console.log("      " + 数字2 + ": " + 统计.次数 + "次(" + 频次百分比 + "%), 平均置信度:" + 统计.平均置信度.toFixed(1) + "%");
    }

    // 特殊处理：如果是2和4的混淆问题
    var 当前数字 = 当前最佳结果.replace(/[^\d]/g, '');
    if ((当前数字.includes('2') || 当前数字.includes('4')) && Object.keys(结果统计).length >= 2) {

        // 查找包含2的结果和包含4的结果
        var 包含2的结果 = [];
        var 包含4的结果 = [];

        for (var 数字3 in 结果统计) {
            if (数字3.includes('2')) {
                包含2的结果.push({数字: 数字3, 统计: 结果统计[数字3]});
            }
            if (数字3.includes('4')) {
                包含4的结果.push({数字: 数字3, 统计: 结果统计[数字3]});
            }
        }

        // 计算2和4的总频次
        var 数字2总频次 = 包含2的结果.reduce(function(sum, item) { return sum + item.统计.次数; }, 0);
        var 数字4总频次 = 包含4的结果.reduce(function(sum, item) { return sum + item.统计.次数; }, 0);

        console.log("   🎯 2vs4分析: 数字2总频次=" + 数字2总频次 + ", 数字4总频次=" + 数字4总频次);

        // 如果数字2的频次明显高于数字4，且当前识别为4，则纠正为2
        if (数字2总频次 > 数字4总频次 && 数字2总频次 >= 总次数 * 0.6 && 当前数字.includes('4')) {
            var 最佳2结果 = 包含2的结果.reduce(function(best, current) {
                return current.统计.次数 > best.统计.次数 ? current : best;
            });

            console.log("   🔧 频次纠错: [" + 当前最佳结果 + "] → [" + 最佳2结果.数字 + "] (数字2频次:" + 数字2总频次 + "/" + 总次数 + "=" + (数字2总频次/总次数*100).toFixed(1) + "%)");
            return 最佳2结果.数字;
        }

        // 如果数字4的频次明显高于数字2，且当前识别为2，则纠正为4
        if (数字4总频次 > 数字2总频次 && 数字4总频次 >= 总次数 * 0.6 && 当前数字.includes('2')) {
            var 最佳4结果 = 包含4的结果.reduce(function(best, current) {
                return current.统计.次数 > best.统计.次数 ? current : best;
            });

            console.log("   🔧 频次纠错: [" + 当前最佳结果 + "] → [" + 最佳4结果.数字 + "] (数字4频次:" + 数字4总频次 + "/" + 总次数 + "=" + (数字4总频次/总次数*100).toFixed(1) + "%)");
            return 最佳4结果.数字;
        }
    }

    console.log("   ✅ 频次投票: 保持原结果 [" + 当前最佳结果 + "]");
    return 当前最佳结果;
}











