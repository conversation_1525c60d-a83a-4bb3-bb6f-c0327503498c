/**
 * Magic悬浮球系统 - 事件处理模块
 * 处理主球点击、菜单图标点击等事件
 * 提供震动反馈和功能执行调度
 */

// 导入菜单管理器
var 悬浮球菜单 = require('./悬浮球菜单.js');

// 事件状态管理
var 事件状态 = {
    主球事件已绑定: false,
    菜单事件已绑定: false,
    事件监听器列表: []
};

/**
 * 悬浮球事件处理模块
 */
var 悬浮球事件 = {
    
    /**
     * 绑定主球点击事件
     * @param {Object} 悬浮球窗口 悬浮球窗口实例
     * @returns {boolean} 绑定是否成功
     */
    绑定主球事件: function(悬浮球窗口) {
        try {
            if (!悬浮球窗口 || !悬浮球窗口.主悬浮球) {
                console.warn("悬浮球窗口或主球控件不存在，无法绑定事件");
                return false;
            }
            
            // 设置全局点击处理器，由拖拽系统调用
            global.悬浮球点击处理器 = function() {
                try {
                    console.log("主悬浮球被点击");

                    // 震动反馈
                    device.vibrate(80);

                    // 播放点击动画
                    悬浮球事件.播放点击动画(悬浮球窗口.主悬浮球);

                    // 获取主球位置并切换菜单状态
                    var 主球位置 = 悬浮球事件.获取主球位置();
                    if (主球位置) {
                        悬浮球菜单.切换菜单状态(主球位置);
                    } else {
                        console.warn("无法获取主球位置");
                        toast("菜单展开失败");
                    }

                } catch (e) {
                    console.error("主球点击事件处理失败:", e);
                }
            };

            // 备用的直接点击事件绑定（防止拖拽系统失效）
            悬浮球窗口.主悬浮球.click(function() {
                // 只有在拖拽系统失效时才会触发这个事件
                if (global.悬浮球点击处理器) {
                    global.悬浮球点击处理器();
                }
            });
            
            事件状态.主球事件已绑定 = true;
            console.log("主球点击事件绑定成功");
            return true;
            
        } catch (e) {
            console.error("绑定主球事件失败:", e);
            return false;
        }
    },
    

    
    /**
     * 播放点击动画
     * @param {Object} 控件 要播放动画的控件
     */
    播放点击动画: function(控件) {
        try {
            if (!控件) {
                console.warn("控件不存在，无法播放动画");
                return;
            }
            
            // 缩小动画
            控件.animate()
                .scaleX(0.9)
                .scaleY(0.9)
                .setDuration(100)
                .withEndAction(function() {
                    // 恢复动画
                    控件.animate()
                        .scaleX(1.0)
                        .scaleY(1.0)
                        .setDuration(100)
                        .start();
                })
                .start();
                
        } catch (e) {
            console.error("播放点击动画失败:", e);
        }
    },
    
    /**
     * 执行菜单功能 - 委托给菜单管理器
     * @param {String} 图标名 图标名称
     */
    执行菜单功能: function(图标名) {
        try {
            // 委托给悬浮球菜单模块处理
            悬浮球菜单.执行菜单功能(图标名);
        } catch (e) {
            console.error("执行菜单功能失败:", e);
        }
    },
    
    /**
     * 解绑所有事件
     * @returns {boolean} 解绑是否成功
     */
    解绑所有事件: function() {
        try {
            // 清理事件监听器
            事件状态.事件监听器列表.forEach(function(监听器) {
                try {
                    if (监听器 && typeof 监听器.remove === 'function') {
                        监听器.remove();
                    }
                } catch (e) {
                    console.warn("移除事件监听器失败:", e);
                }
            });

            // 重置状态
            事件状态.主球事件已绑定 = false;
            事件状态.菜单事件已绑定 = false;
            事件状态.事件监听器列表.length = 0; // 更高效的数组清空

            // 清理全局点击处理器
            if (global.悬浮球点击处理器) {
                delete global.悬浮球点击处理器;
            }

            console.log("所有悬浮球事件已解绑");
            return true;

        } catch (e) {
            console.error("解绑悬浮球事件失败:", e);
            return false;
        }
    },
    
    /**
     * 获取主球位置
     * @param {Object} 悬浮球窗口 悬浮球窗口实例
     * @returns {Object} 位置对象 {x, y}
     */
    获取主球位置: function() {
        try {
            // 从悬浮球管理系统获取位置
            if (global.悬浮球管理系统) {
                var XML悬浮球 = global.悬浮球管理系统.获取模块('XML悬浮球');
                if (XML悬浮球) {
                    return XML悬浮球.获取位置();
                }
            }

            // 备用方案：使用默认位置
            console.warn("无法获取主球位置，使用默认位置");
            return { x: -20, y: device.height - 300 };

        } catch (e) {
            console.error("获取主球位置失败:", e);
            return null;
        }
    },

    /**
     * 获取事件状态
     * @returns {Object} 事件状态信息
     */
    获取事件状态: function() {
        return {
            主球事件已绑定: 事件状态.主球事件已绑定,
            菜单事件已绑定: 事件状态.菜单事件已绑定,
            监听器数量: 事件状态.事件监听器列表.length
        };
    }
};

// 导出模块
module.exports = 悬浮球事件;
