Device connected: Devi<PERSON> samsung SM-N9760(adb: emulator-5556)
发送项目耗时: 0.095 秒
发送项目耗时: 16.694 秒
12:30:41.544/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/su_main.js]
12:30:41.884/D: ✅ 屏幕捕获权限获取成功
12:30:41.893/D: 🧪 开始执行算数主程序测试...
12:30:41.895/D: 🚀 ===== 算数游戏多区域OCR识别测试 =====
12:30:41.896/D: 📋 基于80-180阈值优化方案，60%性能提升
12:30:41.897/D: 🎯 将依次测试：蓝色、黄色、绿色、红色区域
12:30:41.898/D: 
🔵 ===== 蓝色区域识别 =====
12:30:41.913/D: 数据路径: /storage/emulated/0/脚本/magic/脚本/ocr/
12:30:42.309/D: ⚙️ 设置OCR识别优化参数（数字识别专用）
12:30:42.310/D: ✅ OCR识别优化配置完成
12:30:42.314/D: 📊 将测试 80-180 灰度范围（基于优化方案），遍历黑色到灰色范围
12:30:42.455/D: 阈值80 | 二值化:80 | 中值滤波:3x3 | 黑色提取:#000000 到 #505050 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.457/D:    ⭐ 发现更好结果！阈值80 识别到2个数字
12:30:42.568/D: 阈值81 | 二值化:81 | 中值滤波:3x3 | 黑色提取:#000000 到 #515151 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.585/D: 阈值82 | 二值化:82 | 中值滤波:3x3 | 黑色提取:#000000 到 #525252 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.600/D: 阈值83 | 二值化:83 | 中值滤波:3x3 | 黑色提取:#000000 到 #535353 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.617/D: 阈值84 | 二值化:84 | 中值滤波:3x3 | 黑色提取:#000000 到 #545454 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.636/D: 阈值85 | 二值化:85 | 中值滤波:3x3 | 黑色提取:#000000 到 #555555 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.652/D: 阈值86 | 二值化:86 | 中值滤波:3x3 | 黑色提取:#000000 到 #565656 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.668/D: 阈值87 | 二值化:87 | 中值滤波:3x3 | 黑色提取:#000000 到 #575757 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.685/D: 阈值88 | 二值化:88 | 中值滤波:3x3 | 黑色提取:#000000 到 #585858 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.698/D: 阈值89 | 二值化:89 | 中值滤波:3x3 | 黑色提取:#000000 到 #595959 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.721/D: 阈值90 | 二值化:90 | 中值滤波:3x3 | 黑色提取:#000000 到 #5a5a5a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.735/D: 阈值91 | 二值化:91 | 中值滤波:3x3 | 黑色提取:#000000 到 #5b5b5b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.750/D: 阈值92 | 二值化:92 | 中值滤波:3x3 | 黑色提取:#000000 到 #5c5c5c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.764/D: 阈值93 | 二值化:93 | 中值滤波:3x3 | 黑色提取:#000000 到 #5d5d5d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.778/D: 阈值94 | 二值化:94 | 中值滤波:3x3 | 黑色提取:#000000 到 #5e5e5e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.792/D: 阈值95 | 二值化:95 | 中值滤波:3x3 | 黑色提取:#000000 到 #5f5f5f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.807/D: 阈值96 | 二值化:96 | 中值滤波:3x3 | 黑色提取:#000000 到 #606060 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.821/D: 阈值97 | 二值化:97 | 中值滤波:3x3 | 黑色提取:#000000 到 #616161 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.834/D: 阈值98 | 二值化:98 | 中值滤波:3x3 | 黑色提取:#000000 到 #626262 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.847/D: 阈值99 | 二值化:99 | 中值滤波:3x3 | 黑色提取:#000000 到 #636363 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.861/D: 阈值100 | 二值化:100 | 中值滤波:3x3 | 黑色提取:#000000 到 #646464 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.876/D: 阈值101 | 二值化:101 | 中值滤波:3x3 | 黑色提取:#000000 到 #656565 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.889/D: 阈值102 | 二值化:102 | 中值滤波:3x3 | 黑色提取:#000000 到 #666666 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.902/D: 阈值103 | 二值化:103 | 中值滤波:3x3 | 黑色提取:#000000 到 #676767 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.916/D: 阈值104 | 二值化:104 | 中值滤波:3x3 | 黑色提取:#000000 到 #686868 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.929/D: 阈值105 | 二值化:105 | 中值滤波:3x3 | 黑色提取:#000000 到 #696969 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.944/D: 阈值106 | 二值化:106 | 中值滤波:3x3 | 黑色提取:#000000 到 #6a6a6a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.959/D: 阈值107 | 二值化:107 | 中值滤波:3x3 | 黑色提取:#000000 到 #6b6b6b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:42.981/D: 阈值108 | 二值化:108 | 中值滤波:3x3 | 黑色提取:#000000 到 #6c6c6c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.004/D: 阈值109 | 二值化:109 | 中值滤波:3x3 | 黑色提取:#000000 到 #6d6d6d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.034/D: 阈值110 | 二值化:110 | 中值滤波:3x3 | 黑色提取:#000000 到 #6e6e6e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.064/D: 阈值111 | 二值化:111 | 中值滤波:3x3 | 黑色提取:#000000 到 #6f6f6f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.095/D: 阈值112 | 二值化:112 | 中值滤波:3x3 | 黑色提取:#000000 到 #707070 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.125/D: 阈值113 | 二值化:113 | 中值滤波:3x3 | 黑色提取:#000000 到 #717171 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.156/D: 阈值114 | 二值化:114 | 中值滤波:3x3 | 黑色提取:#000000 到 #727272 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.206/D: 阈值115 | 二值化:115 | 中值滤波:3x3 | 黑色提取:#000000 到 #737373 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.242/D: 阈值116 | 二值化:116 | 中值滤波:3x3 | 黑色提取:#000000 到 #747474 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.288/D: 阈值117 | 二值化:117 | 中值滤波:3x3 | 黑色提取:#000000 到 #757575 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.319/D: 阈值118 | 二值化:118 | 中值滤波:3x3 | 黑色提取:#000000 到 #767676 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.372/D: 阈值119 | 二值化:119 | 中值滤波:3x3 | 黑色提取:#000000 到 #777777 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.420/D: 阈值120 | 二值化:120 | 中值滤波:3x3 | 黑色提取:#000000 到 #787878 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.467/D: 阈值121 | 二值化:121 | 中值滤波:3x3 | 黑色提取:#000000 到 #797979 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.515/D: 阈值122 | 二值化:122 | 中值滤波:3x3 | 黑色提取:#000000 到 #7a7a7a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.541/D: 阈值123 | 二值化:123 | 中值滤波:3x3 | 黑色提取:#000000 到 #7b7b7b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.578/D: 阈值124 | 二值化:124 | 中值滤波:3x3 | 黑色提取:#000000 到 #7c7c7c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.621/D: 阈值125 | 二值化:125 | 中值滤波:3x3 | 黑色提取:#000000 到 #7d7d7d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.673/D: 阈值126 | 二值化:126 | 中值滤波:3x3 | 黑色提取:#000000 到 #7e7e7e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.716/D: 阈值127 | 二值化:127 | 中值滤波:3x3 | 黑色提取:#000000 到 #7f7f7f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.769/D: 阈值128 | 二值化:128 | 中值滤波:3x3 | 黑色提取:#000000 到 #808080 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.816/D: 阈值129 | 二值化:129 | 中值滤波:3x3 | 黑色提取:#000000 到 #818181 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.852/D: 阈值130 | 二值化:130 | 中值滤波:3x3 | 黑色提取:#000000 到 #828282 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:43.964/D: 阈值131 | 二值化:131 | 中值滤波:3x3 | 黑色提取:#000000 到 #838383 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:43.966/D:    ⭐ 发现更好结果！阈值131 识别到3个数字
12:30:44.073/D: 阈值132 | 二值化:132 | 中值滤波:3x3 | 黑色提取:#000000 到 #848484 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:44.222/D: 阈值133 | 二值化:133 | 中值滤波:3x3 | 黑色提取:#000000 到 #858585 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:44.368/D: 阈值134 | 二值化:134 | 中值滤波:3x3 | 黑色提取:#000000 到 #868686 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:44.395/D: 阈值135 | 二值化:135 | 中值滤波:3x3 | 黑色提取:#000000 到 #878787 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:44.587/D: 阈值136 | 二值化:136 | 中值滤波:3x3 | 黑色提取:#000000 到 #888888 | 识别:[7
22
1] | 纯数字:[7221] | 字符数:4
12:30:44.589/D:    ⭐ 发现更好结果！阈值136 识别到4个数字
12:30:44.776/D: 阈值137 | 二值化:137 | 中值滤波:3x3 | 黑色提取:#000000 到 #898989 | 识别:[7
22
1 4] | 纯数字:[72214] | 字符数:5
12:30:44.778/D:    ⭐ 发现更好结果！阈值137 识别到5个数字
12:30:45.042/D: 阈值138 | 二值化:138 | 中值滤波:3x3 | 黑色提取:#000000 到 #8a8a8a | 识别:[7
22
3 4] | 纯数字:[72234] | 字符数:5
12:30:45.260/D: 阈值139 | 二值化:139 | 中值滤波:3x3 | 黑色提取:#000000 到 #8b8b8b | 识别:[4
22
4] | 纯数字:[4224] | 字符数:4
12:30:45.505/D: 阈值140 | 二值化:140 | 中值滤波:3x3 | 黑色提取:#000000 到 #8c8c8c | 识别:[2
4] | 纯数字:[24] | 字符数:2
12:30:45.779/D: 阈值141 | 二值化:141 | 中值滤波:3x3 | 黑色提取:#000000 到 #8d8d8d | 识别:[22
8 4] | 纯数字:[2284] | 字符数:4
12:30:46.046/D: 阈值142 | 二值化:142 | 中值滤波:3x3 | 黑色提取:#000000 到 #8e8e8e | 识别:[22
8 4] | 纯数字:[2284] | 字符数:4
12:30:46.230/D: 阈值143 | 二值化:143 | 中值滤波:3x3 | 黑色提取:#000000 到 #8f8f8f | 识别:[7
22
4] | 纯数字:[7224] | 字符数:4
12:30:46.415/D: 阈值144 | 二值化:144 | 中值滤波:3x3 | 黑色提取:#000000 到 #909090 | 识别:[7
22
4] | 纯数字:[7224] | 字符数:4
12:30:46.609/D: 阈值145 | 二值化:145 | 中值滤波:3x3 | 黑色提取:#000000 到 #919191 | 识别:[2
3 4] | 纯数字:[234] | 字符数:3
12:30:46.952/D: 阈值146 | 二值化:146 | 中值滤波:3x3 | 黑色提取:#000000 到 #929292 | 识别:[22
4] | 纯数字:[224] | 字符数:3
12:30:47.235/D: 阈值147 | 二值化:147 | 中值滤波:3x3 | 黑色提取:#000000 到 #939393 | 识别:[23
4] | 纯数字:[234] | 字符数:3
12:30:47.352/D: 阈值148 | 二值化:148 | 中值滤波:3x3 | 黑色提取:#000000 到 #949494 | 识别:[722  
4] | 纯数字:[7224] | 字符数:4
12:30:47.476/D: 阈值149 | 二值化:149 | 中值滤波:3x3 | 黑色提取:#000000 到 #959595 | 识别:[7221 
1] | 纯数字:[72211] | 字符数:5
12:30:47.600/D: 阈值150 | 二值化:150 | 中值滤波:3x3 | 黑色提取:#000000 到 #969696 | 识别:[71
 22] | 纯数字:[7122] | 字符数:4
12:30:47.767/D: 阈值151 | 二值化:151 | 中值滤波:3x3 | 黑色提取:#000000 到 #979797 | 识别:[71
 22] | 纯数字:[7122] | 字符数:4
12:30:47.967/D: 阈值152 | 二值化:152 | 中值滤波:3x3 | 黑色提取:#000000 到 #989898 | 识别:[63241] | 纯数字:[63241] | 字符数:5
12:30:48.120/D: 阈值153 | 二值化:153 | 中值滤波:3x3 | 黑色提取:#000000 到 #999999 | 识别:[6311] | 纯数字:[6311] | 字符数:4
12:30:48.269/D: 阈值154 | 二值化:154 | 中值滤波:3x3 | 黑色提取:#000000 到 #9a9a9a | 识别:[6311] | 纯数字:[6311] | 字符数:4
12:30:48.420/D: 阈值155 | 二值化:155 | 中值滤波:3x3 | 黑色提取:#000000 到 #9b9b9b | 识别:[1
6311] | 纯数字:[16311] | 字符数:5
12:30:48.472/D: 阈值156 | 二值化:156 | 中值滤波:3x3 | 黑色提取:#000000 到 #9c9c9c | 识别:[2 21] | 纯数字:[221] | 字符数:3
12:30:48.554/D: 阈值157 | 二值化:157 | 中值滤波:3x3 | 黑色提取:#000000 到 #9d9d9d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:48.640/D: 阈值158 | 二值化:158 | 中值滤波:3x3 | 黑色提取:#000000 到 #9e9e9e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:48.725/D: 阈值159 | 二值化:159 | 中值滤波:3x3 | 黑色提取:#000000 到 #9f9f9f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:48.809/D: 阈值160 | 二值化:160 | 中值滤波:3x3 | 黑色提取:#000000 到 #a0a0a0 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:48.891/D: 阈值161 | 二值化:161 | 中值滤波:3x3 | 黑色提取:#000000 到 #a1a1a1 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.323/D: 阈值169 | 二值化:169 | 中值滤波:3x3 | 黑色提取:#000000 到 #a9a9a9 | 识别:[3] | 纯数字:[3] | 字符数:1
12:30:49.830/D: 
📊 ===== 识别结果汇总 =====
12:30:49.832/D: 总共测试了 101 个阈值（80-180范围），有效识别 83 次
12:30:49.833/D: 
🔍 详细结果列表:
12:30:49.835/D:    1. 阈值80 | 二值化:80 | 黑色提取:#000000 到 #505050 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.836/D:    2. 阈值81 | 二值化:81 | 黑色提取:#000000 到 #515151 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.838/D:    3. 阈值82 | 二值化:82 | 黑色提取:#000000 到 #525252 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.839/D:    4. 阈值83 | 二值化:83 | 黑色提取:#000000 到 #535353 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.839/D:    5. 阈值84 | 二值化:84 | 黑色提取:#000000 到 #545454 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.848/D:    6. 阈值85 | 二值化:85 | 黑色提取:#000000 到 #555555 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.849/D:    7. 阈值86 | 二值化:86 | 黑色提取:#000000 到 #565656 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.850/D:    8. 阈值87 | 二值化:87 | 黑色提取:#000000 到 #575757 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.850/D:    9. 阈值88 | 二值化:88 | 黑色提取:#000000 到 #585858 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.851/D:    10. 阈值89 | 二值化:89 | 黑色提取:#000000 到 #595959 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.852/D:    11. 阈值90 | 二值化:90 | 黑色提取:#000000 到 #5a5a5a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.852/D:    12. 阈值91 | 二值化:91 | 黑色提取:#000000 到 #5b5b5b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.853/D:    13. 阈值92 | 二值化:92 | 黑色提取:#000000 到 #5c5c5c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.854/D:    14. 阈值93 | 二值化:93 | 黑色提取:#000000 到 #5d5d5d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.855/D:    15. 阈值94 | 二值化:94 | 黑色提取:#000000 到 #5e5e5e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.856/D:    16. 阈值95 | 二值化:95 | 黑色提取:#000000 到 #5f5f5f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.856/D:    17. 阈值96 | 二值化:96 | 黑色提取:#000000 到 #606060 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.857/D:    18. 阈值97 | 二值化:97 | 黑色提取:#000000 到 #616161 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.857/D:    19. 阈值98 | 二值化:98 | 黑色提取:#000000 到 #626262 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.858/D:    20. 阈值99 | 二值化:99 | 黑色提取:#000000 到 #636363 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.859/D:    21. 阈值100 | 二值化:100 | 黑色提取:#000000 到 #646464 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.860/D:    22. 阈值101 | 二值化:101 | 黑色提取:#000000 到 #656565 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.860/D:    23. 阈值102 | 二值化:102 | 黑色提取:#000000 到 #666666 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.863/D:    24. 阈值103 | 二值化:103 | 黑色提取:#000000 到 #676767 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.864/D:    25. 阈值104 | 二值化:104 | 黑色提取:#000000 到 #686868 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.868/D:    26. 阈值105 | 二值化:105 | 黑色提取:#000000 到 #696969 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.868/D:    27. 阈值106 | 二值化:106 | 黑色提取:#000000 到 #6a6a6a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.869/D:    28. 阈值107 | 二值化:107 | 黑色提取:#000000 到 #6b6b6b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.870/D:    29. 阈值108 | 二值化:108 | 黑色提取:#000000 到 #6c6c6c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.870/D:    30. 阈值109 | 二值化:109 | 黑色提取:#000000 到 #6d6d6d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.871/D:    31. 阈值110 | 二值化:110 | 黑色提取:#000000 到 #6e6e6e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.872/D:    32. 阈值111 | 二值化:111 | 黑色提取:#000000 到 #6f6f6f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.872/D:    33. 阈值112 | 二值化:112 | 黑色提取:#000000 到 #707070 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.873/D:    34. 阈值113 | 二值化:113 | 黑色提取:#000000 到 #717171 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.873/D:    35. 阈值114 | 二值化:114 | 黑色提取:#000000 到 #727272 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.874/D:    36. 阈值115 | 二值化:115 | 黑色提取:#000000 到 #737373 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.875/D:    37. 阈值116 | 二值化:116 | 黑色提取:#000000 到 #747474 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.875/D:    38. 阈值117 | 二值化:117 | 黑色提取:#000000 到 #757575 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.876/D:    39. 阈值118 | 二值化:118 | 黑色提取:#000000 到 #767676 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.877/D:    40. 阈值119 | 二值化:119 | 黑色提取:#000000 到 #777777 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.878/D:    41. 阈值120 | 二值化:120 | 黑色提取:#000000 到 #787878 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.879/D:    42. 阈值121 | 二值化:121 | 黑色提取:#000000 到 #797979 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.879/D:    43. 阈值122 | 二值化:122 | 黑色提取:#000000 到 #7a7a7a | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.880/D:    44. 阈值123 | 二值化:123 | 黑色提取:#000000 到 #7b7b7b | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.880/D:    45. 阈值124 | 二值化:124 | 黑色提取:#000000 到 #7c7c7c | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.881/D:    46. 阈值125 | 二值化:125 | 黑色提取:#000000 到 #7d7d7d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.882/D:    47. 阈值126 | 二值化:126 | 黑色提取:#000000 到 #7e7e7e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.882/D:    48. 阈值127 | 二值化:127 | 黑色提取:#000000 到 #7f7f7f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.883/D:    49. 阈值128 | 二值化:128 | 黑色提取:#000000 到 #808080 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.884/D:    50. 阈值129 | 二值化:129 | 黑色提取:#000000 到 #818181 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.884/D:    51. 阈值130 | 二值化:130 | 黑色提取:#000000 到 #828282 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.885/D:    52. 阈值131 | 二值化:131 | 黑色提取:#000000 到 #838383 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:49.886/D:    53. 阈值132 | 二值化:132 | 黑色提取:#000000 到 #848484 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:49.886/D:    54. 阈值133 | 二值化:133 | 黑色提取:#000000 到 #858585 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:49.887/D:    55. 阈值134 | 二值化:134 | 黑色提取:#000000 到 #868686 | 识别:[122] | 纯数字:[122] | 字符数:3
12:30:49.888/D:    56. 阈值135 | 二值化:135 | 黑色提取:#000000 到 #878787 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.888/D:    57. 阈值136 | 二值化:136 | 黑色提取:#000000 到 #888888 | 识别:[7
22
1] | 纯数字:[7221] | 字符数:4
12:30:49.889/D:    58. 阈值137 | 二值化:137 | 黑色提取:#000000 到 #898989 | 识别:[7
22
1 4] | 纯数字:[72214] | 字符数:5
12:30:49.889/D:    59. 阈值138 | 二值化:138 | 黑色提取:#000000 到 #8a8a8a | 识别:[7
22
3 4] | 纯数字:[72234] | 字符数:5
12:30:49.890/D:    60. 阈值139 | 二值化:139 | 黑色提取:#000000 到 #8b8b8b | 识别:[4
22
4] | 纯数字:[4224] | 字符数:4
12:30:49.891/D:    61. 阈值140 | 二值化:140 | 黑色提取:#000000 到 #8c8c8c | 识别:[2
4] | 纯数字:[24] | 字符数:2
12:30:49.892/D:    62. 阈值141 | 二值化:141 | 黑色提取:#000000 到 #8d8d8d | 识别:[22
8 4] | 纯数字:[2284] | 字符数:4
12:30:49.892/D:    63. 阈值142 | 二值化:142 | 黑色提取:#000000 到 #8e8e8e | 识别:[22
8 4] | 纯数字:[2284] | 字符数:4
12:30:49.893/D:    64. 阈值143 | 二值化:143 | 黑色提取:#000000 到 #8f8f8f | 识别:[7
22
4] | 纯数字:[7224] | 字符数:4
12:30:49.893/D:    65. 阈值144 | 二值化:144 | 黑色提取:#000000 到 #909090 | 识别:[7
22
4] | 纯数字:[7224] | 字符数:4
12:30:49.894/D:    66. 阈值145 | 二值化:145 | 黑色提取:#000000 到 #919191 | 识别:[2
3 4] | 纯数字:[234] | 字符数:3
12:30:49.895/D:    67. 阈值146 | 二值化:146 | 黑色提取:#000000 到 #929292 | 识别:[22
4] | 纯数字:[224] | 字符数:3
12:30:49.895/D:    68. 阈值147 | 二值化:147 | 黑色提取:#000000 到 #939393 | 识别:[23
4] | 纯数字:[234] | 字符数:3
12:30:49.896/D:    69. 阈值148 | 二值化:148 | 黑色提取:#000000 到 #949494 | 识别:[722  
4] | 纯数字:[7224] | 字符数:4
12:30:49.896/D:    70. 阈值149 | 二值化:149 | 黑色提取:#000000 到 #959595 | 识别:[7221 
1] | 纯数字:[72211] | 字符数:5
12:30:49.899/D:    71. 阈值150 | 二值化:150 | 黑色提取:#000000 到 #969696 | 识别:[71
 22] | 纯数字:[7122] | 字符数:4
12:30:49.900/D:    72. 阈值151 | 二值化:151 | 黑色提取:#000000 到 #979797 | 识别:[71
 22] | 纯数字:[7122] | 字符数:4
12:30:49.901/D:    73. 阈值152 | 二值化:152 | 黑色提取:#000000 到 #989898 | 识别:[63241] | 纯数字:[63241] | 字符数:5
12:30:49.902/D:    74. 阈值153 | 二值化:153 | 黑色提取:#000000 到 #999999 | 识别:[6311] | 纯数字:[6311] | 字符数:4
12:30:49.902/D:    75. 阈值154 | 二值化:154 | 黑色提取:#000000 到 #9a9a9a | 识别:[6311] | 纯数字:[6311] | 字符数:4
12:30:49.903/D:    76. 阈值155 | 二值化:155 | 黑色提取:#000000 到 #9b9b9b | 识别:[1
6311] | 纯数字:[16311] | 字符数:5
12:30:49.905/D:    77. 阈值156 | 二值化:156 | 黑色提取:#000000 到 #9c9c9c | 识别:[2 21] | 纯数字:[221] | 字符数:3
12:30:49.905/D:    78. 阈值157 | 二值化:157 | 黑色提取:#000000 到 #9d9d9d | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.906/D:    79. 阈值158 | 二值化:158 | 黑色提取:#000000 到 #9e9e9e | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.908/D:    80. 阈值159 | 二值化:159 | 黑色提取:#000000 到 #9f9f9f | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.908/D:    81. 阈值160 | 二值化:160 | 黑色提取:#000000 到 #a0a0a0 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.910/D:    82. 阈值161 | 二值化:161 | 黑色提取:#000000 到 #a1a1a1 | 识别:[22] | 纯数字:[22] | 字符数:2
12:30:49.911/D:    83. 阈值169 | 二值化:169 | 黑色提取:#000000 到 #a9a9a9 | 识别:[3] | 纯数字:[3] | 字符数:1
12:30:49.912/D: 
🏆 ===== 最佳识别结果 =====
12:30:49.913/D: 最佳阈值: 137
12:30:49.915/D: 处理流程: 二值化(137) → 中值滤波(3x3) → 黑色提取(#000000 到 #898989) → OCR识别
12:30:49.917/D: 识别结果: "72214" (数字字符数:5个)
12:30:49.918/D: 
🎯 最终识别结果: "72214"
12:30:49.919/D: 🔵 蓝色区域结果: 72214
12:30:50.921/D: 
🟡 ===== 黄色区域识别 =====
12:30:50.922/D: 
🎯 开始识别黄色区域 (坐标: 83,625,170,105)
12:30:51.257/D: ✅ 黄色区域OCR初始化完成
12:30:51.259/D: ❌ 黄色区域识别失败: JavaException: java.lang.IllegalStateException: image has been recycled
12:30:51.259/D: 🟡 黄色区域结果: 识别失败
12:30:52.321/D: 
🟢 ===== 绿色区域识别 =====
12:30:52.322/D: 
🎯 开始识别绿色区域 (坐标: 89,773,170,105)
12:30:52.326/D: ❌ 绿色区域识别失败: JavaException: java.lang.IllegalStateException: image has been recycled
12:30:52.327/D: 🟢 绿色区域结果: 识别失败
12:30:53.329/D: 
🔴 ===== 红色区域识别 =====
12:30:53.333/D: 
🎯 开始识别红色区域 (坐标: 288,775,170,105)
12:30:53.336/D: ❌ 红色区域识别失败: JavaException: java.lang.IllegalStateException: image has been recycled
12:30:53.336/D: 🔴 红色区域结果: 识别失败
12:30:53.338/D: 
🎯 ===== 最终识别结果汇总 =====
12:30:53.339/D: 🔵 蓝色区域: 72214
12:30:53.340/D: 🟡 黄色区域: 识别失败
12:30:53.341/D: 🟢 绿色区域: 识别失败
12:30:53.342/D: 🔴 红色区域: 识别失败
12:30:53.349/D: ❌ 算数主程序执行失败: TypeError: Cannot find function end in object com.googlecode.tesseract.android.TessBaseAPI@6c091b0.
12:30:53.352/E: TypeError: Cannot find function end in object com.googlecode.tesseract.android.TessBaseAPI@6c091b0. (su_main.js#185)
TypeError: Cannot find function end in object com.googlecode.tesseract.android.TessBaseAPI@6c091b0.
    at 算数_主程序 (su_main.js:185:0)
    at su_main.js:195:0

12:30:53.353/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/su_main.js ]运行结束，用时11.806000秒
