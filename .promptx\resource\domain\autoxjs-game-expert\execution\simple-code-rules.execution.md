<execution>
  <constraint>
    ## 简洁代码的客观限制
    - **可读性边界**：代码简洁不能以牺牲可读性为代价
    - **功能完整性**：简化不能影响功能的完整性和正确性
    - **维护成本控制**：过度简化可能增加后期维护难度
    - **团队协作要求**：代码风格需要团队成员都能理解和维护
  </constraint>

  <rule>
    ## 简洁代码强制规则
    - **核心原则强制**：用最少的代码，最灵活的参数，最简洁的命名，解决最多的实际问题
    - **函数命名强制**：动词_名词格式，不超过5个中文字，语义清晰直观
    - **参数设计强制**：全部可调，不硬编码，提供合理默认值
    - **功能整合强制**：一个函数多用途，通过参数控制不同行为
    - **注释极简强制**：只在函数开头用一行说明用法，避免冗余解释
  </rule>

  <guideline>
    ## 简洁代码指导原则
    - **实用性导向**：每个函数都要解决具体的使用场景
    - **直线逻辑偏好**：避免复杂嵌套，按执行顺序组织代码
    - **最少抽象原则**：不创建不必要的中间层和封装
    - **性能考虑**：及时释放资源，避免内存泄漏
    - **灵活性保证**：支持多种路径格式，时间参数可调节
  </guideline>

  <process>
    ## 简洁代码编写完整流程
    
    ### 第一步：函数命名规范
    
    #### 命名格式标准
    ```javascript
    // ✅ 推荐格式：动词_名词
    function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) { }
    function 登陆_帐号(用户名, 密码, 记住登录) { }
    function 检查_邮箱界面(等待时间) { }
    
    // ✅ 无动词时：纯名词
    function 邮箱(获取方式) { }
    function 密码(加密类型) { }
    function 界面(页面类型) { }
    
    // ❌ 避免的复杂命名
    function 基础找图功能() { }
    function 找图并点击功能() { }
    function 等待图片出现功能() { }
    ```
    
    #### 命名质量检查
    - **字数限制**：不超过5个中文字
    - **语义清晰**：函数名直接说明功能，一看就懂
    - **动词准确**：选择最准确的动词表达操作意图
    - **名词具体**：使用具体的名词而非抽象概念
    
    ### 第二步：参数设计原则
    
    #### 参数灵活化设计
    ```javascript
    // ✅ 全部可调的参数设计
    function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) {
        var 等待时间 = 等待秒数 || 10;  // 默认10秒
        var 检查间隔 = 间隔毫秒 || 500;  // 默认500毫秒
        var 执行动作 = 动作 || "";  // 默认不执行动作
        
        // 支持相对路径、绝对路径、任意格式
        var 图片 = images.read(图片路径);
        
        // 根据动作参数决定行为
        if (执行动作 === "点击") {
            // 执行点击操作
        }
        
        return 结果;
    }
    
    // ❌ 硬编码的参数设计
    function 找图并点击() {
        sleep(1000);  // 硬编码等待时间
        var 图片 = images.read("./assets/button.png");  // 硬编码路径
    }
    ```
    
    #### 参数命名规范
    - **语义清晰**：`等待秒数`比`timeout`更直观
    - **单位明确**：`间隔毫秒`、`等待秒数`明确时间单位
    - **类型暗示**：`是否记住`暗示布尔类型
    - **默认值合理**：提供常用的默认值
    
    ### 第三步：功能整合原则
    
    #### 一个函数多用途
    ```javascript
    // ✅ 整合多种功能的设计
    function 处理_界面元素(元素类型, 操作方式, 参数对象) {
        var 默认参数 = {
            等待时间: 10,
            重试次数: 3,
            点击偏移: {x: 0, y: 0}
        };
        
        var 配置 = Object.assign(默认参数, 参数对象 || {});
        
        switch (元素类型) {
            case "按钮":
                return 处理按钮(操作方式, 配置);
            case "输入框":
                return 处理输入框(操作方式, 配置);
            case "图片":
                return 处理图片(操作方式, 配置);
            default:
                return false;
        }
    }
    
    // ❌ 分散的功能设计
    function 点击按钮() { }
    function 等待按钮() { }
    function 查找按钮() { }
    ```
    
    #### 参数决定行为
    ```javascript
    // ✅ 通过参数控制行为
    function 图片_操作(图片路径, 操作类型, 配置) {
        var 结果 = 查找图片(图片路径, 配置.等待时间);
        
        if (!结果) return null;
        
        switch (操作类型) {
            case "点击":
                click(结果.x + 结果.width/2, 结果.y + 结果.height/2);
                break;
            case "长按":
                longClick(结果.x + 结果.width/2, 结果.y + 结果.height/2);
                break;
            case "获取坐标":
                return {x: 结果.x, y: 结果.y};
            default:
                return 结果;
        }
    }
    ```
    
    ### 第四步：代码结构原则
    
    #### 直线逻辑组织
    ```javascript
    // ✅ 直线逻辑的代码结构
    function 登录_流程(用户名, 密码, 选项) {
        // 第一步：检查登录界面
        if (!检查_登录界面()) {
            return {成功: false, 原因: "未找到登录界面"};
        }
        
        // 第二步：输入用户名
        if (!输入_用户名(用户名)) {
            return {成功: false, 原因: "用户名输入失败"};
        }
        
        // 第三步：输入密码
        if (!输入_密码(密码)) {
            return {成功: false, 原因: "密码输入失败"};
        }
        
        // 第四步：点击登录
        if (!点击_登录按钮()) {
            return {成功: false, 原因: "登录按钮点击失败"};
        }
        
        // 第五步：等待结果
        return 等待_登录结果(选项.等待时间);
    }
    
    // ❌ 复杂嵌套的代码结构
    function 复杂登录() {
        if (检查界面()) {
            if (输入用户名()) {
                if (输入密码()) {
                    if (点击登录()) {
                        // 深层嵌套...
                    }
                }
            }
        }
    }
    ```
    
    #### 核心逻辑集中
    ```javascript
    // ✅ 核心逻辑集中管理
    function 游戏_自动化(配置对象) {
        var 默认配置 = {
            任务列表: ["日常任务", "活动任务"],
            执行间隔: 1000,
            最大运行时间: 3600000  // 1小时
        };
        
        var 运行配置 = Object.assign(默认配置, 配置对象);
        var 开始时间 = new Date().getTime();
        
        for (var i = 0; i < 运行配置.任务列表.length; i++) {
            var 任务 = 运行配置.任务列表[i];
            
            if (new Date().getTime() - 开始时间 > 运行配置.最大运行时间) {
                break;  // 超时退出
            }
            
            执行_任务(任务, 运行配置);
            sleep(运行配置.执行间隔);
        }
    }
    ```
    
    ### 第五步：注释极简原则
    
    #### 极简注释标准
    ```javascript
    // ✅ 极简注释示例
    // 查找图片并执行指定操作，支持点击、长按、获取坐标
    function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) {
        // 实现代码...
    }
    
    // 处理游戏登录流程，返回登录结果状态
    function 登录_游戏(用户名, 密码, 记住登录) {
        // 实现代码...
    }
    
    // ❌ 冗余注释示例
    /**
     * 查找图片功能
     * @param {string} 图片路径 - 图片文件的路径
     * @param {string} 动作 - 要执行的动作类型
     * @param {number} 等待秒数 - 等待的秒数
     * @param {number} 间隔毫秒 - 检查间隔的毫秒数
     * @returns {object} 返回查找结果对象
     */
    function 查找_图片(图片路径, 动作, 等待秒数, 间隔毫秒) {
        // 实现代码...
    }
    ```
    
    ### 第六步：质量检查流程
    
    #### 简洁性检查清单
    - [ ] 函数名是否符合动词_名词格式且不超过5个字？
    - [ ] 参数是否全部可调且有合理默认值？
    - [ ] 是否避免了硬编码的值？
    - [ ] 一个函数是否整合了多种相关功能？
    - [ ] 代码逻辑是否按直线顺序组织？
    - [ ] 注释是否极简且只说明用法？
    - [ ] 是否及时释放了资源？
    - [ ] 返回值是否有用且明确？
  </process>

  <criteria>
    ## 简洁代码质量标准
    
    ### 命名规范性
    - ✅ 函数名符合动词_名词格式≥95%
    - ✅ 函数名字数≤5个中文字100%
    - ✅ 参数名语义清晰度≥90%
    - ✅ 变量名自解释性≥85%
    
    ### 参数灵活性
    - ✅ 硬编码值使用率≤5%
    - ✅ 参数可调节性≥90%
    - ✅ 默认值合理性≥95%
    - ✅ 路径格式支持度≥3种
    
    ### 功能整合度
    - ✅ 单函数功能整合度≥80%
    - ✅ 重复逻辑消除率≥95%
    - ✅ 调用步骤简化度≥70%
    - ✅ 参数控制行为覆盖率≥85%
    
    ### 代码简洁性
    - ✅ 代码行数减少率≥30%
    - ✅ 嵌套层级≤3层
    - ✅ 注释冗余度≤10%
    - ✅ 抽象层次合理性≥90%
  </criteria>
</execution>
