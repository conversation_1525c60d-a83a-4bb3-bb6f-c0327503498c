/**
 * Font Awesome 5.15.2 精选Unicode图标库
 * 来源: https://fa.uutool.cn/unicode/5.15.2/
 * 包含最实用和美观的图标，按功能分类，并配置语义化颜色
 * 颜色设计遵循Material Design和Apple HIG指南
 */

"ui";

// 语义化颜色配置 - 基于UI设计最佳实践
var 图标颜色配置 = {
    // 基础语义颜色
    成功: "#4CAF50",      // 绿色 - 成功、完成、正确
    错误: "#F44336",      // 红色 - 错误、危险、删除
    警告: "#FF9800",      // 橙色 - 警告、注意
    信息: "#2196F3",      // 蓝色 - 信息、链接、主要操作
    中性: "#757575",      // 灰色 - 中性、次要信息

    // 功能性颜色
    导航: "#1976D2",      // 深蓝 - 导航、结构
    操作: "#4CAF50",      // 绿色 - 操作、交互
    媒体: "#9C27B0",      // 紫色 - 媒体、娱乐
    状态: {
        成功: "#4CAF50",
        错误: "#F44336",
        警告: "#FF9800",
        信息: "#2196F3"
    },

    // 分类颜色
    设备: "#607D8B",      // 蓝灰 - 科技、设备
    文件: "#795548",      // 棕色 - 文档、文件
    通信: "#00BCD4",      // 青色 - 通信、连接
    用户: "#3F51B5",      // 靛蓝 - 用户、人员
    商业: "#4CAF50",      // 绿色 - 商业、金钱
    工具: "#FF5722",      // 深橙 - 工具、设置
    时间: "#9E9E9E",      // 灰色 - 时间、历史
    天气: "#03A9F4",      // 浅蓝 - 天气、自然
    交通: "#FF9800",      // 橙色 - 交通、移动
    安全: "#F44336",      // 红色 - 安全、保护
    医疗: "#E91E63",      // 粉红 - 医疗、健康
    教育: "#673AB7",      // 深紫 - 教育、学习
    娱乐: "#E91E63",      // 粉红 - 娱乐、游戏
    食物: "#FF5722",      // 深橙 - 食物、餐饮
    运动: "#FF9800",      // 橙色 - 运动、活力
    自然: "#4CAF50",      // 绿色 - 自然、环保
    建筑: "#795548"       // 棕色 - 建筑、结构
};

// Font Awesome Unicode图标库
var FontAwesome图标 = {
    
    // =============== 导航类图标 ===============
    导航: {
        主页: { 字符: "\uf015", 颜色: 图标颜色配置.导航 },           // fa-home f015
        菜单: { 字符: "\uf0c9", 颜色: 图标颜色配置.导航 },           // fa-bars f0c9
        搜索: { 字符: "\uf002", 颜色: 图标颜色配置.信息 },           // fa-search f002
        返回: { 字符: "\uf060", 颜色: 图标颜色配置.导航 },           // fa-arrow-left f060
        前进: { 字符: "\uf061", 颜色: 图标颜色配置.导航 },           // fa-arrow-right f061
        向上: { 字符: "\uf062", 颜色: 图标颜色配置.导航 },           // fa-arrow-up f062
        向下: { 字符: "\uf063", 颜色: 图标颜色配置.导航 },           // fa-arrow-down f063
        关闭: { 字符: "\uf00d", 颜色: 图标颜色配置.错误 },           // fa-times f00d
        展开: { 字符: "\uf065", 颜色: 图标颜色配置.信息 },           // fa-expand f065
        收缩: { 字符: "\uf066", 颜色: 图标颜色配置.信息 },           // fa-compress f066
        更多: { 字符: "\uf141", 颜色: 图标颜色配置.中性 },           // fa-ellipsis-h f141
        定位: { 字符: "\uf124", 颜色: 图标颜色配置.警告 },           // fa-location-arrow f124
    },

    // =============== 操作类图标 ===============
    操作: {
        添加: { 字符: "\uf067", 颜色: 图标颜色配置.成功 },           // fa-plus f067
        删除: { 字符: "\uf1f8", 颜色: 图标颜色配置.错误 },           // fa-trash f1f8
        编辑: { 字符: "\uf044", 颜色: 图标颜色配置.信息 },           // fa-edit f044
        保存: { 字符: "\uf0c7", 颜色: 图标颜色配置.成功 },           // fa-save f0c7
        复制: { 字符: "\uf0c5", 颜色: 图标颜色配置.信息 },           // fa-copy f0c5
        粘贴: { 字符: "\uf0ea", 颜色: 图标颜色配置.信息 },           // fa-paste f0ea
        剪切: { 字符: "\uf0c4", 颜色: 图标颜色配置.警告 },           // fa-cut f0c4
        撤销: { 字符: "\uf0e2", 颜色: 图标颜色配置.中性 },           // fa-undo f0e2
        重做: { 字符: "\uf01e", 颜色: 图标颜色配置.中性 },           // fa-redo f01e
        刷新: { 字符: "\uf021", 颜色: 图标颜色配置.信息 },           // fa-sync f021
        下载: { 字符: "\uf019", 颜色: 图标颜色配置.成功 },           // fa-download f019
        上传: { 字符: "\uf093", 颜色: 图标颜色配置.信息 },           // fa-upload f093
        分享: { 字符: "\uf064", 颜色: 图标颜色配置.信息 },           // fa-share f064
        链接: { 字符: "\uf0c1", 颜色: 图标颜色配置.信息 },           // fa-link f0c1
        解锁: { 字符: "\uf09c", 颜色: 图标颜色配置.成功 },           // fa-unlock f09c
        锁定: { 字符: "\uf023", 颜色: 图标颜色配置.错误 },           // fa-lock f023
    },

    // =============== 媒体控制图标 ===============
    媒体: {
        播放: { 字符: "\uf04b", 颜色: 图标颜色配置.成功 },           // fa-play f04b
        暂停: { 字符: "\uf04c", 颜色: 图标颜色配置.警告 },           // fa-pause f04c
        停止: { 字符: "\uf04d", 颜色: 图标颜色配置.错误 },           // fa-stop f04d
        快进: { 字符: "\uf050", 颜色: 图标颜色配置.媒体 },           // fa-fast-forward f050
        快退: { 字符: "\uf049", 颜色: 图标颜色配置.媒体 },           // fa-fast-backward f049
        上一个: { 字符: "\uf048", 颜色: 图标颜色配置.媒体 },         // fa-step-backward f048
        下一个: { 字符: "\uf051", 颜色: 图标颜色配置.媒体 },         // fa-step-forward f051
        音量开: { 字符: "\uf028", 颜色: 图标颜色配置.信息 },         // fa-volume-up f028
        音量关: { 字符: "\uf026", 颜色: 图标颜色配置.中性 },         // fa-volume-off f026
        静音: { 字符: "\uf6a9", 颜色: 图标颜色配置.错误 },           // fa-volume-mute f6a9
        录音: { 字符: "\uf130", 颜色: 图标颜色配置.错误 },           // fa-microphone f130
        相机: { 字符: "\uf030", 颜色: 图标颜色配置.媒体 },           // fa-camera f030
        视频: { 字符: "\uf03d", 颜色: 图标颜色配置.媒体 },           // fa-video f03d
    },

    // =============== 状态指示图标 ===============
    状态: {
        成功: { 字符: "\uf058", 颜色: 图标颜色配置.状态.成功 },           // fa-check-circle f058
        错误: { 字符: "\uf057", 颜色: 图标颜色配置.状态.错误 },           // fa-times-circle f057
        警告: { 字符: "\uf071", 颜色: 图标颜色配置.状态.警告 },           // fa-exclamation-triangle f071
        信息: { 字符: "\uf05a", 颜色: 图标颜色配置.状态.信息 },           // fa-info-circle f05a
        问号: { 字符: "\uf059", 颜色: 图标颜色配置.状态.信息 },           // fa-question-circle f059
        感叹号: { 字符: "\uf06a", 颜色: 图标颜色配置.状态.警告 },         // fa-exclamation-circle f06a
        对勾: { 字符: "\uf00c", 颜色: 图标颜色配置.状态.成功 },           // fa-check f00c
        叉号: { 字符: "\uf00d", 颜色: 图标颜色配置.状态.错误 },           // fa-times f00d
        星星: { 字符: "\uf005", 颜色: "#FFD700" },           // fa-star f005 - 金色
        心形: { 字符: "\uf004", 颜色: "#E91E63" },           // fa-heart f004 - 粉红色
        拇指向上: { 字符: "\uf164", 颜色: 图标颜色配置.成功 },       // fa-thumbs-up f164
        拇指向下: { 字符: "\uf165", 颜色: 图标颜色配置.错误 },       // fa-thumbs-down f165
    },

    // =============== 设备类图标 ===============
    设备: {
        电脑: { 字符: "\uf108", 颜色: 图标颜色配置.设备 },           // fa-desktop f108
        笔记本: { 字符: "\uf109", 颜色: 图标颜色配置.设备 },         // fa-laptop f109
        手机: { 字符: "\uf10b", 颜色: 图标颜色配置.设备 },           // fa-mobile f10b
        平板: { 字符: "\uf10a", 颜色: 图标颜色配置.设备 },           // fa-tablet f10a
        键盘: { 字符: "\uf11c", 颜色: 图标颜色配置.设备 },           // fa-keyboard f11c
        鼠标: { 字符: "\uf8cc", 颜色: 图标颜色配置.设备 },           // fa-mouse f8cc
        打印机: { 字符: "\uf02f", 颜色: 图标颜色配置.设备 },         // fa-print f02f
        硬盘: { 字符: "\uf0a0", 颜色: 图标颜色配置.设备 },           // fa-hdd f0a0
        内存: { 字符: "\uf538", 颜色: 图标颜色配置.设备 },           // fa-memory f538
        芯片: { 字符: "\uf2db", 颜色: 图标颜色配置.设备 },           // fa-microchip f2db
        服务器: { 字符: "\uf233", 颜色: 图标颜色配置.设备 },         // fa-server f233
        网络: { 字符: "\uf6ff", 颜色: 图标颜色配置.通信 },           // fa-network-wired f6ff
        控制台: { 字符: "\uf120", 颜色: 图标颜色配置.工具 },         // fa-terminal f120
    },

    // =============== 文件类图标 ===============
    文件: {
        文件: { 字符: "\uf15b", 颜色: 图标颜色配置.文件 },           // fa-file f15b
        文档: { 字符: "\uf15c", 颜色: 图标颜色配置.文件 },           // fa-file-alt f15c
        文件夹: { 字符: "\uf07b", 颜色: 图标颜色配置.文件 },         // fa-folder f07b
        打开文件夹: { 字符: "\uf07c", 颜色: 图标颜色配置.文件 },     // fa-folder-open f07c
        图片: { 字符: "\uf03e", 颜色: "#9C27B0" },           // fa-image f03e - 紫色
        音频: { 字符: "\uf1c7", 颜色: "#FF5722" },           // fa-file-audio f1c7 - 深橙
        视频: { 字符: "\uf1c8", 颜色: "#E91E63" },           // fa-file-video f1c8 - 粉红
        PDF: { 字符: "\uf1c1", 颜色: "#F44336" },            // fa-file-pdf f1c1 - 红色
        Word: { 字符: "\uf1c2", 颜色: "#2196F3" },           // fa-file-word f1c2 - 蓝色
        Excel: { 字符: "\uf1c3", 颜色: "#4CAF50" },          // fa-file-excel f1c3 - 绿色
        PPT: { 字符: "\uf1c4", 颜色: "#FF9800" },            // fa-file-powerpoint f1c4 - 橙色
        压缩包: { 字符: "\uf1c6", 颜色: "#795548" },         // fa-file-archive f1c6 - 棕色
        代码: { 字符: "\uf1c9", 颜色: "#607D8B" },           // fa-file-code f1c9 - 蓝灰
    },

    // =============== 通信类图标 ===============
    通信: {
        邮件: { 字符: "\uf0e0", 颜色: 图标颜色配置.通信 },           // fa-envelope f0e0
        消息: { 字符: "\uf075", 颜色: 图标颜色配置.通信 },           // fa-comment f075
        聊天: { 字符: "\uf086", 颜色: 图标颜色配置.通信 },           // fa-comments f086
        电话: { 字符: "\uf095", 颜色: 图标颜色配置.成功 },           // fa-phone f095
        传真: { 字符: "\uf1ac", 颜色: 图标颜色配置.中性 },           // fa-fax f1ac
        铃铛: { 字符: "\uf0f3", 颜色: 图标颜色配置.警告 },           // fa-bell f0f3
        通知: { 字符: "\uf0a1", 颜色: 图标颜色配置.信息 },           // fa-bullhorn f0a1
        RSS: { 字符: "\uf09e", 颜色: 图标颜色配置.警告 },            // fa-rss f09e
        WiFi: { 字符: "\uf1eb", 颜色: 图标颜色配置.成功 },           // fa-wifi f1eb
        蓝牙: { 字符: "\uf293", 颜色: 图标颜色配置.信息 },           // fa-bluetooth f293
        信号: { 字符: "\uf012", 颜色: 图标颜色配置.成功 },           // fa-signal f012
    },

    // =============== 用户类图标 ===============
    用户: {
        用户: "\uf007",           // fa-user f007
        用户组: "\uf0c0",         // fa-users f0c0
        用户圈: "\uf2bd",         // fa-user-circle f2bd
        用户加: "\uf234",         // fa-user-plus f234
        用户减: "\uf503",         // fa-user-minus f503
        用户编辑: "\uf4ff",       // fa-user-edit f4ff
        用户设置: "\uf4fe",       // fa-user-cog f4fe
        用户检查: "\uf4fc",       // fa-user-check f4fc
        用户锁定: "\uf502",       // fa-user-lock f502
        用户屏蔽: "\uf506",       // fa-user-slash f506
        管理员: "\uf21b",         // fa-user-secret f21b
        医生: "\uf0f0",           // fa-user-md f0f0
    },

    // =============== 商业类图标 ===============
    商业: {
        购物车: "\uf07a",         // fa-shopping-cart f07a
        购物袋: "\uf290",         // fa-shopping-bag f290
        信用卡: "\uf09d",         // fa-credit-card f09d
        钱币: "\uf51e",           // fa-coins f51e
        钞票: "\uf0d6",           // fa-money-bill f0d6
        银行: "\uf19c",           // fa-university f19c
        图表: "\uf080",           // fa-chart-bar f080
        趋势: "\uf201",           // fa-chart-line f201
        饼图: "\uf200",           // fa-chart-pie f200
        计算器: "\uf1ec",         // fa-calculator f1ec
        收据: "\uf543",           // fa-receipt f543
        标签: "\uf02b",           // fa-tag f02b
    },

    // =============== 工具类图标 ===============
    工具: {
        设置: "\uf013",           // fa-cog f013
        工具: "\uf7d9",           // fa-tools f7d9
        扳手: "\uf0ad",           // fa-wrench f0ad
        螺丝刀: "\uf54a",         // fa-screwdriver f54a
        锤子: "\uf6e3",           // fa-hammer f6e3
        魔法棒: "\uf0d0",         // fa-magic f0d0
        调色板: "\uf53f",         // fa-palette f53f
        画笔: "\uf1fc",           // fa-paint-brush f1fc
        滴管: "\uf1fb",           // fa-eye-dropper f1fb
        尺子: "\uf545",           // fa-ruler f545
        指南针: "\uf14e",         // fa-compass f14e
        放大镜: "\uf002",         // fa-search f002
    },

    // =============== 时间类图标 ===============
    时间: {
        时钟: "\uf017",           // fa-clock f017
        日历: "\uf073",           // fa-calendar-alt f073
        历史: "\uf1da",           // fa-history f1da
        秒表: "\uf2f2",           // fa-stopwatch f2f2
        闹钟: "\uf0f3",           // fa-bell f0f3
        沙漏: "\uf254",           // fa-hourglass f254
        定时器: "\uf2f2",         // fa-stopwatch f2f2
    },

    // =============== 天气类图标 ===============
    天气: {
        太阳: "\uf185",           // fa-sun f185
        月亮: "\uf186",           // fa-moon f186
        云朵: "\uf0c2",           // fa-cloud f0c2
        云: "\uf0c2",             // fa-cloud f0c2 (别名)
        雨天: "\uf73d",           // fa-cloud-rain f73d
        雪花: "\uf2dc",           // fa-snowflake f2dc
        闪电: "\uf0e7",           // fa-bolt f0e7
        温度计: "\uf491",         // fa-thermometer f491
        风: "\uf72e",             // fa-wind f72e
    },

    // =============== 交通类图标 ===============
    交通: {
        汽车: "\uf1b9",           // fa-car f1b9
        卡车: "\uf0d1",           // fa-truck f0d1
        公交: "\uf207",           // fa-bus f207
        火车: "\uf238",           // fa-train f238
        飞机: "\uf072",           // fa-plane f072
        船只: "\uf21a",           // fa-ship f21a
        自行车: "\uf206",         // fa-bicycle f206
        摩托车: "\uf21c",         // fa-motorcycle f21c
        出租车: "\uf1ba",         // fa-taxi f1ba
        地铁: "\uf239",           // fa-subway f239
        加油站: "\uf52f",         // fa-gas-pump f52f
        停车: "\uf540",           // fa-parking f540
    },

    // =============== 安全类图标 ===============
    安全: {
        盾牌: "\uf3ed",           // fa-shield-alt f3ed
        钥匙: "\uf084",           // fa-key f084
        指纹: "\uf577",           // fa-fingerprint f577
        眼睛: "\uf06e",           // fa-eye f06e
        眼睛关闭: "\uf070",       // fa-eye-slash f070
        密码: "\uf023",           // fa-lock f023
        解锁: "\uf09c",           // fa-unlock f09c
        证书: "\uf0a3",           // fa-certificate f0a3
        徽章: "\uf2c1",           // fa-id-badge f2c1
        身份证: "\uf2c2",         // fa-id-card f2c2
        安全帽: "\uf807",         // fa-hard-hat f807
        生化危险: "\uf780",       // fa-biohazard f780
        病毒: "\ue074",           // fa-virus e074
    },

    // =============== 医疗类图标 ===============
    医疗: {
        医院: "\uf0f8",           // fa-hospital f0f8
        救护车: "\uf0f9",         // fa-ambulance f0f9
        听诊器: "\uf0f1",         // fa-stethoscope f0f1
        药丸: "\uf484",           // fa-pills f484
        注射器: "\uf48e",         // fa-syringe f48e
        体温计: "\uf491",         // fa-thermometer f491
        心跳: "\uf21e",           // fa-heartbeat f21e
        DNA: "\uf471",            // fa-dna f471
        显微镜: "\uf610",         // fa-microscope f610
        X光: "\uf497",            // fa-x-ray f497
        轮椅: "\uf193",           // fa-wheelchair f193
        急救箱: "\uf479",         // fa-first-aid f479
    },

    // =============== 教育类图标 ===============
    教育: {
        学校: "\uf549",           // fa-school f549
        毕业帽: "\uf19d",         // fa-graduation-cap f19d
        书本: "\uf02d",           // fa-book f02d
        打开的书: "\uf518",       // fa-book-open f518
        书签: "\uf02e",           // fa-bookmark f02e
        铅笔: "\uf303",           // fa-pencil-alt f303
        钢笔: "\uf304",           // fa-pen f304
        黑板: "\uf51b",           // fa-chalkboard f51b
        老师: "\uf51c",           // fa-chalkboard-teacher f51c
        学生: "\uf501",           // fa-user-graduate f501
        苹果: "\uf5d1",           // fa-apple-alt f5d1
        地球仪: "\uf0ac",         // fa-globe f0ac
    },

    // =============== 娱乐类图标 ===============
    娱乐: {
        游戏手柄: "\uf11b",       // fa-gamepad f11b
        Xbox: "\uf412",           // fa-xbox f412
        骰子: "\uf522",           // fa-dice f522
        扑克: "\uf2dd",           // fa-superpowers f2dd
        音乐: "\uf001",           // fa-music f001
        耳机: "\uf025",           // fa-headphones f025
        吉他: "\uf7a6",           // fa-guitar f7a6
        电影: "\uf008",           // fa-film f008
        票: "\uf3ff",             // fa-ticket-alt f3ff
        奖杯: "\uf091",           // fa-trophy f091
        礼物: "\uf06b",           // fa-gift f06b
        生日蛋糕: "\uf1fd",       // fa-birthday-cake f1fd
        气球: "\uf0ac",           // fa-globe f0ac
    },

    // =============== 食物类图标 ===============
    食物: {
        餐具: "\uf2e7",           // fa-utensils f2e7
        咖啡: "\uf0f4",           // fa-coffee f0f4
        啤酒: "\uf0fc",           // fa-beer f0fc
        汉堡: "\uf805",           // fa-hamburger f805
        披萨: "\uf818",           // fa-pizza-slice f818
        苹果: "\uf5d1",           // fa-apple-alt f5d1
        胡萝卜: "\uf787",         // fa-carrot f787
        面包: "\uf7ec",           // fa-bread-slice f7ec
        奶酪: "\uf7ef",           // fa-cheese f7ef
        鸡蛋: "\uf7fb",           // fa-egg f7fb
        鱼: "\uf578",             // fa-fish f578
        冰淇淋: "\uf810",         // fa-ice-cream f810
    },

    // =============== 运动类图标 ===============
    运动: {
        足球: "\uf1e3",           // fa-futbol f1e3
        篮球: "\uf434",           // fa-basketball-ball f434
        棒球: "\uf433",           // fa-baseball-ball f433
        网球: "\uf45d",           // fa-table-tennis f45d
        排球: "\uf45f",           // fa-volleyball-ball f45f
        高尔夫: "\uf450",         // fa-golf-ball f450
        保龄球: "\uf436",         // fa-bowling-ball f436
        游泳: "\uf5c4",           // fa-swimmer f5c4
        跑步: "\uf70c",           // fa-running f70c
        骑行: "\uf206",           // fa-bicycle f206
        滑雪: "\uf7c9",           // fa-skiing f7c9
        哑铃: "\uf44b",           // fa-dumbbell f44b
    },

    // =============== 自然类图标 ===============
    自然: {
        树: "\uf1bb",             // fa-tree f1bb
        叶子: "\uf06c",           // fa-leaf f06c
        花: "\uf4d8",             // fa-seedling f4d8
        山: "\uf6fc",             // fa-mountain f6fc
        水: "\uf773",             // fa-water f773
        火: "\uf06d",             // fa-fire f06d
        地球: "\uf57c",           // fa-globe-africa f57c
        星星: "\uf005",           // fa-star f005
        彩虹: "\uf75b",           // fa-rainbow f75b
        蝴蝶: "\uf469",           // fa-briefcase-medical f469
        猫: "\uf6be",             // fa-cat f6be
        狗: "\uf6d3",             // fa-dog f6d3
    },

    // =============== 建筑类图标 ===============
    建筑: {
        房子: "\uf015",           // fa-home f015
        建筑: "\uf1ad",           // fa-building f1ad
        城市: "\uf64f",           // fa-city f64f
        教堂: "\uf51d",           // fa-church f51d
        清真寺: "\uf678",         // fa-mosque f678
        工厂: "\uf275",           // fa-industry f275
        桥梁: "\uf557",           // fa-archway f557
        塔: "\uf519",             // fa-broadcast-tower f519
        灯塔: "\uf66f",           // fa-landmark f66f
        帐篷: "\uf6bb",           // fa-campground f6bb
        仓库: "\uf494",           // fa-warehouse f494
        商店: "\uf54e",           // fa-store f54e
    }
};

// 便捷访问方法
var FA图标 = {
    /**
     * 获取图标Unicode字符
     * @param {String} 分类 - 图标分类
     * @param {String} 名称 - 图标名称
     * @returns {String} Unicode字符
     */
    获取图标: function(分类, 名称) {
        if (FontAwesome图标[分类] && FontAwesome图标[分类][名称]) {
            var 图标数据 = FontAwesome图标[分类][名称];
            // 兼容旧格式（字符串）和新格式（对象）
            return typeof 图标数据 === 'string' ? 图标数据 : 图标数据.字符;
        }
        console.warn("图标不存在: " + 分类 + "." + 名称);
        return "";
    },

    /**
     * 获取图标颜色
     * @param {String} 分类 - 图标分类
     * @param {String} 名称 - 图标名称
     * @returns {String} 颜色值
     */
    获取图标颜色: function(分类, 名称) {
        if (FontAwesome图标[分类] && FontAwesome图标[分类][名称]) {
            var 图标数据 = FontAwesome图标[分类][名称];
            if (typeof 图标数据 === 'object' && 图标数据.颜色) {
                return 图标数据.颜色;
            }
        }
        return 图标颜色配置.中性; // 默认颜色
    },

    /**
     * 获取完整图标信息
     * @param {String} 分类 - 图标分类
     * @param {String} 名称 - 图标名称
     * @returns {Object} 包含字符和颜色的对象
     */
    获取完整图标: function(分类, 名称) {
        if (FontAwesome图标[分类] && FontAwesome图标[分类][名称]) {
            var 图标数据 = FontAwesome图标[分类][名称];
            if (typeof 图标数据 === 'string') {
                return {
                    字符: 图标数据,
                    颜色: 图标颜色配置.中性
                };
            } else {
                return {
                    字符: 图标数据.字符,
                    颜色: 图标数据.颜色
                };
            }
        }
        console.warn("图标不存在: " + 分类 + "." + 名称);
        return { 字符: "", 颜色: 图标颜色配置.中性 };
    },

    /**
     * 获取所有分类
     * @returns {Array} 分类列表
     */
    获取分类列表: function() {
        return Object.keys(FontAwesome图标);
    },

    /**
     * 获取指定分类的所有图标
     * @param {String} 分类 - 图标分类
     * @returns {Object} 图标对象
     */
    获取分类图标: function(分类) {
        return FontAwesome图标[分类] || {};
    },

    /**
     * 搜索图标
     * @param {String} 关键词 - 搜索关键词
     * @returns {Array} 匹配的图标列表
     */
    搜索图标: function(关键词) {
        var 结果 = [];
        for (var 分类 in FontAwesome图标) {
            for (var 名称 in FontAwesome图标[分类]) {
                if (名称.indexOf(关键词) !== -1 || 分类.indexOf(关键词) !== -1) {
                    结果.push({
                        分类: 分类,
                        名称: 名称,
                        字符: FontAwesome图标[分类][名称]
                    });
                }
            }
        }
        return 结果;
    }
};

// 导出模块
module.exports = {
    FontAwesome图标: FontAwesome图标,
    FA图标: FA图标,
    图标颜色配置: 图标颜色配置,

    // 便捷方法
    获取图标: function(分类, 名称) {
        return FA图标.获取图标(分类, 名称);
    },

    获取图标颜色: function(分类, 名称) {
        return FA图标.获取图标颜色(分类, 名称);
    },

    获取完整图标: function(分类, 名称) {
        return FA图标.获取完整图标(分类, 名称);
    },

    搜索图标: function(关键词) {
        return FA图标.搜索图标(关键词);
    },

    /**
     * 创建带颜色的图标HTML
     * @param {String} 分类 - 图标分类
     * @param {String} 名称 - 图标名称
     * @param {String} 自定义颜色 - 可选的自定义颜色
     * @param {String} 大小 - 可选的字体大小
     * @returns {String} HTML字符串
     */
    创建彩色图标: function(分类, 名称, 自定义颜色, 大小) {
        var 图标信息 = FA图标.获取完整图标(分类, 名称);
        var 颜色 = 自定义颜色 || 图标信息.颜色;
        var 字体大小 = 大小 || "16sp";

        return '<text text="' + 图标信息.字符 + '" textColor="' + 颜色 + '" textSize="' + 字体大小 + '"/>';
    }
};
