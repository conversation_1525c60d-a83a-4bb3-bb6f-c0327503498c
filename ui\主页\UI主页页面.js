/**
 * Magic 游戏辅助脚本 - 主页界面
 * 基于AutoXjs原生UI组件开发
 * 使用统一的#00A843绿色主题
 */

// 导入全局样式
var 全局样式 = require('../全局样式/公共样式.js');

// 主页界面XML布局
var 主页布局 = (
    <frame w="*" h="*">
        {/* 主页面内容 */}
        <vertical w="*" h="*">
        {/* 顶部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h={全局样式.尺寸规范.工具栏高度}
            gravity="center_vertical"
            elevation="4dp">

            <button
                id="菜单按钮"
                text="☰"
                background="#00000000"
                textColor={全局样式.颜色主题.白色}
                textSize="22sp"
                w="56dp"
                h="56dp"
                gravity="center"/>

            <text
                id="页面标题"
                text="主页"
                textColor={全局样式.颜色主题.白色}
                textSize={全局样式.尺寸规范.字体超大}
                layout_weight="1"
                gravity="center"
                textStyle="bold"/>

            <View w="56dp" h="56dp"/>
        </horizontal>

        {/* 主要内容区域 */}
        <ScrollView
            layout_weight="1"
            background={全局样式.颜色主题.背景主色}>

            <vertical padding={全局样式.尺寸规范.间距中}>
                {/* 游戏数据卡片 */}
                <card
                    id="游戏数据卡片"
                    cardBackgroundColor={全局样式.颜色主题.背景次色}
                    cardCornerRadius="16dp"
                    cardElevation="2dp"
                    margin="0 0 0 16dp">

                    <vertical>
                        {/* 卡片标题栏 */}
                        <horizontal
                            padding={全局样式.尺寸规范.间距中}
                            gravity="center_vertical">

                            <text
                                text="游戏数据"
                                textSize={全局样式.尺寸规范.字体大}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"
                                fontWeight="bold"/>

                            <button
                                id="刷新按钮"
                                background="#00000000"
                                textSize="16sp"
                                w="40dp"
                                h="40dp"
                                gravity="center"
                                margin="0 0 8dp 0"/>

                            <button
                                id="删除数据按钮"
                                background="#00000000"
                                textSize="16sp"
                                w="40dp"
                                h="40dp"
                                gravity="center"/>
                        </horizontal>

                        {/* 数据内容区域 */}
                        <ScrollView
                            id="游戏数据滚动区域"
                            h="200dp">

                            <vertical
                                id="数据内容区域"
                                padding="40dp"
                                gravity="center">

                                <text
                                    id="数据图标"
                                    textSize="48sp"
                                    alpha="0.6"
                                    textColor={全局样式.颜色主题.文字次色}
                                    gravity="center"/>

                                <text
                                    id="数据状态文字"
                                    text="暂无数据"
                                    textSize={全局样式.尺寸规范.字体大}
                                    textColor={全局样式.颜色主题.文字主色}
                                    margin="8dp"
                                    gravity="center"
                                    fontWeight="bold"/>

                                <text
                                    text="开始运行脚本后将显示游戏数据"
                                    textSize={全局样式.尺寸规范.字体中}
                                    textColor={全局样式.颜色主题.文字次色}
                                    gravity="center"/>

                                {/* 游戏数据列表容器 */}
                                <vertical
                                    id="游戏数据列表容器"
                                    w="*"
                                    visibility="gone"
                                    padding="8dp">
                                    {/* 游戏数据条目将动态添加到这里 */}
                                </vertical>
                            </vertical>
                        </ScrollView>
                    </vertical>
                </card>

                {/* 脚本状态卡片 */}
                <card
                    id="脚本状态卡片"
                    cardBackgroundColor={全局样式.颜色主题.背景次色}
                    cardCornerRadius="16dp"
                    cardElevation="2dp">

                    <vertical padding={全局样式.尺寸规范.间距大}>
                        <text
                            text="脚本状态"
                            textSize={全局样式.尺寸规范.字体大}
                            textColor={全局样式.颜色主题.文字主色}
                            fontWeight="bold"
                            margin="0 0 0 8dp"/>

                        <horizontal gravity="center_vertical">
                            <text
                                id="状态指示器"
                                text="●"
                                textSize="16sp"
                                textColor={全局样式.颜色主题.文字次色}
                                margin="0 0 8dp 0"/>

                            <text
                                id="状态文字"
                                text="脚本未运行"
                                textSize={全局样式.尺寸规范.字体中}
                                textColor={全局样式.颜色主题.文字次色}
                                layout_weight="1"/>
                        </horizontal>
                    </vertical>
                </card>
            </vertical>
        </ScrollView>

        {/* 底部控制按钮区域 */}
        <horizontal
            padding={全局样式.尺寸规范.间距中}>

            <button
                id="运行按钮"
                text="运行"
                style="Widget.AppCompat.Button.Colored"
                backgroundTint="#4CAF50"
                textColor="#FFFFFF"
                textSize="16sp"
                layout_weight="1"
                margin="0 0 8dp 0"
                h="48dp"
                fontWeight="bold"/>

            <button
                id="停止按钮"
                text="停止"
                style="Widget.AppCompat.Button.Colored"
                backgroundTint="#4CAF50"
                textColor="#FFFFFF"
                textSize="16sp"
                layout_weight="1"
                margin="8dp 0 0 0"
                h="48dp"/>
        </horizontal>

        {/* 底部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h="50dp"
            elevation="8dp">

            <vertical
                id="脚本标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="脚本图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>

            <vertical
                id="主页标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="主页图标"
                    textSize="20sp"
                    textColor={全局样式.颜色主题.白色}
                    gravity="center"/>
            </vertical>

            <vertical
                id="日志标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="日志图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>
        </horizontal>
        </vertical>

        {/* 抽屉遮罩层 - 初始隐藏，完全透明但保持点击功能 */}
        <vertical id="抽屉遮罩" w="*" h="*" bg="#00000000" visibility="gone" clickable="true"/>

        {/* 抽屉内容面板 - 方案一：卡片式分组布局 */}
        <vertical id="抽屉面板" w="280dp" h="*" bg="#4CAF50" visibility="gone">
            {/* 顶部应用信息卡片 */}
            <card w="*" h="wrap_content" margin="12dp 12dp 12dp 12dp" cardBackgroundColor="#FFFFFF" cardCornerRadius="12dp" cardElevation="6dp">
                <vertical w="*" h="wrap_content" bg="#FFFFFF" padding="14dp">
                    <text id="应用图标" textSize="36sp" textColor="#4CAF50" gravity="center" margin="0 0 5dp 0"/>
                    <text text="Magic 游戏助手" textSize="16sp" textStyle="bold" textColor="#2E7D32" gravity="center"/>
                    <text text="v1.0.0" textSize="10sp" textColor="#66BB6A" gravity="center" margin="1dp 0 0 0"/>
                    <text text="基于 AutoXjs ozobiozobi v6.5.8.17" textSize="8sp" textColor="#81C784" gravity="center" margin="2dp 0 0 0"/>
                </vertical>
            </card>

            {/* 滚动内容区域 */}
            <ScrollView w="*" h="*" layout_weight="1">
                <vertical padding="4dp">
                    {/* 权限管理卡片 */}
                    <card w="*" h="wrap_content" margin="8dp" cardBackgroundColor="#FFFFFF" cardCornerRadius="12dp" cardElevation="0dp">
                        <vertical w="*" h="wrap_content" bg="#FFFFFF" padding="10dp">
                            <text text="权限管理" textSize="14sp" textStyle="bold" textColor="#2E7D32" margin="0 0 8dp 0"/>

                            {/* 无障碍服务权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="无障碍图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 9dp 0" gravity="center_vertical"/>
                                    <text text="无障碍服务" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="无障碍权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="用于自动化操作" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 悬浮窗权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="悬浮窗图标" textSize="10sp" textColor="#2E7D32" margin="0 1dp 5dp 0" gravity="center_vertical"/>
                                    <text text="悬浮窗权限" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="悬浮窗权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="显示悬浮控制面板" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 前台服务权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="前台服务图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 10dp 0" gravity="center_vertical"/>
                                    <text text="前台服务" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="前台服务权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="保持应用在前台运行，防止被系统杀死" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 存储权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="存储权限图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 10dp 0" gravity="center_vertical"/>
                                    <text text="存储权限" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="读取权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="访问设备存储，保存配置和日志数据" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 通知访问权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="通知访问图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 10dp 0" gravity="center_vertical"/>
                                    <text text="通知访问" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="通知访问权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="监听游戏通知，实现智能辅助功能" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 截图权限 */}
                            <vertical w="*" h="wrap_content" margin="0 0 2dp 0" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="截图图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 10dp 0" gravity="center_vertical"/>
                                    <text text="屏幕截图" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="截图权限开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="用于图像识别功能" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>

                            {/* 控制台显示 */}
                            <vertical w="*" h="wrap_content" padding="6dp" bg="#F1F8E9" radius="8dp">
                                <horizontal w="*" h="wrap_content" gravity="center_vertical">
                                    <text id="控制台图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 10dp 0" gravity="center_vertical"/>
                                    <text text="控制台显示" textSize="14sp" textColor="#2E7D32" textStyle="bold" layout_weight="1" gravity="center_vertical"/>
                                    <switch id="控制台显示开关" checked="false"
                                        trackTint="#E0E0E0"
                                        thumbTint="#FFFFFF"
                                        checkedTrackTint={全局样式.颜色主题.成功绿}
                                        checkedThumbTint="#FFFFFF"/>
                                </horizontal>
                                <text text="显示/隐藏悬浮控制台窗口" textSize="10sp" textColor="#66BB6A" margin="24dp 0 0 0"/>
                            </vertical>
                        </vertical>
                    </card>

                    {/* 关于应用卡片 */}
                    <card w="*" h="wrap_content" margin="8dp 12dp 8dp 6dp" cardBackgroundColor="#FFFFFF" cardCornerRadius="12dp" cardElevation="4dp">
                        <horizontal id="关于应用项" w="*" h="48dp" gravity="center_vertical" clickable="true" bg="#FFFFFF" padding="12dp">
                            <text id="关于图标" textSize="14sp" textColor="#2E7D32" margin="0 1dp 13dp 0" gravity="center_vertical"/>
                            <text text="关于应用" textSize="14sp" textColor="#2E7D32" layout_weight="1" textStyle="bold" gravity="center_vertical"/>
                            <text id="关于箭头" textSize="14sp" textColor="#66BB6A" gravity="center_vertical"/>
                        </horizontal>
                    </card>
                </vertical>
            </ScrollView>
        </vertical>
    </frame>
);

// 导出主页布局和相关功能
module.exports = {
    // 布局定义
    布局: 主页布局,

    // 更新游戏数据显示
    更新游戏数据: function(数据) {
        try {
            if (数据 && Object.keys(数据).length > 0) {
                // 隐藏空状态，显示数据列表
                if (ui.数据内容区域) {
                    ui.数据内容区域.attr("visibility", "gone");
                }
                if (ui.游戏数据列表容器) {
                    ui.游戏数据列表容器.attr("visibility", "visible");

                    // 清空现有数据
                    ui.游戏数据列表容器.removeAllViews();

                    // 添加数据条目
                    for (var 键 in 数据) {
                        var 数据条目 = ui.inflate(
                            <horizontal padding="8dp 4dp" gravity="center_vertical">
                                <text
                                    text={键 + ":"}
                                    textSize="14sp"
                                    textColor={全局样式.颜色主题.文字主色}
                                    w="100dp"
                                    fontWeight="bold"/>
                                <text
                                    text={数据[键].toString()}
                                    textSize="14sp"
                                    textColor={全局样式.颜色主题.文字次色}
                                    layout_weight="1"/>
                            </horizontal>
                        );
                        ui.游戏数据列表容器.addView(数据条目);
                    }
                }
            } else {
                // 显示空状态
                if (ui.数据内容区域) {
                    ui.数据内容区域.attr("visibility", "visible");
                }
                if (ui.游戏数据列表容器) {
                    ui.游戏数据列表容器.attr("visibility", "gone");
                }
                ui.数据状态文字.setText("暂无数据");
            }
        } catch (e) {
            console.error("更新游戏数据失败:", e);
        }
    },

    // 更新脚本状态
    更新脚本状态: function(运行中) {
        try {
            if (运行中) {
                // 使用稳定的旧API，添加安全检查
                if (ui.状态指示器 && ui.状态指示器.setTextColor) {
                    ui.状态指示器.setTextColor(colors.parseColor(全局样式.颜色主题.成功色));
                }
                if (ui.状态文字) {
                    if (ui.状态文字.setText) ui.状态文字.setText("脚本运行中");
                    if (ui.状态文字.setTextColor) ui.状态文字.setTextColor(colors.parseColor(全局样式.颜色主题.成功色));
                }
            } else {
                // 使用稳定的旧API，添加安全检查
                if (ui.状态指示器 && ui.状态指示器.setTextColor) {
                    ui.状态指示器.setTextColor(colors.parseColor(全局样式.颜色主题.文字次色));
                }
                if (ui.状态文字) {
                    if (ui.状态文字.setText) ui.状态文字.setText("脚本未运行");
                    if (ui.状态文字.setTextColor) ui.状态文字.setTextColor(colors.parseColor(全局样式.颜色主题.文字次色));
                }
            }
        } catch (e) {
            console.error("更新脚本状态失败:", e);
        }
    },

    // 显示气泡提示
    显示提示: function(消息, 类型) {
        try {
            if (类型 === "成功") {
                toast("成功: " + 消息);
            } else if (类型 === "错误") {
                toast("错误: " + 消息);
            } else if (类型 === "警告") {
                toast("警告: " + 消息);
            } else {
                toast(消息);
            }
        } catch (e) {
            console.error("显示提示失败:", e);
        }
    }
};