{"version": "0.2.0", "configurations": [{"name": "按键监听调试-终端输出", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/按键事件.py", "console": "integratedTerminal", "python": "${workspaceFolder}/.venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {}, "args": [], "justMyCode": true}, {"name": "按键监听调试-控制台输出", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/按键事件.py", "console": "internalConsole", "python": "${workspaceFolder}/.venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {}, "args": [], "justMyCode": true}]}