/**
 * 蓝色区域数字识别脚本
 * 使用Tesseract OCR识别所有数字(0-9)
 * 蓝色区域范围：(289,635,170,105)
 * 参考2.js方案3：黑色数字直接提取
 */

// 导入Tesseract OCR类
importClass(com.googlecode.tesseract.android.TessBaseAPI);

// 申请截图权限
if (!requestScreenCapture(false)) {
    console.log("❌ 截图权限申请失败");
    exit();
}
console.log("✅ 截图权限申请成功");
sleep(2000);  // 等待权限生效

console.log("🎯 开始蓝色区域数字识别");

try {
    // 新建OCR实例
    var tessocr = new TessBaseAPI();
    console.log("📦 Tesseract OCR实例创建成功");
    
    // 设置tessdata父目录路径
    var dataPath = files.path("./");
    console.log("📁 数据目录路径: " + dataPath);
    
    // 初始化tessocr - 使用英文模式
    console.log("🔧 正在初始化Tesseract OCR...");
    var ok = tessocr.init(dataPath, "eng");
    
    if (ok) {
        console.log("✅ Tesseract OCR初始化成功");
        
        // OCR识别优化配置
        console.log("⚙️ 设置OCR识别优化参数");
        
        // PSM 6: 单一文本块识别
        tessocr.setPageSegMode(6);
        
        // 设置字符白名单，只识别数字0-9
        tessocr.setVariable("tessedit_char_whitelist", "0123456789");
        
        // 设置OCR引擎模式为LSTM引擎
        tessocr.setVariable("tessedit_ocr_engine_mode", "1");
        
        console.log("✅ OCR识别优化配置完成");

        // 截图并裁剪指定区域
        console.log("📸 截图并裁剪蓝色区域 (289,635,170,105)");
        var img = captureScreen();

        if (!img) {
            console.log("❌ 截图失败");
            exit();
        }

        // 裁剪蓝色数字区域
        var 裁剪区域 = images.clip(img, 289, 635, 170, 105);
        console.log("✅ 裁剪完成，区域尺寸: " + 裁剪区域.getWidth() + "x" + 裁剪区域.getHeight());

        // 调试：保存裁剪区域图片，用于分析
        try {
            var 调试保存路径 = "/storage/emulated/0/脚本/magic/脚本/ocr/蓝色区域调试.png";
            images.save(裁剪区域, 调试保存路径);
            console.log("🔍 调试：蓝色区域图片已保存到 " + 调试保存路径);
            console.log("💡 请查看此图片，确认是否有数字显示");
        } catch (e) {
            console.log("⚠️ 调试图片保存失败: " + e);
        }

        // 释放原始截图
        img.recycle();

        // 新方案：多重图像优化+像素分布分析 - 解决数字3和5识别混淆问题
        console.log("🔧 新方案: 多重图像优化+像素分布分析 - 解决数字3和5识别混淆问题");

        var 最佳结果 = {
            阈值: 0,
            纯数字: "",
            置信度: 0,
            数字字符数: 0,
            原始文本: ""
        };

        var 所有结果 = [];  // 收集所有有效结果
        var 已输出纠错信息 = false;  // 标记是否已输出过纠错信息
        var 已输出轮廓纠错信息 = false;  // 标记是否已输出过轮廓纠错信息

        // 遍历阈值1-100
        for (var 阈值 = 1; 阈值 <= 100; 阈值++) {
            try {

                // 先将图片转为灰度图
                var 灰度图 = images.grayscale(裁剪区域);

                // 使用当前阈值进行黑色提取
                var 黑色提取图 = images.threshold(灰度图, 阈值, 255, "BINARY");

                // 多重图像优化：解决数字3和5识别混淆问题

                // 1. 中值滤波去噪：去除小黑点噪声
                var 去噪图 = images.medianBlur(黑色提取图, 3);

                // 2. 高斯模糊：轻微模糊减少噪声，但保持主要特征
                var 模糊图 = images.gaussianBlur(去噪图, [3, 3]);

                // 3. 再次二值化：增强对比度，使数字边缘更清晰
                var 增强图 = images.threshold(模糊图, 127, 255, "BINARY");

                // 4. 轻微膨胀：加粗数字线条，使特征更明显
                try {
                    // 尝试使用膨胀操作（如果支持）
                    var 最终图 = images.dilate ? images.dilate(增强图, [2, 2]) : 增强图;
                } catch (e) {
                    // 如果不支持膨胀，使用原图
                    var 最终图 = 增强图;
                }

                // 对优化后的图片进行OCR识别
                tessocr.setImage(最终图.getBitmap());
                var 原始识别结果 = tessocr.getUTF8Text().trim();

                // 获取识别置信度
                var 置信度 = 0;
                try {
                    if (typeof tessocr.meanConfidence === 'function') {
                        置信度 = tessocr.meanConfidence();
                    } else if (typeof tessocr.getMeanConfidence === 'function') {
                        置信度 = tessocr.getMeanConfidence();
                    } else {
                        置信度 = (原始识别结果.match(/\d/g) || []).length * 25;
                    }
                } catch (e) {
                    置信度 = (原始识别结果.match(/\d/g) || []).length * 25;
                }

                // 像素分布分析：通过分析上下左右像素分布区分3和5
                var 识别结果1 = 开口方向检测(最终图, 原始识别结果);

                // 新增：轮廓分析识别数字2和4
                var 识别结果 = 轮廓分析识别_蓝色版(最终图, 识别结果1);

                var 数字字符数 = (识别结果.match(/\d/g) || []).length;
                var 纯数字 = 识别结果.replace(/[^\d]/g, '');

                // 收集所有有效结果（不显示实时输出，避免重复）
                if (数字字符数 >= 1) {
                    var 清理后文本 = 识别结果.replace(/\n/g, ' ').replace(/\s+/g, ' ');
                    所有结果.push({
                        阈值: 阈值,
                        原始文本: 清理后文本,
                        纯数字: 纯数字,
                        数字字符数: 数字字符数,
                        置信度: 置信度
                    });
                }

                // 判断是否为更好的结果（优先考虑置信度最高的结果）
                var 是更好结果 = false;

                // 只要识别到数字就考虑
                if (数字字符数 >= 1) {
                    if (最佳结果.数字字符数 === 0) {
                        // 如果还没有有效结果，接受第一个有数字的结果
                        是更好结果 = true;
                    } else if (置信度 > 最佳结果.置信度) {
                        // 优先选择置信度最高的结果
                        是更好结果 = true;
                    }
                }

                if (是更好结果) {
                    最佳结果.阈值 = 阈值;
                    最佳结果.纯数字 = 纯数字;
                    最佳结果.置信度 = 置信度;
                    最佳结果.数字字符数 = 数字字符数;
                    最佳结果.原始文本 = 识别结果;
                    console.log("   ⭐ 更好结果！");
                }

                // 释放处理后的图片资源
                灰度图.recycle();
                黑色提取图.recycle();
                去噪图.recycle();
                模糊图.recycle();
                增强图.recycle();
                if (最终图 !== 增强图) {
                    最终图.recycle();
                }

            } catch (e) {
                console.log("   ❌ 阈值" + 阈值 + "处理失败: " + e);
            }
        }

        // 按置信度从低到高排序并显示结果
        所有结果.sort(function(a, b) {
            return a.置信度 - b.置信度;  // 升序排列
        });

        console.log("\n📊 按置信度排序的识别结果（置信度从低到高）:");
        所有结果.forEach(function(结果, 索引) {
            console.log("   " + (索引 + 1) + ". 阈值" + 结果.阈值 + ": 原始[" + 结果.原始文本 + "] 纯数字[" + 结果.纯数字 + "] 字符数[" + 结果.数字字符数 + "] 置信度[" + 结果.置信度.toFixed(1) + "%]");
        });

        console.log("\n🏆 最佳黑色提取结果:");
        console.log("   最佳阈值: " + 最佳结果.阈值);
        // 清理最佳结果的原始文本中的换行符
        var 最佳清理文本 = 最佳结果.原始文本.replace(/\n/g, ' ').replace(/\s+/g, ' ');
        console.log("   原始文本: \"" + 最佳清理文本 + "\"");
        console.log("   纯数字: \"" + 最佳结果.纯数字 + "\"");
        console.log("   数字字符数: " + 最佳结果.数字字符数);
        console.log("   置信度: " + 最佳结果.置信度.toFixed(1) + "%");

        console.log("\n🎯 最终识别结果: \"" + 最佳结果.纯数字 + "\"");

        // 输出识别区域的坐标信息
        if (最佳结果.数字字符数 > 0) {
            console.log("📍 识别区域坐标信息:");
            console.log("   区域左上角: (289, 635)");
            console.log("   区域宽高: 170 x 105");
            console.log("   区域右下角: (" + (289 + 170) + ", " + (635 + 105) + ")");
            console.log("   区域中心点: (" + (289 + 170/2) + ", " + (635 + 105/2) + ")");
            console.log("   识别到的数字: \"" + 最佳结果.纯数字 + "\" 位于蓝色区域");
        } else {
            console.log("❌ 黑色提取无有效数字结果");
        }

        // 释放图片资源
        裁剪区域.recycle();
        console.log("🧹 图片资源已释放");
        
    } else {
        console.log("❌ Tesseract OCR初始化失败");
        console.log("💡 可能原因:");
        console.log("   1. tessdata目录不存在");
        console.log("   2. eng.traineddata文件缺失");
    }
    
} catch (e) {
    console.log("❌ Tesseract OCR运行出错: " + e);
    console.log("💡 可能原因:");
    console.log("   1. tessdata目录不存在");
    console.log("   2. eng.traineddata文件缺失");
    console.log("   3. 截图或图像处理失败");
}

console.log("\n📋 蓝色区域数字识别完成");
console.log("🎯 蓝色区域范围: (289,635,170,105)");
console.log("🚀 遍历阈值1-100，多重图像优化+像素分布分析识别所有数字0-9");

// 静默版像素分布分析：只在第一次检测到纠错时输出信息
function 开口方向检测(图像, 原始文本) {
    if (!原始文本) return 原始文本;

    var 纯数字 = 原始文本.replace(/[^\d]/g, '');

    // 只对包含3或5的数字进行分析
    if (纯数字.includes('3') || 纯数字.includes('5')) {
        try {
            // 获取图像尺寸
            var 图像宽度 = 图像.getWidth();
            var 图像高度 = 图像.getHeight();

            // 静默执行像素分布分析
            var 像素分布分析 = 分析像素分布_静默版(图像, 图像宽度, 图像高度);

            // 静默执行数字特征分析
            var 分布分析结果 = 分析数字特征_静默版(像素分布分析, 纯数字);

            // 只在第一次需要纠错时输出信息，避免重复
            if (分布分析结果.建议修正 && !已输出纠错信息) {
                console.log("   🔧 数字纠错: [" + 原始文本 + "] → [" + 分布分析结果.修正结果 + "] (原因: " + 分布分析结果.简要原因 + ")");
                已输出纠错信息 = true;  // 标记已输出，避免重复
            }

            if (分布分析结果.建议修正) {
                return 分布分析结果.修正结果;
            }

            return 原始文本;

        } catch (e) {
            return 原始文本;
        }
    }

    return 原始文本;
}

// 静默版像素分布分析 - 不输出调试信息
function 分析像素分布_静默版(图像, 图像宽度, 图像高度) {
    // 定义四个分析区域
    var 区域配置 = {
        左侧: { x起始: 0, x结束: Math.floor(图像宽度 * 0.4), y起始: Math.floor(图像高度 * 0.2), y结束: Math.floor(图像高度 * 0.8) },
        右侧: { x起始: Math.floor(图像宽度 * 0.6), x结束: 图像宽度, y起始: Math.floor(图像高度 * 0.2), y结束: Math.floor(图像高度 * 0.8) },
        上部: { x起始: Math.floor(图像宽度 * 0.2), x结束: Math.floor(图像宽度 * 0.8), y起始: 0, y结束: Math.floor(图像高度 * 0.4) },
        下部: { x起始: Math.floor(图像宽度 * 0.2), x结束: Math.floor(图像宽度 * 0.8), y起始: Math.floor(图像高度 * 0.6), y结束: 图像高度 }
    };

    var 分布结果 = {};

    // 静默分析每个区域
    for (var 区域名 in 区域配置) {
        var 区域 = 区域配置[区域名];
        var 黑色像素数 = 0;
        var 总像素数 = 0;

        try {
            for (var y = 区域.y起始; y < 区域.y结束; y++) {
                for (var x = 区域.x起始; x < 区域.x结束; x++) {
                    if (x >= 0 && x < 图像宽度 && y >= 0 && y < 图像高度) {
                        var 像素颜色 = 图像.pixel(x, y);
                        var 红色 = (像素颜色 >> 16) & 0xFF;
                        var 绿色 = (像素颜色 >> 8) & 0xFF;
                        var 蓝色 = 像素颜色 & 0xFF;
                        var 灰度值 = (红色 + 绿色 + 蓝色) / 3;

                        if (灰度值 < 100) {
                            黑色像素数++;
                        }
                        总像素数++;
                    }
                }
            }

            var 密度 = 总像素数 > 0 ? (黑色像素数 / 总像素数) * 100 : 0;
            分布结果[区域名 + "密度"] = 密度;

        } catch (e) {
            分布结果[区域名 + "密度"] = 0;
        }
    }

    return 分布结果;
}

// 静默版数字特征分析 - 只返回结果，不输出调试信息
function 分析数字特征_静默版(像素分布, 当前数字) {
    var 左侧密度 = 像素分布.左侧密度;
    var 右侧密度 = 像素分布.右侧密度;
    var 上部密度 = 像素分布.上部密度;
    var 下部密度 = 像素分布.下部密度;

    // 计算特征指标
    var 左右差异 = 右侧密度 - 左侧密度;  // 正值表示右侧密度高

    var 建议修正 = false;
    var 修正结果 = 当前数字;
    var 简要原因 = "";

    // 判断逻辑
    if (Math.abs(左右差异) > 2.0) {  // 左右差异显著
        if (左右差异 < -2.0) {
            // 左侧密度高，右侧密度低 → 数字3特征
            if (当前数字.includes('5')) {
                建议修正 = true;
                修正结果 = 当前数字.replace(/5/g, '3');
                简要原因 = "右侧开口特征";
            }
        } else {
            // 右侧密度高，左侧密度低 → 数字5特征
            if (当前数字.includes('3')) {
                建议修正 = true;
                修正结果 = 当前数字.replace(/3/g, '5');
                简要原因 = "左侧开口特征";
            }
        }
    }

    // 结合上下分布进一步判断数字5
    if (上部密度 > 下部密度 + 3.0) {
        if (当前数字.includes('3') && !建议修正) {
            建议修正 = true;
            修正结果 = 当前数字.replace(/3/g, '5');
            简要原因 = "上部密度高特征";
        } else if (建议修正 && 简要原因 === "左侧开口特征") {
            简要原因 = "左侧开口+上部密度高";
        }
    }

    return {
        建议修正: 建议修正,
        修正结果: 修正结果,
        简要原因: 简要原因
    };
}

// 保留原有函数以防兼容性问题
function 分析侧边像素密度(图像, 侧边, 图像宽度, 图像高度) {
    var 检测宽度 = Math.floor(图像宽度 * 0.4);  // 增加到40%的区域
    var 检测高度 = Math.floor(图像高度 * 0.8);  // 增加到80%的高度
    var 起始高度 = Math.floor(图像高度 * 0.1);  // 从10%开始

    console.log("   📏 " + 侧边 + "检测区域: 宽度" + 检测宽度 + ", 高度" + 检测高度 + ", 起始高度" + 起始高度);

    var 黑色像素数 = 0;
    var 采样计数 = 0;

    try {
        for (var y = 起始高度; y < 起始高度 + 检测高度; y++) {
            for (var x = 0; x < 检测宽度; x++) {
                var 实际x = 侧边 === "左侧" ? x : (图像宽度 - 检测宽度 + x);

                // 边界检查
                if (实际x >= 0 && 实际x < 图像宽度 && y >= 0 && y < 图像高度) {
                    // 获取像素颜色
                    var 像素颜色 = 图像.pixel(实际x, y);
                    var 红色 = (像素颜色 >> 16) & 0xFF;
                    var 绿色 = (像素颜色 >> 8) & 0xFF;
                    var 蓝色 = 像素颜色 & 0xFF;
                    var 灰度值 = (红色 + 绿色 + 蓝色) / 3;

                    // 降低黑色判断阈值，提高敏感度
                    if (灰度值 < 100) {  // 从128降低到100
                        黑色像素数++;
                    }
                    采样计数++;
                }
            }
        }

        console.log("   🔢 " + 侧边 + "采样统计: 总采样" + 采样计数 + ", 黑色像素" + 黑色像素数);

    } catch (e) {
        console.log("   ⚠️ " + 侧边 + "像素分析异常: " + e.toString());
    }

    var 像素密度 = 采样计数 > 0 ? (黑色像素数 / 采样计数) * 100 : 0;

    return {
        侧边: 侧边,
        黑色像素数: 黑色像素数,
        总像素数: 采样计数,
        密度: 像素密度
    };
}

// 分析开口方向并给出判断
function 分析开口方向(左侧统计, 右侧统计, 当前数字) {
    var 密度差异 = 右侧统计.密度 - 左侧统计.密度;
    var 密度差异绝对值 = Math.abs(密度差异);

    console.log("   📈 密度差异分析: " + 密度差异.toFixed(2) + "% (右侧-左侧)");

    // 降低判断开口方向的阈值，提高敏感度
    var 显著差异阈值 = 3.0;  // 3%的密度差异认为是显著的

    console.log("   🎯 使用阈值: " + 显著差异阈值 + "%，当前差异: " + 密度差异绝对值.toFixed(2) + "%");

    if (密度差异绝对值 < 显著差异阈值) {
        return {
            分析结果: "左右密度相近(" + 密度差异绝对值.toFixed(2) + "%)，无明显开口特征",
            建议修正: false,
            修正结果: 当前数字
        };
    }

    // 数字3：开口朝右，右侧密度应该更低（更多空白）
    // 数字5：开口朝左，左侧密度应该更低（更多空白）

    if (密度差异 > 显著差异阈值) {
        // 右侧密度高，左侧密度低 → 左侧开口特征
        console.log("   👈 检测到左侧开口特征（右侧密度更高）");

        if (当前数字.includes('3')) {
            return {
                分析结果: "左侧开口特征，数字3可能是5的误识别",
                建议修正: true,
                修正结果: 当前数字.replace(/3/g, '5')
            };
        } else {
            return {
                分析结果: "左侧开口特征与数字5匹配",
                建议修正: false,
                修正结果: 当前数字
            };
        }
    } else {
        // 左侧密度高，右侧密度低 → 右侧开口特征
        console.log("   👉 检测到右侧开口特征（左侧密度更高）");

        if (当前数字.includes('5')) {
            return {
                分析结果: "右侧开口特征，数字5可能是3的误识别",
                建议修正: true,
                修正结果: 当前数字.replace(/5/g, '3')
            };
        } else {
            return {
                分析结果: "右侧开口特征与数字3匹配",
                建议修正: false,
                修正结果: 当前数字
            };
        }
    }
}

// 新增：蓝色区域轮廓分析识别数字2和4
function 轮廓分析识别_蓝色版(图像, 原始文本) {
    if (!原始文本) return 原始文本;

    var 纯数字 = 原始文本.replace(/[^\d]/g, '');

    // 对包含2或4的数字进行轮廓分析
    if (纯数字.includes('2') || 纯数字.includes('4')) {
        try {
            // 获取图像尺寸
            var 图像宽度 = 图像.getWidth();
            var 图像高度 = 图像.getHeight();

            // 静默执行像素分布分析
            var 像素分布分析 = 分析像素分布_2和4版本_蓝色(图像, 图像宽度, 图像高度);

            // 静默执行2和4特征分析
            var 分布分析结果 = 分析2和4特征_蓝色版(像素分布分析, 纯数字);

            // 只在第一次需要纠错时输出信息，避免重复
            if (分布分析结果.建议修正 && !已输出数字2纠错信息) {
                console.log("   🔧 数字纠错(2): [" + 原始文本 + "] → [" + 分布分析结果.修正结果 + "] (原因: " + 分布分析结果.简要原因 + ")");
                已输出数字2纠错信息 = true;  // 标记已输出，避免重复
            }

            if (分布分析结果.建议修正) {
                return 分布分析结果.修正结果;
            }

            return 原始文本;

        } catch (e) {
            return 原始文本;
        }
    }

    return 原始文本;
}

// 蓝色区域2和4专用像素分布分析 - 重新设计的区域划分
function 分析像素分布_2和4版本_蓝色(图像, 图像宽度, 图像高度) {
    // 基于真实数字特征的区域划分
    var 区域配置 = {
        左上: { x起始: 0, x结束: Math.floor(图像宽度 * 0.5), y起始: 0, y结束: Math.floor(图像高度 * 0.4) },  // 数字4的三角形开口区域
        右上: { x起始: Math.floor(图像宽度 * 0.6), x结束: 图像宽度, y起始: 0, y结束: Math.floor(图像高度 * 0.4) },  // 数字4的竖线顶部
        中间: { x起始: Math.floor(图像宽度 * 0.2), x结束: Math.floor(图像宽度 * 0.8), y起始: Math.floor(图像高度 * 0.3), y结束: Math.floor(图像高度 * 0.7) },  // 数字2的转折区域
        顶部: { x起始: Math.floor(图像宽度 * 0.1), x结束: Math.floor(图像宽度 * 0.9), y起始: 0, y结束: Math.floor(图像高度 * 0.25) },  // 数字2的顶部水平线
        底部: { x起始: Math.floor(图像宽度 * 0.1), x结束: Math.floor(图像宽度 * 0.9), y起始: Math.floor(图像高度 * 0.75), y结束: 图像高度 }  // 数字2的底部水平线
    };

    var 分布结果 = {};

    // 静默分析每个区域
    for (var 区域名 in 区域配置) {
        var 区域 = 区域配置[区域名];
        var 黑色像素数 = 0;
        var 总像素数 = 0;

        try {
            for (var y = 区域.y起始; y < 区域.y结束; y++) {
                for (var x = 区域.x起始; x < 区域.x结束; x++) {
                    if (x >= 0 && x < 图像宽度 && y >= 0 && y < 图像高度) {
                        var 像素颜色 = 图像.pixel(x, y);
                        var 红色 = (像素颜色 >> 16) & 0xFF;
                        var 绿色 = (像素颜色 >> 8) & 0xFF;
                        var 蓝色 = 像素颜色 & 0xFF;
                        var 灰度值 = (红色 + 绿色 + 蓝色) / 3;

                        if (灰度值 < 100) {
                            黑色像素数++;
                        }
                        总像素数++;
                    }
                }
            }

            var 密度 = 总像素数 > 0 ? (黑色像素数 / 总像素数) * 100 : 0;
            分布结果[区域名 + "密度"] = 密度;

        } catch (e) {
            分布结果[区域名 + "密度"] = 0;
        }
    }

    return 分布结果;
}

// 蓝色区域2和4特征分析函数 - 基于真实数字特征重新设计
function 分析2和4特征_蓝色版(像素分布, 当前数字) {
    var 左上密度 = 像素分布.左上密度;
    var 右上密度 = 像素分布.右上密度;
    var 中间密度 = 像素分布.中间密度;
    var 顶部密度 = 像素分布.顶部密度;
    var 底部密度 = 像素分布.底部密度;

    var 建议修正 = false;
    var 修正结果 = 当前数字;
    var 简要原因 = "";

    // 数字2的特征：顶部和底部封闭（密度高），中间有转折开口（密度低）
    var 数字2特征 = (顶部密度 > 25 && 底部密度 > 25 && 中间密度 < 20);

    // 数字4的特征：左上三角形开口（密度低），右上有竖线（密度高）
    var 数字4特征 = (左上密度 < 15 && 右上密度 > 30);

    // 判断逻辑：只在特征非常明显时才进行纠正
    if (数字2特征 && 当前数字.includes('4')) {
        建议修正 = true;
        修正结果 = 当前数字.replace(/4/g, '2');
        简要原因 = "S型曲线特征(上下封闭+中间开口)";
    } else if (数字4特征 && 当前数字.includes('2')) {
        建议修正 = true;
        修正结果 = 当前数字.replace(/2/g, '4');
        简要原因 = "三角形+竖线特征(左上开口+右上封闭)";
    }

    return {
        建议修正: 建议修正,
        修正结果: 修正结果,
        简要原因: 简要原因
    };
}
