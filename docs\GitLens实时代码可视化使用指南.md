# GitLens实时代码可视化使用指南

## 📋 概述

GitLens是VS Code最强大的Git可视化扩展，提供类似Cursor的实时代码修改可视化和自动跳转功能。本指南将帮助您快速上手并充分利用GitLens的强大功能。

## 🚀 快速开始

### 安装GitLens
1. 打开VS Code扩展面板 (`Ctrl+Shift+X`)
2. 搜索 `GitLens`
3. 安装 `GitLens — Git supercharged` (作者：GitKraken)
4. 重启VS Code

### 立即启用核心功能

打开命令面板 (`Ctrl+Shift+P`)，依次执行以下命令：

```
GitLens: Toggle Line Blame          # 启用行内blame显示
GitLens: Toggle File Changes        # 启用文件修改标记  
GitLens: Toggle File Heatmap        # 启用文件热力图
```

## ⚙️ 最佳配置设置

### 方法1：通过设置界面
1. 按 `Ctrl+,` 打开设置
2. 搜索 `gitlens`
3. 根据需要调整各项设置

### 方法2：直接编辑settings.json
按 `Ctrl+Shift+P` → `Preferences: Open Settings (JSON)`，添加以下配置：

```json
{
  "gitlens.currentLine.enabled": true,
  "gitlens.codeLens.enabled": true,
  "gitlens.hovers.enabled": true,
  "gitlens.changes.locations": ["gutter", "line", "overview"],
  "gitlens.blame.format": "${author}, ${agoOrDate}",
  "gitlens.blame.heatmap.enabled": true,
  "gitlens.statusBar.enabled": true,
  "gitlens.views.repositories.files.layout": "tree",
  "gitlens.blame.ignoreWhitespace": true,
  "gitlens.defaultDateFormat": "YYYY-MM-DD HH:mm",
  "gitlens.defaultDateShortFormat": "MM-DD HH:mm"
}
```

## 👀 实时修改可视化功能

### 装订线指示器（Gutter Indicators）
在代码行号左侧会显示彩色指示器：
- **🟢 绿色条**: 新增的行
- **🔵 蓝色条**: 修改的行
- **🔺 红色三角**: 删除的行

### 行内Blame信息
每行代码末尾显示最后修改信息：
```javascript
console.log("Hello World");  // 张三, 2小时前
```

### 热力图显示
代码背景颜色表示修改频率：
- **深红色**: 最近频繁修改的代码
- **橙色**: 中等修改频率
- **浅色/无色**: 很少修改的代码

### CodeLens信息
文件顶部和代码块开始处显示：
- **Recent Change**: 最近修改的作者和时间
- **Authors**: 参与修改的作者数量

## 🔄 自动跳转和导航功能

### Revision Navigation（修订导航）
1. **启用方式**: 点击文件顶部的CodeLens链接
2. **导航按钮**: 文件顶部出现前进/后退按钮
3. **快捷键**:
   - `Alt+,` - 上一个修订版本
   - `Alt+.` - 下一个修订版本

### 快速跳转命令
在命令面板中使用：
```
GitLens: Show Next Change           # 跳转到下一个修改
GitLens: Show Previous Change       # 跳转到上一个修改
GitLens: Show Line History          # 查看当前行历史
GitLens: Show File History          # 查看文件历史
```

### 右键菜单快捷操作
右键点击代码行 → **GitLens** 菜单：
- **Show Line History** - 查看该行的修改历史
- **Show File History** - 查看整个文件的历史
- **Show Commit Details** - 查看提交详情
- **Copy SHA** - 复制提交SHA

## 🔍 自动Diff对比功能

### 启用自动Diff对比

#### 方法1：通过设置界面
1. **打开设置** (`Ctrl+,`)
2. **搜索** `gitlens diff`
3. **启用以下选项**：
   - `GitLens › Views › File History: Show All Branches` ✅
   - `GitLens › Views › File History: Show Merge Commits` ✅
   - `GitLens › Advanced › File History: Follow Renames` ✅

#### 方法2：编辑settings.json
按 `Ctrl+Shift+P` → `Preferences: Open Settings (JSON)`，添加：

```json
{
  "gitlens.views.fileHistory.enabled": true,
  "gitlens.views.fileHistory.location": "explorer",
  "gitlens.advanced.fileHistoryFollowsRenames": true,
  "gitlens.advanced.fileHistoryShowAllBranches": true,
  "gitlens.views.compare.enabled": true,
  "gitlens.views.compare.location": "gitlens",
  "diffEditor.renderSideBySide": true,
  "diffEditor.ignoreTrimWhitespace": false,
  "git.openDiffOnClick": true,
  "workbench.editor.enablePreview": false
}
```

#### 方法3：完整的最佳实践配置
```json
{
  "gitlens.currentLine.enabled": true,
  "gitlens.codeLens.enabled": true,
  "gitlens.views.fileHistory.enabled": true,
  "gitlens.views.fileHistory.location": "explorer",
  "git.openDiffOnClick": true,
  "diffEditor.renderSideBySide": true,
  "diffEditor.ignoreTrimWhitespace": false,
  "diffEditor.renderIndicators": true,
  "diffEditor.codeLens": true,
  "diffEditor.wordWrap": "on",
  "diffEditor.renderMarginRevertIcon": true,
  "workbench.editor.enablePreview": false,
  "gitlens.hovers.enabled": true,
  "gitlens.blame.heatmap.enabled": true,
  "gitlens.changes.locations": ["gutter", "line", "overview"]
}
```

### 自动触发Diff的方式

#### 1. 文件历史自动展开
在GitLens面板中，`FILE HISTORY`部分会自动显示文件的修改历史，点击任意历史记录就会自动打开diff对比。

#### 2. 设置快捷键
按 `Ctrl+Shift+P` → `Preferences: Open Keyboard Shortcuts`，搜索并设置：
- `GitLens: Compare File with Previous` → 推荐快捷键 `Ctrl+Alt+D`
- `GitLens: Compare File with Working Tree` → 推荐快捷键 `Ctrl+Alt+W`

#### 3. 右键菜单自动化
右键点击文件 → **GitLens** → **Compare File with Previous** 会立即打开diff对比。

### 关闭Diff对比功能

#### 方法1：临时关闭当前Diff
- **关闭当前diff标签页**: 点击diff编辑器标签页的 `×` 按钮
- **快捷键关闭**: `Ctrl+W` 关闭当前diff标签页
- **返回原文件**: 点击原文件标签页

#### 方法2：禁用自动Diff功能
在settings.json中设置：
```json
{
  "git.openDiffOnClick": false,
  "diffEditor.renderSideBySide": false,
  "gitlens.views.fileHistory.enabled": false
}
```

#### 方法3：通过命令面板关闭
```
GitLens: Close Unchanged Files      # 关闭未修改的文件
GitLens: Close All Diff Editors     # 关闭所有diff编辑器
```

#### 方法4：禁用特定Diff功能
```json
{
  "diffEditor.codeLens": false,
  "diffEditor.renderIndicators": false,
  "diffEditor.renderMarginRevertIcon": false,
  "gitlens.views.compare.enabled": false
}
```

### Diff对比显示选项

#### 并排显示 vs 内联显示
```json
{
  "diffEditor.renderSideBySide": true,    // 并排显示（推荐）
  "diffEditor.renderSideBySide": false    // 内联显示
}
```

#### 忽略空白字符
```json
{
  "diffEditor.ignoreTrimWhitespace": true,   // 忽略行尾空白
  "diffEditor.ignoreTrimWhitespace": false   // 显示所有差异
}
```

#### 词级别对比
```json
{
  "diffEditor.wordWrap": "on",              // 启用自动换行
  "diffEditor.wordWrap": "off"              // 禁用自动换行
}
```

## 📊 侧边栏视图功能

### GitLens Inspect面板
提供当前代码的详细上下文信息：
- **Inspect** - 查看选中代码的提交详情
- **Line History** - 当前行的修改历史
- **File History** - 文件的完整修改历史
- **Search & Compare** - 搜索和比较功能

### Source Control增强
在源代码管理面板中新增：
- **Commits** - 提交历史浏览
- **Branches** - 分支管理
- **Remotes** - 远程仓库管理
- **Stashes** - 暂存管理
- **Contributors** - 贡献者统计

## 💡 高级使用技巧

### 悬停查看详情
- **悬停blame信息**: 查看完整提交详情和文件变更
- **悬停装订线**: 查看具体的代码修改内容
- **悬停CodeLens**: 查看作者贡献统计

### 状态栏信息
底部状态栏显示当前行的Git信息：
- 最后修改者
- 修改时间
- 提交SHA（点击可查看详情）

### 比较和搜索
1. **比较功能**:
   - 与工作树比较
   - 与上一个提交比较
   - 与特定提交比较

2. **搜索功能**:
   - 按提交信息搜索
   - 按作者搜索
   - 按文件变更搜索

## 🎯 实际使用场景

### 场景1：代码审查
1. 打开需要审查的文件
2. 查看热力图识别频繁修改的区域
3. 悬停blame信息了解修改原因
4. 使用Revision Navigation查看历史版本
5. **自动Diff对比**: 点击FILE HISTORY中的记录自动打开并排对比

### 场景2：Bug追踪
1. 定位到有问题的代码行
2. 右键 → **Show Line History**
3. 查看该行的所有修改记录
4. 找到引入问题的提交
5. **使用Diff对比**: 自动查看问题引入前后的代码差异

### 场景3：团队协作
1. 查看CodeLens了解代码所有权
2. 通过blame信息联系相关开发者
3. 使用Contributors视图了解团队贡献
4. **Diff分享**: 通过并排对比向团队展示代码变更

### 场景4：代码回滚
1. 在FILE HISTORY中找到需要回滚的版本
2. 使用自动Diff对比查看差异
3. 确认回滚内容后执行操作
4. **关闭Diff**: 完成后关闭对比视图回到正常编辑

## 🔧 故障排除

### 常见问题

**Q: GitLens功能没有显示？**
A: 确保当前文件夹是Git仓库，并且文件已被Git跟踪

**Q: Blame信息显示不准确？**
A: 检查Git配置，确保用户名和邮箱设置正确

**Q: 自动Diff对比不工作？**
A: 检查以下设置：
```json
{
  "git.openDiffOnClick": true,
  "gitlens.views.fileHistory.enabled": true,
  "diffEditor.renderSideBySide": true
}
```

**Q: Diff对比显示异常？**
A: 尝试重置diff编辑器设置：
```json
{
  "diffEditor.ignoreTrimWhitespace": false,
  "diffEditor.renderIndicators": true,
  "workbench.editor.enablePreview": false
}
```

**Q: 性能问题？**
A: 在大型仓库中可以禁用某些功能：
```json
{
  "gitlens.blame.heatmap.enabled": false,
  "gitlens.codeLens.enabled": false,
  "gitlens.views.fileHistory.enabled": false
}
```

**Q: 如何完全关闭Diff功能？**
A: 设置以下配置：
```json
{
  "git.openDiffOnClick": false,
  "diffEditor.renderSideBySide": false,
  "gitlens.views.compare.enabled": false
}
```

### 重置配置
如果遇到问题，可以重置GitLens配置：
```
GitLens: Reset Avatars Cache
GitLens: Reset Suppressed Warnings
GitLens: Close All Diff Editors
```

## 📚 更多资源

- **官方文档**: https://github.com/gitkraken/vscode-gitlens
- **视频教程**: https://www.youtube.com/watch?v=UQPb73Zz9qk
- **社区支持**: https://github.com/gitkraken/vscode-gitlens/discussions

---

**创建时间**: 2025年1月  
**适用版本**: GitLens v15+, VS Code v1.85+  
**作者**: AI开发助手
