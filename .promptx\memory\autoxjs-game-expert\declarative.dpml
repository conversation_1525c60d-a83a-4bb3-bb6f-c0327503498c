<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753883208057_q13xcedjl" time="2025/07/30 21:46">
    <content>
      Magic项目当前状态和结构：
      1. 项目基于AutoXjs ozobiozobi v6.5.8.17，采用Android原生XML布局
      2. 主要目录结构：main.js入口，ui/界面模块，脚本/逻辑模块，assets/资源，存储数据/配置
      3. 核心模块：主页、日志页、脚本配置页、菜单抽屉页，每个模块包含UI定义和业务逻辑
      4. 已完成基础架构：应用启动、权限检查、配置管理、日志系统、页面切换
      5. 技术规范：ES5语法，4空格缩进，中文注释，禁用WebView/HTML/CSS
      6. 已修复的关键问题：XML格式错误(逗号改空格)，API兼容性(ui.layout替代setContentView)，控件安全访问
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753883223972_48kmf3vs4" time="2025/07/30 21:47">
    <content>
      开发规范和习惯：
      1. 技术准确性第一：所有代码基于AutoXjs官方文档，严禁虚假信息，不确定时联网搜索确认
      2. 问题导向开发：专注解决实际问题，绝对禁止创建示例/测试/demo文件，开发前完整阅读相关代码
      3. 简洁代码规则：用最少代码解决最多问题，函数命名动词_名词格式，参数全部可调，功能整合，直线逻辑
      4. 安全编程模式：控件访问前检查存在性，完善try-catch异常处理，及时释放资源避免内存泄漏
      5. API使用规范：优先ui.layout()替代setContentView，使用attr()方法设置控件属性，XML属性用空格分隔不用逗号
      6. 模块化架构：UI定义与业务逻辑分离，统一导出&quot;布局&quot;属性，require/module.exports模块管理
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753883235308_jkzsqtmp5" time="2025/07/30 21:47">
    <content>
      已修复的关键技术问题和解决方案：
      1. XML格式错误：padding/margin/stroke属性必须用空格分隔，不能用逗号，如&quot;8dp 4dp&quot;不是&quot;8dp,4dp&quot;
      2. API兼容性问题：ui.setContentView()不稳定，改用ui.layout()；控件方法如setText()改用attr()方法
      3. 模块导出不一致：统一使用&quot;布局&quot;作为XML布局导出属性名，避免属性名不匹配导致页面切换失败
      4. 控件访问安全性：直接访问控件可能空指针异常，必须先检查if(ui.控件名)再操作
      5. 权限检查优化：避免启动时触发权限请求导致崩溃，移除截图权限检查，延迟提示用户授权
      6. 全局错误处理：使用events.on(&quot;uncaughtException&quot;)监听未捕获异常，安全记录日志
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753883323047_wmwlovg0n" time="2025/07/30 21:48">
    <content>
      PromptX项目配置和资源体系：
      1. 项目已配置完整的PromptX专业资源体系，包含6个专业资源
      2. 角色资源：autoxjs-game-expert专业角色，提供AutoXjs游戏辅助开发专业能力
      3. 执行模式：autoxjs-development-workflow开发工作流程，simple-code-rules简洁代码规则
      4. 知识库：autoxjs-ozobiozobi-expertise技术知识，magic-project-standards项目标准
      5. 思维模式：autoxjs-expert-thinking专家思维，指导AI的专业思考方式
      6. 记忆系统：已建立专业记忆库，支持跨会话记忆保持和精确检索
      7. 项目路径：D:\magic，MCP实例：mcp-22460(cursor)，完全支持多项目隔离
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208480629_90yfxizxd" time="2025/08/03 16:08">
    <content>
      Magic项目完整开发规范深度学习总结：
    
      ## 项目核心定位
      - 基于AutoXjs ozobiozobi v6.5.8.17的专业游戏辅助脚本应用
      - 支持Android 9+系统，雷电模拟器环境(540x960, DPI240)
      - 专家级开发工程师角色，10年+AutoXjs经验，产品设计思维
    
      ## 技术栈严格限制
      - JavaScript引擎：Rhino 1.7.13，强制ES5语法
      - UI技术：Android原生XML布局+AutoXjs内置组件
      - 严格禁止：WebView、HTML、CSS、前端框架、浏览器特性
      - 开发工具：VSCode+AutoXjs Extension，Git版本控制
    
      ## 核心开发原则(4大原则)
      1. 技术准确性第一：基于官方文档，99%准确率，禁止虚假信息
      2. 自然语言优先：易读易懂的自然语言描述，避免过度结构化
      3. 问题导向开发：解决实际问题，绝对禁止创建示例/测试/demo文件
      4. 质量保证优先：可读性、可维护性、性能优化，系统性分析
    
      ## 编码规范标准
      - 语法：ES5标准，4空格缩进，语句结尾加分号，中文注释
      - 命名：中文变量名+英文API，动词_名词格式，语义清晰
      - 质量：代码覆盖率≥80%，关键函数100%注释，完善try-catch
      - 性能：内存≤500MB，响应≤500ms，及时释放资源
    
      ## AutoXjs v6.5.8.17新增功能掌握
      - 网络检测：networkUtils.isWifiAvailable()、getWifiIPv4()
      - 屏幕信息：device.getCurWidth()、getCurHeight()、getCurOrientation()
      - 调试增强：traceLog()跟踪堆栈，比console.log更强大
      - 截图增强：images.captureScreen(true)强制返回新对象
      - UI控件：setTint()设置色调，setBackgroundGradient()渐变背景
      - 布局分析：text(&#x27;文本&#x27;).find(true)带刷新查找，提高成功率
    
      ## API使用规范(关键变更)
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ## 专家级问题解决流程(4阶段)
      1. 问题理解与上下文建立：深度分析、技术栈聚焦、影响范围确定
      2. 多层次诊断：项目内部+架构层面+技术文档对比+外部资源检索
      3. 双方案制定：方案A(标准实现)+方案B(替代方案)
      4. 实施与优化：精确实施、反思性改进、预防机制建立
    
      ## AI开发助手严格规则
      - 需求理解：禁止立即编码，3-5轮确认，需求拆解，影响分析
      - 代码检查：全面审查相关文件，依赖关系分析，重复代码识别
      - 方案设计：详细实现方案，文件修改清单，质量保证方案
      - 执行流程：需求确认→代码检查→方案设计→用户确认→执行开发
      - 强制工具：必须使用interactive_feedback工具确认关键决策
    
      ## 简洁代码编写规则(7大原则)
      1. 函数命名：动词_名词格式，≤5个中文字，语义清晰
      2. 参数设计：全部可调，不硬编码，合理默认值，路径灵活
      3. 功能整合：一个函数多用途，参数决定行为，避免过度拆分
      4. 代码结构：直线逻辑，最少抽象，核心逻辑集中
      5. 注释原则：极简注释，参数自解释，删除冗余
      6. 灵活性：时间可调，行为可选，路径不限，返回有用信息
      7. 实用性：解决实际问题，功能完整，易于理解，性能考虑
    
      ## 项目管理标准
      - 文件组织：main.js入口，ui/界面，scripts/逻辑，assets/资源
      - 开发流程：需求分析→设计→开发→测试→部署
      - 文档管理：开发文档、用户文档、技术文档同步更新
      - 版本控制：Git分支策略，feat/fix/docs提交规范
    </content>
    <tags>#最佳实践 #流程管理 #工具使用</tags>
  </item>
  <item id="mem_1754208620792_nwcpe8xp2" time="2025/08/03 16:10">
    <content>
      README.md项目规则深度学习与遵守承诺：
    
      ## 学习完成确认
      已通过promptx_learn指令深度学习了以下核心资源：
      1. @knowledge://magic-project-standards - Magic项目标准
      2. @knowledge://autoxjs-ozobiozobi-expertise - AutoXjs技术专业知识
      3. @execution://autoxjs-development-workflow - AutoXjs开发工作流程
      4. @execution://simple-code-rules - 简洁代码编写规则
    
      ## 严格遵守承诺
      我承诺在Magic项目的所有开发过程中严格遵守以下规则：
    
      ### 技术约束严格遵守
      - JavaScript ES5语法：绝不使用ES6+特性，确保Rhino 1.7.13兼容
      - UI技术限制：严禁WebView、HTML、CSS、前端框架，只用Android原生XML
      - API版本约束：所有代码基于AutoXjs ozobiozobi v6.5.8.17官方文档
      - 性能约束：内存≤500MB，响应≤500ms，支持Android 9+
    
      ### 强制性开发规则遵守
      - 技术准确性：严禁虚假信息，不确定时联网搜索官方文档确认
      - 问题导向：绝对禁止创建示例、测试、demo等非功能性文件
      - 代码规范：4空格缩进、分号结尾、中文注释、中文变量名
      - 安全编程：控件访问前检查存在性、完善try-catch异常处理
      - 资源管理：及时释放图像资源、清理全局变量、避免内存泄漏
    
      ### 开发工作流程严格执行
      - 第一阶段：任务理解和分析（深度需求分析+预检查准备）
      - 第二阶段：技术方案设计（架构设计+开发顺序规划）
      - 第三阶段：代码实现和测试（编码实现+质量保证）
      - 第四阶段：问题解决方法（深度分析+系统诊断+双方案制定）
    
      ### 简洁代码规则强制执行
      - 核心原则：用最少代码，最灵活参数，最简洁命名，解决最多问题
      - 函数命名：动词_名词格式，≤5个中文字，语义清晰直观
      - 参数设计：全部可调，不硬编码，提供合理默认值
      - 功能整合：一个函数多用途，通过参数控制不同行为
      - 注释极简：只在函数开头用一行说明用法，避免冗余解释
    
      ### API使用规范严格遵循
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ### 质量标准严格达成
      - 技术准确性：基于官方文档API使用率100%，技术信息准确率≥99%
      - 代码质量：代码规范遵循率100%，注释覆盖率≥80%，错误处理≥95%
      - 性能指标：内存≤500MB，响应≤500ms，Android 9+兼容性100%
      - 用户体验：界面操作直观性≥90%，功能学习成本≤30分钟
    
      ## 执行保证
      在每次开发任务中，我将：
      1. 严格按照4阶段开发流程执行
      2. 确保所有代码符合简洁代码7大原则
      3. 使用推荐的新API和XML布局规范
      4. 达到所有质量标准的量化指标
      5. 绝不违反任何技术约束和强制规则
    
      这是我对Magic项目开发规范的庄严承诺，将在所有开发工作中严格遵守执行。
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208668052_njoejb2dh" time="2025/08/03 16:11">
    <content>
      README.md项目规则深度学习与严格遵守承诺：
    
      ## 已完成的学习内容
      通过promptx_learn指令深度学习了README.md中的完整项目规范：
      1. Magic项目核心定位：AutoXjs ozobiozobi v6.5.8.17专业游戏辅助脚本
      2. 技术栈严格限制：ES5语法+Android原生XML，禁止WebView/HTML/CSS
      3. 4大核心开发原则：技术准确性第一、自然语言优先、问题导向开发、质量保证优先
      4. AutoXjs v6.5.8.17新增功能：网络检测、屏幕信息、调试增强、截图增强等
      5. API使用规范：ui.layout()替代setContentView()，attr()方法设置属性
      6. 专家级问题解决流程：4阶段系统性解决方案
      7. 简洁代码编写规则：7大原则，动词_名词命名，参数全可调
      8. AI开发助手严格规则：需求确认→代码检查→方案设计→用户确认→执行
    
      ## 严格遵守承诺
      我郑重承诺在Magic项目的所有开发工作中严格遵守以下规则：
    
      ### 技术约束绝对遵守
      - JavaScript引擎：只使用ES5语法，确保Rhino 1.7.13完全兼容
      - UI技术：严格禁止WebView、HTML、CSS、前端框架，只用Android原生XML
      - API版本：所有代码必须基于AutoXjs ozobiozobi v6.5.8.17官方文档
      - 性能要求：内存使用≤500MB，响应时间≤500ms，支持Android 9+
    
      ### 开发原则严格执行
      - 技术准确性第一：99%准确率，禁止虚假信息，不确定时联网搜索确认
      - 问题导向开发：绝对禁止创建示例/测试/demo文件，只解决实际问题
      - 自然语言优先：易读易懂的描述，避免过度结构化
      - 质量保证优先：可读性、可维护性、性能优化系统性分析
    
      ### 编码规范强制遵循
      - 语法风格：4空格缩进，语句结尾加分号，中文注释，中文变量名
      - 命名规范：动词_名词格式，≤5个中文字，语义清晰直观
      - 安全编程：控件访问前检查存在性，完善try-catch异常处理
      - 资源管理：及时释放图像资源，清理全局变量，避免内存泄漏
    
      ### API使用规范严格遵守
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，绝不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ### 简洁代码规则强制执行
      - 核心原则：用最少代码，最灵活参数，最简洁命名，解决最多问题
      - 函数命名：动词_名词格式，不超过5个中文字
      - 参数设计：全部可调，不硬编码，提供合理默认值
      - 功能整合：一个函数多用途，参数决定行为
      - 注释极简：只在函数开头用一行说明用法
    
      ### AI开发助手规则严格遵守
      - 需求理解：禁止立即编码，3-5轮确认，需求拆解，影响分析
      - 代码检查：全面审查相关文件，依赖关系分析，重复代码识别
      - 方案设计：详细实现方案，文件修改清单，质量保证方案
      - 执行流程：需求确认→代码检查→方案设计→用户确认→执行开发
    
      ### 专家级问题解决流程严格执行
      - 第一阶段：问题理解与上下文建立
      - 第二阶段：多层次诊断与双方案制定
      - 第三阶段：假设验证与方案制定
      - 第四阶段：实施与持续优化
    
      ## 执行保证声明
      我承诺在每次开发任务中：
      1. 严格按照README.md中的所有规则执行
      2. 绝不违反任何技术约束和强制规则
      3. 确保代码质量达到所有量化标准
      4. 使用推荐的新API和规范实现方式
      5. 遵循专家级问题解决流程
    
      这是我对Magic项目开发规范的庄严承诺，将在所有开发工作中严格遵守执行。
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
</memory>