24,866,243,80
close区域

425,2,110,176
右广告

3,4,124，151
左广告

1. 读取图片 (Image Loading)
作用：将图片文件加载到内存中，转换为可处理的数据格式
在AutoXjs中：images.read() 或 captureScreen()
目的：获取原始图像数据作为后续处理的基础
2. 高斯模糊 (Gaussian Blur)
作用：对图像进行平滑处理，减少噪声和细节
原理：使用高斯核对图像进行卷积运算
效果：
去除图像噪声
平滑边缘
为后续处理做准备
参数：模糊半径（值越大，模糊效果越强）
3. 灰度 (Grayscale)
作用：将彩色图像转换为灰度图像
原理：将RGB三个通道合并为单一灰度值
优势：
减少数据量
简化后续处理
提高处理速度
公式：Gray = 0.299*R + 0.587*G + 0.114*B
4. findContours (轮廓检测)
作用：在二值化图像中查找物体的轮廓
原理：检测图像中的边界线
返回：轮廓点的坐标集合
应用：
物体识别
形状分析
边界检测
5. matchShapes (形状匹配)
作用：比较两个轮廓的相似度
原理：使用Hu矩等方法计算形状特征
返回：相似度数值（越小越相似）
应用：
模板匹配
物体识别
形状分类
6. minAreaRect (最小外接矩形)
作用：找到包围轮廓的最小面积矩形
特点：矩形可以是旋转的
返回：矩形的中心点、宽高、旋转角度
应用：
物体定位
方向检测
尺寸测量
7. contourArea (轮廓面积)
作用：计算轮廓包围的面积
单位：像素平方
应用：
物体大小判断
过滤小噪声
面积阈值筛选
8. drawContours (绘制轮廓)
作用：在图像上绘制检测到的轮廓
参数：
目标图像
轮廓数据
颜色
线条粗细
用途：
可视化调试
结果展示
标记检测区域

@d:\magic/脚本\算数\看广告.js 要求一：先修改看广告.js里面的功能，按以下要求修改：我们不需要读取大图小图，而是在屏幕三个区域中查找，遍历三个目录的图片模板，现在帮我做一个轮廓检测功能，函数名：看广告，处理图片的大概步骤
读取图片--> 高斯模糊--> 灰度-->--> findContours-->
matchShapes-->minAreaRect--> contourArea--> drawContours
百度了一下, 说是matchShapes参数传错, 正常返回值最大是1, 最小是0,
把matchShapes参数改一下, 相似度调整为0.03
(相似度越低越相似)
我们现在有三个区域，先用var = 定义三个区域，（24,866,243,80，close区域），（425,2,110,176，右广告），（3,4,124，151
左广告），分别对应的目录，广告\右广告，广告\左广告，广告\close区域，要遍历目录下的图片，要对图片做预处理，路径要使用安卓的相对路径，要有截图保存功能，让我可以看到对图片处理的效果，保存在/storage/emulated/0/目录中，可以直接测试，先注释导出模块的函数，要做一个单函数多接口的功能，要能通用，找到图片则返回位置，他的相似度，各种信息。，还有请你备注一下我们用了什么功能。




