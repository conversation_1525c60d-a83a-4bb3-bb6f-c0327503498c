/**
 * 文件操作模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

function 读取_配置(文件编号, 项目编号) {
    try {
        // 设置默认参数
        文件编号 = 文件编号 || 1;  // 默认使用google1.txt，1-10000表示google1.txt到google10000.txt
        项目编号 = 项目编号 || 1;  // 1=邮箱，2=密码，3=其他信息

        // 构建文件路径 - 统一使用带编号格式
        var 文件路径 = "/storage/emulated/0/Pictures/google" + 文件编号 + ".txt";

        console.log("📁 读取配置文件: " + 文件路径 + " 第" + 项目编号 + "项");

        // 检查文件是否存在
        if (!files.exists(文件路径)) {
            console.log("❌ 配置文件不存在: " + 文件路径);
            return null;
        }

        // 读取文件内容
        var 文件内容 = files.read(文件路径);
        if (!文件内容) {
            console.log("❌ 配置文件读取失败或为空");
            return null;
        }

        // 去除首尾空格并按逗号分割
        var 项目数组 = 文件内容.trim().split(',');

        // 检查项目编号是否有效
        if (项目编号 < 1 || 项目编号 > 项目数组.length) {
            console.log("❌ 项目编号超出范围: " + 项目编号 + " (文件共" + 项目数组.length + "项)");
            return null;
        }

        // 获取指定项目内容并去除首尾空格
        var 内容 = 项目数组[项目编号 - 1].trim();

        if (!内容) {
            console.log("⚠️ 第" + 项目编号 + "项内容为空");
            return null;
        }

        console.log("✅ 读取成功: " + 内容);
        return 内容;

    } catch (error) {
        console.error("❌ 读取配置时发生错误: " + error);
        return null;
    }
}

// 导出模块
module.exports = {
    读取_配置: 读取_配置
};

