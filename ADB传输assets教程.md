# ADB传输assets目录到雷电模拟器教程

## 🎯 目标
将本地 `D:\magic\assets` 目录传输到雷电模拟器的 `/storage/emulated/0/magic/assets/` 位置，直接覆盖现有文件。

## 📋 前提条件

### 1. 确保ADB已安装
- 雷电模拟器自带ADB工具
- 或者安装Android SDK Platform Tools

### 2. 启动雷电模拟器
- 打开雷电模拟器
- 确保模拟器正常运行

### 3. 开启ADB调试
- 在模拟器中打开 **设置** → **关于手机**
- 连续点击 **版本号** 7次开启开发者模式
- 返回设置 → **开发者选项** → 开启 **USB调试**

## 🚀 传输步骤

### 步骤1：打开命令行
```cmd
# 按Win+R，输入cmd，回车打开命令提示符
# 或者在开始菜单搜索"命令提示符"
```

### 步骤2：连接雷电模拟器
```cmd
# 雷电模拟器默认ADB端口是5555
adb connect 127.0.0.1:5555
```

**预期输出：**
```
connected to 127.0.0.1:5555
```

### 步骤3：验证连接
```cmd
adb devices
```

**预期输出：**
```
List of devices attached
127.0.0.1:5555  device
```
1. 传输整个magic目录：
adb push "D:\magic" "/storage/emulated/0/脚本/"

### 步骤4：创建目标目录
```cmd
adb shell mkdir -p /storage/emulated/0/magic
```

### 步骤5：传输assets目录
```cmd
# 切换到magic项目目录
cd /d D:\magic

# 传输整个assets目录（递归传输，覆盖现有文件）
adb push assets /storage/emulated/0/magic/
```

**传输过程示例：**
```
assets\通用模板\跳过.png: 1 file pushed, 0 skipped. 15.2 MB/s (2341 bytes in 0.000s)
assets\google\邮箱.png: 1 file pushed, 0 skipped. 18.7 MB/s (3456 bytes in 0.000s)
...
```

### 步骤6：验证传输结果
```cmd
# 查看目标目录内容
adb shell ls -la /storage/emulated/0/magic/assets/

# 查看通用模板目录
adb shell ls -la /storage/emulated/0/magic/assets/通用模板/
```

## 🔧 常用ADB命令

### 连接相关
```cmd
# 连接模拟器
adb connect 127.0.0.1:5555
adb connect emulator-5556

# 断开连接
adb disconnect 127.0.0.1:5555

# 查看已连接设备
adb devices
```


🔧 ADB命令大集合：快速上手指南 📱
ADB（Android Debug Bridge）是Android开发中不可或缺的工具。以下是一些常用的ADB命令，帮助你快速上手：

1️⃣ adb shell：进入设备内核。
2️⃣ adb version：查看ADB版本号。
3️⃣ adb devices：列出所有连接的设备。
4️⃣ adb install：安装应用。
5️⃣ adb uninstall：卸载应用。
6️⃣ adb pull：从设备下载文件。
7️⃣ adb push：将文件上传到设备。
8️⃣ adb shell pm list packages -3：查看第三方应用包名。
9️⃣ adb shell am monitor：执行命令后点击需要查询的应用，查看该应用包名。
🔟 adb shell screencap -p：截图。
1️⃣1️⃣ adb shell screenrecord：录制视频。
1️⃣2️⃣ adb monkey -p 报名 -v-v 次数：进行压力测试。
1️⃣3️⃣ adb logcat | findstr 关键词：查找日志中的关键词。
1️⃣4️⃣ adb shell getprop ro.product.model：查看设备型号。

掌握这些命令，你
### 应用包名查看
```cmd
# 查看所有已安装应用的包名
adb shell pm list packages

# 查看第三方应用包名（不包含系统应用）
adb shell pm list packages -3

# 查看系统应用包名
adb shell pm list packages -s

# 根据关键词搜索应用包名
adb shell pm list packages | grep "关键词"

adb shell pm list packages | grep Brain

# 查看当前运行的应用包名
adb shell dumpsys window | grep mCurrentFocus

# 查看指定应用的详细信息
adb shell dumpsys package 包名

# 示例：查看AutoXjs相关应用
adb shell pm list packages | grep auto
```

### 文件操作
```cmd
# 传输单个文件
adb push "本地文件路径" "/storage/emulated/0/目标路径/"

# 传输整个目录
adb push "本地目录" "/storage/emulated/0/目标目录/"

# 从模拟器下载文件
adb pull "/storage/emulated/0/文件路径" "本地保存路径"

# 查看目录内容
adb shell ls -la "/storage/emulated/0/路径/"

# 删除文件或目录
adb shell rm -rf "/storage/emulated/0/路径/"
```

## ⚠️ 注意事项

### 1. 路径问题
- Windows路径使用反斜杠 `\` 或正斜杠 `/`
- Android路径必须使用正斜杠 `/`
- 包含空格的路径要用引号包围

### 2. 权限问题
- 确保模拟器已开启USB调试
- 某些系统目录可能需要root权限

### 3. 文件覆盖
- `adb push` 默认会覆盖同名文件
- 如果需要保留原文件，请先备份

### 4. 中文文件名
- 支持中文文件名和目录名
- 建议使用UTF-8编码

## 🎮 针对Magic项目的快速命令

### 一键传输assets
```cmd
cd /d D:\magic
adb connect 127.0.0.1:5555
adb shell mkdir -p /storage/emulated/0/magic
adb push assets /storage/emulated/0/magic/
```

### 验证传输
```cmd
adb shell ls -la /storage/emulated/0/magic/assets/通用模板/
adb shell ls -la /storage/emulated/0/magic/assets/google/
```

### 查看特定文件
```cmd
adb shell ls -la /storage/emulated/0/magic/assets/通用模板/跳过.png
```

## 🔄 故障排除

### 问题1：连接失败
```cmd
# 重启ADB服务
adb kill-server
adb start-server
adb connect 127.0.0.1:5555
```

### 问题2：端口被占用
```cmd
# 雷电模拟器可能使用不同端口，尝试：
adb connect 127.0.0.1:5554
adb connect 127.0.0.1:5556
```

### 问题3：权限不足
- 确保模拟器开启了USB调试
- 重启模拟器后重新连接

### 问题4：传输中断
```cmd
# 重新传输
adb push assets /storage/emulated/0/magic/
```

## ✅ 完成确认

传输完成后，您的雷电模拟器中应该有：
- `/storage/emulated/0/magic/assets/通用模板/跳过.png`
- `/storage/emulated/0/magic/assets/google/` 目录及其文件
- 其他assets目录中的所有文件和子目录

现在您可以在AutoXjs脚本中使用这些资源文件了！🎮
