/**
 * 检查分数功能模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 */

// 导入截图模块 - 使用相对路径
var 截图模块 = require('./截图.js');

/**
 * 检查分数函数
 * 功能：全屏截图保存为分数提示.png，查找分数确认键并点击验证
 * @returns {boolean} 成功返回true，失败返回false
 */
function 检查_分数() {
    try {
        console.log("🔍 开始检查分数流程");

        // 第一步：在分数提示大图中查找分数确认键并点击（重试4次）
        console.log("📋 步骤1: 在分数提示大图中查找分数确认键（最多重试4次）");
        var 确认键成功 = false;
        var 重试次数 = 0;
        var 最大重试 = 4;

        while (!确认键成功 && 重试次数 < 最大重试) {
            重试次数++;
            console.log("🔄 第" + 重试次数 + "次尝试在分数提示大图中查找分数确认键");

            // 读取分数提示大图
            var 分数提示大图 = images.read("/storage/emulated/0/magic/assets/通用模板/分数提示.png");
            if (!分数提示大图) {
                console.log("❌ 无法读取分数提示大图");
                break;
            }

            // 读取分数确认键小图
            var 分数确认键小图 = images.read("/storage/emulated/0/magic/assets/通用模板/分数确认键.png");
            if (!分数确认键小图) {
                console.log("❌ 无法读取分数确认键小图");
                分数提示大图.recycle();
                break;
            }

            // 在大图中查找小图
            var 找到位置 = images.findImage(分数提示大图, 分数确认键小图, {
                threshold: 0.8,  // 相似度阈值
                region: [0, 0, 分数提示大图.getWidth(), 分数提示大图.getHeight()]
            });

            // 释放图片资源
            分数提示大图.recycle();
            分数确认键小图.recycle();

            if (找到位置) {
                console.log("✅ 在分数提示大图中找到分数确认键，位置: (" + 找到位置.x + "," + 找到位置.y + ")");

                // 在找到的位置随机点击
                var 随机偏移X = random(-10, 10);
                var 随机偏移Y = random(-10, 10);
                var 点击X = 找到位置.x + 随机偏移X;
                var 点击Y = 找到位置.y + 随机偏移Y;

                console.log("🎯 在位置 (" + 点击X + "," + 点击Y + ") 随机点击");
                click(点击X, 点击Y);

                确认键成功 = true;
                break;
            } else {
                console.log("⚠️ 第" + 重试次数 + "次尝试未在分数提示大图中找到分数确认键");

                // 如果不是最后一次重试，则等待后继续
                if (重试次数 < 最大重试) {
                    var 重试等待 = random(1000, 2000);
                    console.log("⏳ 等待 " + 重试等待 + " 毫秒后进行下一次重试");
                    sleep(重试等待);
                }
            }
        }

        if (!确认键成功) {
            console.log("⚠️ 经过" + 最大重试 + "次重试仍未在分数提示大图中找到分数确认键");
        }

        // 第二步：查找是否在游戏画面并点击结束教程（重试8次）
        var 游戏画面成功 = false;
        if (确认键成功) {
            // 随机延时0.2-0.5秒
            var 游戏画面等待 = random(200, 500);
            console.log("⏳ 分数确认键成功后等待 " + 游戏画面等待 + " 毫秒后检查游戏画面");
            sleep(游戏画面等待);

            console.log("📋 步骤2: 查找游戏画面（最多重试8次）");
            var 游戏画面重试次数 = 0;
            var 游戏画面最大重试 = 8;

            while (!游戏画面成功 && 游戏画面重试次数 < 游戏画面最大重试) {
                游戏画面重试次数++;
                console.log("🔄 第" + 游戏画面重试次数 + "次尝试查找结束教程");

                游戏画面成功 = 截图模块.查找_图片(
                    "/storage/emulated/0/magic/assets/通用模板/结束教程.png",
                    0, 0, device.width, device.height,  // 全屏范围
                    1, 200,            // 1秒内每200毫秒查找一次
                    "判断"             // 判断模式：找到则点击，找不到则跳过
                );

                if (游戏画面成功) {
                    console.log("✅ 结束教程点击成功（第" + 游戏画面重试次数 + "次尝试）");

                    // 点击后验证是否还在结束教程界面
                    sleep(1000); // 等待1秒让界面响应
                    var 游戏画面验证结果 = 截图模块.查找_图片(
                        "/storage/emulated/0/magic/assets/通用模板/结束教程.png",
                        0, 0, device.width, device.height,  // 全屏范围
                        2, 200,            // 2秒内每200毫秒查找一次
                        "查找"             // 查找模式：只查找不点击，返回位置或null
                    );

                    if (游戏画面验证结果 === null) {
                        console.log("✅ 验证成功：已离开结束教程界面");
                        游戏画面成功 = true;
                        break;
                    } else {
                        console.log("⚠️ 验证失败：仍在结束教程界面，点击未生效，继续重试");
                        游戏画面成功 = false;
                        // 继续循环，返回重新查找和点击
                    }
                } else {
                    console.log("⚠️ 第" + 游戏画面重试次数 + "次尝试未找到结束教程");

                    // 如果不是最后一次重试，则等待后继续
                    if (游戏画面重试次数 < 游戏画面最大重试) {
                        var 游戏画面重试等待 = random(1000, 2000);
                        console.log("⏳ 等待 " + 游戏画面重试等待 + " 毫秒后进行下一次重试");
                        sleep(游戏画面重试等待);
                    }
                }
            }

            if (!游戏画面成功) {
                console.log("⚠️ 经过" + 游戏画面最大重试 + "次重试仍未找到结束教程或点击未生效");
            }
        } else {
            console.log("⚠️ 分数确认键未成功，跳过游戏画面检查步骤");
            // 第一步失败，第二步也不执行
            游戏画面成功 = false;
        }

        // 检查分数结束
        console.log("🔍 检查分数流程完成");
        return 确认键成功 && 游戏画面成功;

    } catch (e) {
        console.error("❌ 检查分数过程中发生错误:", e);
        return false;
    }
}

// 导出模块
module.exports = {
    检查_分数: 检查_分数
};

// // 直接运行测试（单独运行时取消注释下面3行）
// console.log("🧪 开始测试检查分数功能");
// var 结果 = 检查_分数();
// console.log("🎯 测试结果: " + (结果 ? "✅ 成功" : "❌ 失败"));
