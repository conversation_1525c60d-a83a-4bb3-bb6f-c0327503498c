# Magic 游戏辅助脚本 - 前端UI设计文档

## 项目概述

基于AutoXjs ozobiozobi v6.5.8.17开发的游戏辅助脚本应用，采用原生Android XML UI设计，使用统一的成功绿色主题（#4CAF50），提供现代化、美观的用户界面。

**当前开发状态**: ✅ **前端UI已完成开发**
- 所有四个页面UI已完成实现
- 图标系统和样式系统已完善
- 权限管理和交互逻辑已实现
- 悬浮球功能已集成

## 设计规范

### 颜色主题
- **主色调**: 成功绿 #4CAF50
- **背景色**: 主背景 #F5F5F5，次背景 #FFFFFF
- **文字色**: 主文字 #000000，次文字 #666666，反色文字 #FFFFFF
- **状态色**: 成功 #4CAF50，警告 #FF9800，错误 #F44336，信息 #2196F3

### 尺寸规范
- **圆角**: 小4dp，中8dp，大16dp，按钮12dp
- **间距**: 小8dp，中16dp，大24dp
- **字体**: 小12sp，中14sp，大16sp，超大18sp，标题20sp
- **控件高度**: 按钮48dp，工具栏56dp，列表项56dp

### 图标系统
- **图标库**: FontAwesome 5.15.2图标库，通过通用图标管理器统一管理
- **图标大小**: 导航20sp，操作16sp，状态14sp，装饰48sp，权限24sp
- **图标颜色**: 白色用于绿色背景，绿色用于白色背景，确保对比度
- **字体管理**: 支持FontAwesome、Roboto、RobotoBold三种字体
- **应用方式**: 通过语义化API自动应用，支持批量配置

## 四个页面UI设计需求

---

## 第一步：主页界面设计 ✅ **已完成**

### 整体布局结构
采用垂直线性布局（vertical），包含顶部导航栏、主内容滚动区域、底部控制按钮区、底部导航栏四个主要部分。

**实现文件**: `ui/主页/UI主页页面.js`
**逻辑文件**: `ui/主页/主页逻辑.js`

### 顶部导航栏设计
- **容器**: 水平线性布局（horizontal），高度56dp，背景成功绿色
- **左侧菜单按钮**:
  - 尺寸56dp×56dp，透明背景
  - FontAwesome菜单图标（fa-bars），22sp大小，白色
  - 点击触发侧滑抽屉
- **中央标题**:
  - 文字"主页"，18sp字体，白色，粗体
  - 居中对齐，占据剩余空间
- **右侧占位**: 56dp×56dp空白区域，保持视觉平衡

### 主内容滚动区域
- **外层容器**: ScrollView，占据剩余垂直空间，背景浅灰色
- **内层容器**: 垂直布局，16dp内边距

#### 游戏数据卡片
- **卡片容器**: card组件，cardBackgroundColor="#FFFFFF"，cardCornerRadius="16dp"，cardElevation="2dp"
- **标题栏**:
  - 水平布局，16dp内边距
  - 左侧"游戏数据"文字，16sp粗体黑色
  - 右侧操作按钮组：
    - 刷新按钮：40dp×40dp，style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"，FontAwesome刷新图标（fa-sync），16sp白色
    - 删除按钮：40dp×40dp，style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"，FontAwesome删除图标（fa-trash），16sp白色
- **数据内容区**:
  - ScrollView容器，200dp高度
  - 空状态：居中显示FontAwesome文档图标（fa-file-alt），48sp灰色，配文字说明
  - 数据列表：动态添加数据条目，每条包含键值对显示

#### 脚本状态卡片
- **卡片容器**: card组件，cardBackgroundColor="#FFFFFF"，cardCornerRadius="16dp"，cardElevation="2dp"
- **内容布局**: 垂直布局，24dp内边距
- **标题**: "脚本状态"文字，16sp粗体黑色，底部8dp间距
- **状态指示**:
  - 水平布局，垂直居中对齐
  - 状态圆点：16sp圆形符号，颜色根据状态变化（灰色/绿色）
  - 状态文字：14sp中等字体，颜色随状态变化

### 底部控制按钮区
- **容器**: 水平布局，16dp内边距
- **运行按钮**:
  - 占据一半宽度，48dp高度，使用Material Design样式
  - style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"
  - FontAwesome播放图标（fa-play）+ "运行"文字，白色，16sp粗体
  - 右侧8dp外边距
- **停止按钮**:
  - 占据一半宽度，48dp高度，使用Material Design样式
  - style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"
  - FontAwesome停止图标（fa-stop）+ "停止"文字，白色，16sp粗体
  - 左侧8dp外边距

### 底部导航栏
- **容器**: 水平布局，50dp高度，绿色背景，8dp阴影
- **导航项目**: 三个等宽垂直布局容器，8dp内边距，可点击
  - 脚本配置：FontAwesome设置图标（fa-cog），20sp，半透明白色/纯白色
  - 主页：FontAwesome主页图标（fa-home），20sp，纯白色（当前页面）
  - 日志：FontAwesome文档图标（fa-file-alt），20sp，半透明白色/纯白色

### 主页界面开发状态
- ✅ **已完成组件**:
  - 顶部导航栏（菜单按钮、标题、占位区域）
  - 游戏数据卡片（标题栏、操作按钮、数据显示区域、空状态）
  - 脚本状态卡片（状态指示器、状态文字）
  - 底部控制按钮（运行按钮、停止按钮）
  - 底部导航栏（三个导航项）
  - 侧滑抽屉集成（遮罩层、抽屉面板）
- ✅ **已实现功能**:
  - 动态游戏数据更新和显示
  - 脚本状态实时更新
  - 气泡提示系统
  - 图标自动应用
  - 抽屉权限管理

---

## 第二步：脚本配置页界面设计 ✅ **已完成**

### 整体布局结构
采用垂直线性布局，包含顶部导航栏、主内容滚动区域、底部操作按钮区、底部导航栏。

**实现文件**: `ui/脚本配置页/UI脚本页面.js`
**逻辑文件**: `ui/脚本配置页/脚本逻辑.js`

### 顶部导航栏设计
- **布局**: 与主页相同的水平布局结构
- **左侧菜单按钮**: FontAwesome菜单图标，白色，22sp
- **中央标题**:
  - 文字"脚本配置"，18sp白色粗体
  - 左对齐布局，16dp左边距，实现视觉居中效果
- **右侧**: 无内容，自然留白

### 主内容滚动区域
- **外层**: ScrollView，浅灰背景，占据剩余空间
- **内层**: 垂直布局，16dp内边距

#### 游戏配置卡片
- **卡片容器**: card组件，cardBackgroundColor="#FFFFFF"，cardCornerRadius="16dp"，cardElevation="2dp"
- **标题**: "游戏配置"，16sp粗体黑色，16dp内边距
- **配置项目**: 每项采用水平布局，16dp内边距，56dp最小高度
  - **自动启动游戏**:
    - 左侧文字标签，14sp黑色，占据剩余空间
    - 右侧Switch开关，关闭时浅灰轨道+白色按钮，开启时绿色轨道+白色按钮
  - **过游戏教程**: 同上布局和样式
  - **自动玩游戏**: 同上布局和样式
  - **每日领IOTA币**: 同上布局和样式

#### 操作配置卡片
- **卡片容器**: 同游戏配置卡片样式
- **标题**: "操作配置"，样式同上
- **左键设置项**:
  - 主项：水平布局，文字+开关，样式同游戏配置项
  - 坐标输入区：垂直布局，默认隐藏，开关开启时显示
    - X/Y坐标输入框：水平排列，各占一半宽度，8dp间距
    - 宽度/高度输入框：水平排列，布局同上
    - 输入框样式：background="#FAFAFA"，radius="8dp"，padding="8dp"，数字输入类型
- **右键设置项**: 同左键设置项的完整布局和样式

#### 分数控制卡片
- **卡片容器**: 同游戏配置卡片样式
- **标题**: "分数控制"，样式同上
- **分数暂停开关**:
  - 主项：水平布局，"分数暂停"文字+开关，样式同游戏配置项
  - 分数输入区：垂直布局，默认隐藏，开关开启时显示
    - 最低/最高分数输入框：水平排列，各占一半宽度，8dp间距
    - 输入框样式：background="#FAFAFA"，radius="8dp"，padding="8dp"，数字输入类型
    - 标签文字：12sp灰色，4dp下边距，分别显示"最低"和"最高"

#### 广告配置卡片
- **卡片容器**: 同游戏配置卡片样式
- **标题**: "广告配置"，样式同上
- **广告左区域设置**:
  - 标题文字："广告左区域"，14sp黑色，16dp内边距
  - 坐标输入：X/Y坐标输入框，水平排列，样式同操作配置
  - 尺寸输入：宽度/高度输入框，水平排列，样式同操作配置
- **广告右区域设置**:
  - 标题文字："广告右区域"，14sp黑色，16dp内边距
  - 坐标输入：X/Y坐标输入框，水平排列，样式同操作配置
  - 尺寸输入：宽度/高度输入框，水平排列，样式同操作配置

### 底部操作按钮区
- **容器**: 水平布局，白色背景，16dp内边距，4dp阴影
- **保存配置按钮**:
  - 占据一半宽度，48dp高度，使用Material Design样式
  - style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"
  - FontAwesome保存图标（fa-save）+ "保存配置"文字，白色，16sp粗体
  - 右侧8dp外边距
- **重置配置按钮**:
  - 占据一半宽度，48dp高度，使用Material Design样式
  - style="Widget.AppCompat.Button.Colored"，backgroundTint="#4CAF50"
  - FontAwesome撤销图标（fa-undo）+ "重置配置"文字，白色，16sp粗体
  - 左侧8dp外边距

### 底部导航栏
- **布局**: 与主页相同结构
- **当前页面**: 脚本配置图标显示为纯白色，其他为半透明白色

### 脚本配置页面开发状态
- ✅ **已完成组件**:
  - 顶部导航栏（菜单按钮、标题）
  - 游戏配置卡片（自动启动游戏、过游戏教程、自动玩游戏、每日领IOTA币）
  - 操作配置卡片（左键设置、右键设置，包含坐标和尺寸输入）
  - 分数控制卡片（分数到达暂停，包含最低最高分数输入）
  - 广告配置卡片（自动看广告开关、广告左右区域坐标设置）
  - 底部操作按钮（保存配置、重置配置）
  - 底部导航栏
- ✅ **已实现功能**:
  - 所有开关控件的状态管理
  - 输入框的数据验证和格式化
  - 配置数据的持久化存储
  - 按钮事件处理和用户反馈
  - 表单数据的保存和重置
  - 图标自动应用和样式统一

---

## 第三步：日志页界面设计 ✅ **已完成**

### 整体布局结构
采用垂直线性布局，包含顶部导航栏、主内容滚动区域、底部导航栏。

**实现文件**: `ui/日志页/UI日志页面.js`
**逻辑文件**: `ui/日志页/日志逻辑.js`

### 顶部导航栏设计
- **布局**: 水平布局，56dp高度，绿色背景，4dp阴影
- **左侧菜单按钮**: FontAwesome菜单图标，白色，22sp，56dp×56dp
- **中央标题**: "日志"文字，18sp白色粗体，居中对齐
- **右侧清空按钮**:
  - 56dp×56dp，透明背景
  - FontAwesome删除图标（fa-trash），18sp白色
  - 点击清空所有日志

### 主内容滚动区域
- **外层**: ScrollView，浅灰背景，占据剩余空间
- **内层**: 垂直布局，16dp内边距

#### 日志卡片
- **卡片容器**: card组件，cardBackgroundColor="#FFFFFF"，cardCornerRadius="16dp"，cardElevation="2dp"
- **标题栏**:
  - 水平布局，16dp内边距，垂直居中对齐
  - 左侧"运行日志"文字，16sp粗体黑色，占据剩余空间
  - 右侧日志计数，12sp灰色文字，显示"X 条记录"

#### 日志内容区域
- **容器**: 垂直布局，16dp内边距
- **空状态显示**:
  - 垂直布局，40dp内边距，居中对齐
  - FontAwesome文档图标（fa-file-alt），48sp灰色，60%透明度
  - "暂无日志记录"文字，16sp粗体黑色，8dp上边距
  - "脚本运行后将显示日志信息"说明文字，14sp灰色
- **日志列表容器**:
  - 默认隐藏，有日志时显示
  - ScrollView，300dp固定高度，支持垂直滚动
  - 内部垂直布局，8dp内边距
  - 日志条目动态添加，每条包含：
    - 水平布局，8dp内边距，垂直居中对齐
    - 左侧状态图标：24dp宽度，14sp大小，颜色根据日志类型变化
    - 右侧内容区：垂直布局，8dp左边距
      - 日志内容文字：14sp黑色
      - 时间戳文字：12sp灰色，2dp右边距

### 底部导航栏
- **布局**: 与主页相同结构
- **当前页面**: 日志图标显示为纯白色，其他为半透明白色

### 日志页面开发状态
- ✅ **已完成组件**:
  - 顶部导航栏（菜单按钮、标题、清空按钮）
  - 日志卡片（标题栏、日志计数）
  - 日志内容区域（空状态显示、日志列表容器）
  - 日志滚动区域（300dp固定高度）
  - 底部导航栏
- ✅ **已实现功能**:
  - 动态日志条目添加和显示
  - 日志类型分类（成功、警告、错误、信息）
  - 日志计数实时更新
  - 清空所有日志功能
  - 自动滚动到最新日志
  - 空状态和数据状态切换
  - 气泡提示系统

---

## 第四步：侧滑抽屉界面设计 ✅ **已完成**

### 整体布局结构
采用帧布局（frame），包含遮罩层和抽屉面板，默认隐藏，点击菜单按钮时显示。

**实现文件**: `ui/菜单抽屉页/侧滑抽屉.js`（集成在主页布局中）

### 遮罩层设计
- **容器**: 垂直布局，全屏尺寸，半透明黑色背景（#80000000）
- **交互**: 可点击，点击时关闭抽屉
- **初始状态**: visibility="gone"

### 抽屉面板设计
- **容器**: 垂直布局，280dp宽度，全屏高度，绿色背景（#4CAF50）
- **初始状态**: visibility="gone"
- **动画**: 从左侧滑入滑出效果

#### 顶部应用信息卡片
- **卡片容器**: card组件，12dp外边距（上下左12dp，右6dp），cardBackgroundColor="#FFFFFF"，cardCornerRadius="12dp"，cardElevation="4dp"
- **内容布局**: 垂直布局，白色背景，14dp内边距
- **应用图标**: FontAwesome魔法棒图标（fa-magic），36sp绿色，居中对齐，5dp下边距
- **应用名称**: "Magic 游戏助手"，16sp粗体深绿色，居中对齐
- **版本信息**: "v1.0.0"，10sp浅绿色，居中对齐，1dp上边距
- **技术信息**: "基于 AutoXjs ozobiozobi v6.5.8.17"，8sp浅绿色，居中对齐，2dp上边距

#### 滚动内容区域
- **容器**: ScrollView，占据剩余空间，4dp内边距

##### 权限管理卡片
- **卡片容器**: card组件，6dp外边距，cardBackgroundColor="#FFFFFF"，cardCornerRadius="12dp"，cardElevation="2dp"
- **内容布局**: 垂直布局，白色背景，10dp内边距
- **标题**: "权限管理"，14sp粗体深绿色，8dp下边距

###### 权限项目设计（三个权限项）
每个权限项采用相同的布局结构：
- **外层容器**: 垂直布局，浅绿背景（#F1F8E9），8dp圆角，6dp内边距，2dp下边距
- **主要内容**: 水平布局，垂直居中对齐
  - **权限图标**: 14sp深绿色，10dp右边距，垂直居中
    - 无障碍服务：FontAwesome盾牌图标（fa-shield-alt）
    - 悬浮窗权限：FontAwesome手机图标（fa-mobile）
    - 屏幕截图：FontAwesome相机图标（fa-camera）
  - **权限名称**: 14sp粗体深绿色，占据剩余空间，垂直居中
  - **权限开关**: Switch组件，关闭时浅灰轨道，开启时绿色轨道，白色按钮
- **权限说明**: 10sp浅绿色，24dp左边距（与图标对齐）

##### 关于应用卡片
- **卡片容器**: card组件，6dp外边距（底部12dp），cardBackgroundColor="#FFFFFF"，cardCornerRadius="12dp"，cardElevation="2dp"
- **内容布局**: 水平布局，48dp高度，白色背景，12dp内边距，垂直居中，可点击
- **关于图标**: FontAwesome信息图标（fa-info-circle），14sp深绿色，10dp右边距
- **关于文字**: "关于应用"，14sp粗体深绿色，占据剩余空间
- **箭头图标**: FontAwesome右箭头图标（fa-arrow-right），14sp浅绿色

### 交互行为设计
- **打开抽屉**: 点击任意页面的菜单按钮，显示遮罩和面板
- **关闭抽屉**: 点击遮罩区域或再次点击菜单按钮
- **权限开关**: 点击切换状态，显示对应的权限申请对话框
- **关于应用**: 点击显示应用信息对话框
- **导航功能**: 抽屉中的导航项点击后不自动关闭抽屉

### 侧滑抽屉开发状态
- ✅ **已完成组件**:
  - 顶部应用信息卡片（应用图标、名称、版本、技术信息）
  - 权限管理卡片（6个权限项：无障碍、悬浮窗、前台服务、存储、通知访问、截图）
  - 关于应用卡片（点击显示应用信息）
  - 遮罩层（透明背景、点击关闭）
- ✅ **已实现功能**:
  - 完整的权限管理系统（检查、申请、开启、关闭）
  - 权限状态实时同步和显示
  - 抽屉开关动画和交互
  - 权限申请流程和用户引导
  - 错误处理和状态回滚
  - 资源清理和内存管理

## 第五步：悬浮球功能设计 ✅ **已完成**

### 悬浮球系统架构
悬浮球功能采用模块化设计，通过悬浮球管理系统统一管理。

**实现文件**: `ui/主页/悬浮球/悬浮球管理系统.js`
**相关文件**: `ui/主页/悬浮球/悬浮球.js`

### 悬浮球功能特点
- ✅ **主悬浮球**: 生化危险图标，绿色背景，可拖拽
- ✅ **功能面板**: 点击主球展开，包含开始、重启、主页、日志四个功能按钮
- ✅ **权限管理**: 自动检查悬浮窗权限，引导用户授权
- ✅ **状态管理**: 启动、停止、状态查询等完整生命周期管理
- ✅ **图标系统**: 集成FontAwesome图标，支持动态应用
- ✅ **交互优化**: 点击外部区域自动收起，防误触设计

### 悬浮球开发状态
- ✅ **已完成组件**:
  - 主悬浮球（生化危险图标、拖拽功能）
  - 功能面板（开始、重启、主页、日志按钮）
  - 权限检查和申请流程
  - 悬浮球管理系统
- ✅ **已实现功能**:
  - 悬浮球启动和停止
  - 权限状态检查和同步
  - 图标自动应用
  - 状态管理和错误处理
  - 与主应用的集成

---

## 开发技术要求

### AutoXjs API使用规范
- 使用ui.layout()加载XML布局
- 使用ui.控件ID访问控件
- 使用控件.on("click", function(){})绑定事件
- 使用控件.attr()方法设置属性
- 使用控件.setText()、setTextColor()等方法更新内容
- 使用ui.inflate()动态创建布局
- 使用ui.post()确保UI操作在主线程执行

### 圆角设置技术规范
#### Card控件圆角设置
- **正确方法**: 使用 `cardBackgroundColor` + `cardCornerRadius` 属性
- **错误方法**: 使用 `background` 属性（会覆盖圆角效果）
- **示例代码**:
  ```xml
  <card
      cardBackgroundColor="#FFFFFF"
      cardCornerRadius="16dp"
      cardElevation="2dp">
  ```

#### Button控件圆角设置
- **正确方法**: 使用官方Material Design样式
- **推荐样式**: `style="Widget.AppCompat.Button.Colored"`
- **背景色设置**: 使用 `backgroundTint` 而不是 `background`
- **示例代码**:
  ```xml
  <button
      style="Widget.AppCompat.Button.Colored"
      backgroundTint="#4CAF50"
      textColor="#FFFFFF">
  ```

#### Input控件圆角设置
- **方法**: 使用 `radius` 属性
- **推荐值**: 8dp（小圆角，适合输入框）
- **示例代码**:
  ```xml
  <input
      background="#FAFAFA"
      radius="8dp"
      padding="8dp">
  ```

### 响应式设计要求
- 支持不同屏幕尺寸的Android设备
- 使用dp单位确保在不同密度屏幕上的一致性
- 使用layout_weight实现弹性布局
- 合理使用ScrollView处理内容溢出

### 性能优化要求
- 避免过深的布局嵌套
- 合理使用visibility控制显示隐藏
- 图标使用字体图标而非图片资源
- 适当使用elevation和阴影效果提升视觉层次

### 兼容性要求
- 兼容AutoXjs ozobiozobi v6.5.8.17版本
- 使用稳定的API，避免实验性功能
- 确保在Android 7.0+系统上正常运行
- 处理权限申请和异常情况

---

## 全局样式和图标系统

### 全局样式系统 ✅ **已完成**
**实现文件**: `ui/全局样式/公共样式.js`

#### 样式组件
- ✅ **颜色主题**: 统一的绿色主题色彩系统
- ✅ **尺寸规范**: 标准化的间距、字体、控件尺寸
- ✅ **输入框样式**: 标准和聚焦状态样式
- ✅ **开关样式**: 配置页和标准开关样式
- ✅ **导航栏样式**: 主导航栏和次导航栏样式
- ✅ **列表样式**: 标准和激活列表项样式
- ✅ **对话框样式**: 统一的对话框外观
- ✅ **状态栏样式**: 成功绿和白色状态栏

### 图标管理系统 ✅ **已完成**
**实现文件**: `ui/全局样式/通用图标管理器.js`

#### 图标功能
- ✅ **FontAwesome图标库**: 完整的图标字符映射
- ✅ **字体管理器**: FontAwesome、Roboto、RobotoBold字体加载
- ✅ **通用图标管理器**: 统一的图标应用接口
- ✅ **批量应用**: 导航栏、操作按钮、抽屉页面图标批量配置
- ✅ **悬浮球图标**: 支持floaty.window控件的图标应用
- ✅ **语义化API**: 基于功能分类的图标应用方法

#### 支持的图标分类
- **导航类**: 主页、菜单、设置等
- **操作类**: 播放、停止、刷新、删除、保存等
- **媒体类**: 相机、播放、停止等
- **安全类**: 盾牌、生化危险等
- **文件类**: 文档、文件夹等
- **通信类**: 铃铛、通知等
- **设备类**: 手机、屏幕等

---

## 项目文件结构

### UI文件组织 ✅ **已完成**
```
ui/
├── 主页/
│   ├── UI主页页面.js          # 主页界面布局
│   ├── 主页逻辑.js            # 主页业务逻辑
│   └── 悬浮球/
│       ├── 悬浮球管理系统.js   # 悬浮球管理
│       └── 悬浮球.js          # 悬浮球实现
├── 脚本配置页/
│   ├── UI脚本页面.js          # 配置页界面布局
│   └── 脚本逻辑.js            # 配置页业务逻辑
├── 日志页/
│   ├── UI日志页面.js          # 日志页界面布局
│   └── 日志逻辑.js            # 日志页业务逻辑
├── 菜单抽屉页/
│   └── 侧滑抽屉.js            # 抽屉界面和权限管理
├── 全局样式/
│   ├── 公共样式.js            # 全局样式定义
│   ├── 通用图标管理器.js       # 图标管理系统
│   ├── FontAwesome图标库.js   # 图标字符映射
│   ├── 字体管理器.js          # 字体加载管理
│   └── 图标颜色配置说明.md     # 图标配置文档
└── 通用组件/                  # 通用UI组件（预留）
```

### 开发规范总结
- ✅ **模块化设计**: 每个页面独立的UI和逻辑文件
- ✅ **统一样式**: 全局样式系统确保视觉一致性
- ✅ **图标标准化**: 通用图标管理器统一图标应用
- ✅ **权限管理**: 完整的权限检查、申请、管理流程
- ✅ **错误处理**: 完善的异常处理和用户反馈
- ✅ **性能优化**: 资源管理、内存清理、异步操作
- ✅ **用户体验**: 气泡提示、状态反馈、交互优化