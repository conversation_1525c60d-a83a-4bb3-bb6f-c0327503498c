/**
 * 帐号登陆模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

// 导入截图模块
var 截图模块 = require('./截图.js');
var 文件模块 = require('./文件.js');

function 登陆_帐号() {
    try {
        console.log("🎮 开始帐号登陆流程");

        // 延时22秒
        console.log("⏳ 延时22秒后开始执行登陆流程...");
        sleep(22000);

        // 第一步：判断并点击跳过按钮
        console.log("📋 步骤1: 判断跳过按钮");
        var 跳过成功 = 截图模块.查找_图片(
            "../../assets/通用模板/查找跳过.png",
            459, 44, 43, 22,  // 区域范围
            2, 200,           // 8秒内每200毫秒查找一次
            "判断点击",        // 判断点击模式：找到则点击，找不到则跳过
            2,               // 重试次数
            0.8              // 相似度
        );

        if (!跳过成功) {
            console.log("⚠️ 未找到跳过按钮，说明已经注册过了，直接跳过整个登陆流程");
            return true;  // 直接返回成功，跳过后续步骤
        } else {
            console.log("✅ 跳过按钮点击成功，继续执行注册流程");
        }

        // 第二步：随机等待0.5-2秒
        var 随机等待 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待 + " 毫秒后执行下一步");
        sleep(随机等待);

        // 第三步：查找并点击报名按钮
        console.log("📋 步骤2: 查找报名按钮");
        var 报名成功 = 截图模块.查找_图片(
            "../../assets/通用模板/报名.png",
            66, 456, 413, 70,  // 区域范围 (66,456,479,526)
            8, 200,           // 8秒内每200毫秒查找一次
            "判断点击",        // 判断点击模式：找到则点击
            3,               // 重试次数
            0.8              // 相似度
        );

        if (!报名成功) {
            console.log("⚠️ 未找到报名按钮");
        } else {
            console.log("✅ 报名按钮点击成功");
        }

        // 第四步：随机等待0.5-2秒
        var 随机等待2 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待2 + " 毫秒后执行输入帐密");
        sleep(随机等待2);

        // 第五步：输入邮箱
        console.log("📋 步骤3: 输入邮箱");
        var 邮箱成功 = 截图模块.查找_图片(
            "../../assets/通用模板/邮箱输入框.png",
            55, 454, 423, 84,  // 区域范围 (55,454,478,538)
            5, 200,           // 5秒内每200毫秒查找一次
            "点击输入",         // 点击输入模式：找到则点击，专门用于输入框
            3,               // 重试次数
            0.8              // 相似度
        );

        if (邮箱成功) {
            // 读取邮箱配置并输入
            var 邮箱内容 = 文件模块.读取_配置(0, 1); 
            if (邮箱内容) {
                sleep(random(500, 1000));
                setText(邮箱内容);
                console.log("✅ 邮箱输入完成: " + 邮箱内容);
            } else {
                console.log("⚠️ 邮箱配置读取失败");
            }
        } else {
            console.log("⚠️ 未找到邮箱输入框");
        }

        // 第六步：随机等待0.5-2秒
        var 随机等待3 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待3 + " 毫秒后输入密码");
        sleep(随机等待3);

        // 第七步：输入密码
        console.log("📋 步骤4: 输入密码");
        var 密码成功 = 截图模块.查找_图片(
            "../../assets/通用模板/密码输入框.png",
            75, 538, 389, 56,  // 区域范围 (75,538,464,594)
            15, 200,           // 15秒内每200毫秒查找一次
            "点击输入",         // 点击输入模式：找到则点击，专门用于输入框
            3,               // 重试次数
            0.8              // 相似度
        );

        if (密码成功) {
            // 读取密码配置并输入
            var 密码内容 = 文件模块.读取_配置(0, 2); 
            if (密码内容) {
                sleep(random(500, 1000));
                setText(密码内容);
                console.log("✅ 密码输入完成");
            } else {
                console.log("⚠️ 密码配置读取失败");
            }
        } else {
            console.log("⚠️ 未找到密码输入框");
        }

        // 第八步：随机等待0.5-2秒
        var 随机等待4 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待4 + " 毫秒后点击登陆");
        sleep(随机等待4);

        // 第九步：点击登陆按钮
        console.log("📋 步骤5: 点击登陆按钮");
        var 登陆成功 = 截图模块.查找_图片(
            "../../assets/通用模板/登陆.png",
            149, 662, 237, 92,  // 区域范围 (149,662,386,754)
            10, 200,            // 10秒内每200毫秒查找一次
            "判断点击",          // 判断点击模式：找到则点击
            3,               // 重试次数
            0.8              // 相似度
        );

        if (登陆成功) {
            console.log("✅ 登陆按钮点击成功");
        } else {
            console.log("⚠️ 未找到登陆按钮");
        }

        // 第十步：随机等待0.5-2秒
        var 随机等待5 = random(500, 2000);
        console.log("⏳ 随机等待 " + 随机等待5 + " 毫秒后查找确认按钮");
        sleep(随机等待5);

        // 第十一步：点击确认按钮
        console.log("📋 步骤6: 点击确认按钮");
        var 确认成功 = 截图模块.查找_图片(
            "../../assets/通用模板/confirm.png",
            143, 512, 259, 108,  // 区域范围 (143,512,402,620)
            15, 200,             // 15秒内每200毫秒查找一次
            "判断点击",           // 判断点击模式：找到则点击
            3,               // 重试次数
            0.8              // 相似度
        );

        if (确认成功) {
            console.log("✅ 确认按钮点击成功");
        } else {
            console.log("⚠️ 未找到确认按钮");
        }

        // 返回整体结果
        var 整体成功 = 跳过成功 || 报名成功 || 邮箱成功 || 密码成功 || 登陆成功 || 确认成功;
        console.log("🎯 帐号登陆流程完成，结果: " + (整体成功 ? "✅ 成功" : "⚠️ 未找到目标按钮"));

        return 整体成功;

    } catch (error) {
        console.error("❌ 登陆帐号时发生错误: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    登陆_帐号: 登陆_帐号
};

// 直接运行测试（单独运行时取消注释下面3行）
// console.log("🧪 开始测试帐号登陆功能");
// var 结果 = 登陆_帐号();
// console.log("🎯 测试结果: " + (结果 ? "✅ 成功" : "❌ 失败"));