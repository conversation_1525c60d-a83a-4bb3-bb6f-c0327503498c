/**
 * AE脚本 - 坐标范围随机按下事件
 * 基于AutoXjs ozobiozobi v6.5.8.17开发
 */



/**
 * 在指定坐标范围内执行随机位置按下事件
 * @param {number} x1 - 左上角X坐标
 * @param {number} y1 - 左上角Y坐标
 * @param {number} x2 - 右下角X坐标
 * @param {number} y2 - 右下角Y坐标
 * @param {number} duration - 按下持续时间（毫秒），默认100ms
 */
function 左键(x1, y1, x2, y2, duration) {
    try {
        // 参数验证
        if (typeof x1 !== 'number' || typeof y1 !== 'number' ||
            typeof x2 !== 'number' || typeof y2 !== 'number') {
            throw new Error("坐标参数必须是数字");
        }

        if (x1 >= x2 || y1 >= y2) {
            throw new Error("坐标范围无效：左上角坐标必须小于右下角坐标");
        }

        // 设置默认持续时间
        duration = duration || 100;

        // 生成随机坐标（在范围内）
        var randomX = Math.floor(Math.random() * (x2 - x1 + 1)) + x1;
        var randomY = Math.floor(Math.random() * (y2 - y1 + 1)) + y1;

        console.log("执行左键按下事件:");
        console.log("  范围: (" + x1 + "," + y1 + ") 到 (" + x2 + "," + y2 + ")");
        console.log("  随机点: (" + randomX + "," + randomY + ")");
        console.log("  持续时间: " + duration + "ms");

        // 检查无障碍服务权限
        if (!auto.service) {
            console.error("无障碍服务未开启，无法执行点击操作");
            toast("请先开启无障碍服务");
            return false;
        }

        // 执行按下事件（在随机点）
        var result = press(randomX, randomY, duration);

        if (result) {
            console.log("✅ 按下左键事件执行成功");
            toast(" 按下左键事件已执行");
            return true;
        } else {
            console.error("❌  按下左键事件执行失败");
            toast( "按下左键事件执行失败");
            return false;
        }

    } catch (e) {
        console.error("坐标范围按下左键事件执行失败:", e);
        toast("执行失败: " + e.message);
        return false;
    }
}

// 执行指定坐标范围438,175,476,206的随机按下事件，持续10秒
左键(4,669,112,772, 100);


/**
 * AE脚本 - 坐标范围随机按下事件
 * 基于AutoXjs ozobiozobi v6.5.8.17开发
 */



/**
 * 在指定坐标范围内执行随机位置按下事件
 * @param {number} x1 - 左上角X坐标
 * @param {number} y1 - 左上角Y坐标
 * @param {number} x2 - 右下角X坐标
 * @param {number} y2 - 右下角Y坐标
 * @param {number} duration - 按下持续时间（毫秒），默认100ms
 */
function 右键(x1, y1, x2, y2, duration) {
    try {
        // 参数验证
        if (typeof x1 !== 'number' || typeof y1 !== 'number' ||
            typeof x2 !== 'number' || typeof y2 !== 'number') {
            throw new Error("坐标参数必须是数字");
        }

        if (x1 >= x2 || y1 >= y2) {
            throw new Error("坐标范围无效：左上角坐标必须小于右下角坐标");
        }

        // 设置默认持续时间
        duration = duration || 100;

        // 生成随机坐标（在范围内）
        var randomX = Math.floor(Math.random() * (x2 - x1 + 1)) + x1;
        var randomY = Math.floor(Math.random() * (y2 - y1 + 1)) + y1;

        console.log("执行左键按下事件:");
        console.log("  范围: (" + x1 + "," + y1 + ") 到 (" + x2 + "," + y2 + ")");
        console.log("  随机点: (" + randomX + "," + randomY + ")");
        console.log("  持续时间: " + duration + "ms");

        // 检查无障碍服务权限
        if (!auto.service) {
            console.error("无障碍服务未开启，无法执行点击操作");
            toast("请先开启无障碍服务");
            return false;
        }

        // 执行按下事件（在随机点）
        var result = press(randomX, randomY, duration);

        if (result) {
            console.log("✅ 按下左键事件执行成功");
            toast(" 按下左键事件已执行");
            return true;
        } else {
            console.error("❌  按下左键事件执行失败");
            toast( "按下左键事件执行失败");
            return false;
        }

    } catch (e) {
        console.error("坐标范围按下左键事件执行失败:", e);
        toast("执行失败: " + e.message);
        return false;
    }
}

// 执行指定坐标范围438,175,476,206的随机按下事件，持续10秒
右键(429,701,535,807, 100);


