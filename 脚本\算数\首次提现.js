/**
 * 算数游戏首次提现教程模块
 * 基于AutoXjs ozobiozobi v6.5.8.17官方API
 */

// 导入截图功能模块
var 截图模块 = require('../常用功能/截图.js');

function 首次提现教程() {
    try {
        console.log("💰 首次提现教程流程");

        // 检查截图权限
        console.log("🔐 检查截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 截图权限申请失败");
            return false;
        }
        console.log("✅ 截图权限申请成功");

        // 等待权限生效
        sleep(1000);

        // 第一步：在屏幕范围281,24,243,247中判断点击点击分数.png
        console.log("📝 第一步：点击分数按钮");

        var 分数图片 = "../../assets/算数游戏/新手教程图片/点击分数2.png";
        console.log("🔍 图片路径: " + 分数图片);

        var 分数结果 = 截图模块.查找_图片(分数图片, 281, 24, 243, 247, 5, 1000, "判断点击", 1, 0.8);

        if (分数结果) {
            console.log("✅ 第一步完成：成功点击分数按钮");
        } else {
            console.log("⚠️ 第一步未找到分数按钮或点击失败，继续执行第二步");
        }

        // 第二步：无论第一步是否成功，都执行第二步
        console.log("📝 第二步：点击转换分数按钮");
        sleep(1000);

        var 转换分数图片 = "../../assets/算数游戏/新手教程图片/转换分数.png";
        console.log("🔍 图片路径: " + 转换分数图片);

        var 转换分数结果 = 截图模块.查找_图片(转换分数图片, 119, 776, 292, 78, 5, 1000, "判断点击", 1, 0.8);

        if (转换分数结果) {
            console.log("✅ 第二步完成：成功点击转换分数按钮");
        } else {
            console.log("⚠️ 第二步未找到转换分数按钮或点击失败");
        }

        // 第三步：延时10秒后点击退出分数取消按钮
        console.log("📝 第三步：延时10秒后点击退出分数取消按钮");
        console.log("⏳ 延时10秒...");
        sleep(10000);

        var 退出分数取消按钮图片 = "../../assets/算数游戏/新手教程图片/退出分数取消按钮.png";
        console.log("🔍 图片路径: " + 退出分数取消按钮图片);

        var 退出分数取消按钮结果 = 截图模块.查找_图片(退出分数取消按钮图片, 398, 146, 135, 52, 5, 1000, "判断点击", 1, 0.8);

        if (退出分数取消按钮结果) {
            console.log("✅ 第三步完成：成功点击退出分数取消按钮");
        } else {
            console.log("⚠️ 第三步未找到退出分数取消按钮或点击失败");
        }
        // 第四步：延时2秒后点击提现箭头2按钮
        console.log("📝 第四步：延时2秒后点击提现箭头2按钮");
        console.log("⏳ 延时2秒...");
        sleep(2000);

        var 提现箭头2图片 = "../../assets/算数游戏/新手教程图片/提现箭头2.png";
        console.log("🔍 图片路径: " + 提现箭头2图片);

        var 提现箭头2结果 = 截图模块.查找_图片(提现箭头2图片, 1, 150, 516, 723, 3, 1000, "判断点击", 1, 0.8);

        if (提现箭头2结果) {
            console.log("✅ 第四步完成：成功点击提现箭头2按钮");
        } else {
            console.log("⚠️ 第四步未找到提现箭头2按钮或点击失败");
        }
        // 第五步：延时3秒后点击下载钱包按钮
        console.log("📝 第五步：延时3秒后点击下载钱包按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 下载钱包按钮图片 = "../../assets/算数游戏/新手教程图片/下载钱包按钮.png";
        console.log("🔍 图片路径: " + 下载钱包按钮图片);

        var 下载钱包按钮结果 = 截图模块.查找_图片(下载钱包按钮图片, 84, 721, 370, 192, 3, 500, "判断点击", 2, 0.7);

        if (下载钱包按钮结果) {
            console.log("✅ 第五步完成：成功点击下载钱包按钮");
        } else {
            console.log("⚠️ 第五步未找到下载钱包按钮或点击失败");
        }
        // 第六步：延时3秒后检查应用前台并切换回游戏
        console.log("📝 第六步：延时3秒后检查应用前台");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        // 检查当前前台应用
        var 当前应用包名 = currentPackage();
        console.log("🔍 当前前台应用: " + 当前应用包名);

        var 目标应用包名 = "com.winrgames.brainbattle";

        if (当前应用包名 !== 目标应用包名) {
            console.log("⚠️ 当前不在目标应用前台，正在切换到: " + 目标应用包名);

            // 切换到目标应用
            var 切换结果 = app.launch(目标应用包名);

            if (切换结果) {
                console.log("✅ 第六步完成：成功切换到游戏应用前台");
                // 等待应用启动
                sleep(2000);
            } else {
                console.log("❌ 第六步失败：无法切换到游戏应用");
            }
        } else {
            console.log("✅ 第六步完成：已在游戏应用前台，无需切换");
        }
        // 第七步：延时3秒后点击填钱包地址箭头3按钮
        console.log("📝 第七步：延时3秒后点击填钱包地址箭头3按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 填钱包地址箭头3图片 = "../../assets/算数游戏/新手教程图片/填钱包地址箭头3.png";
        console.log("🔍 图片路径: " + 填钱包地址箭头3图片);

        var 填钱包地址箭头3结果 = 截图模块.查找_图片(填钱包地址箭头3图片, 12, 267, 504, 610, 4, 500, "判断点击", 1, 0.8);

        if (填钱包地址箭头3结果) {
            console.log("✅ 第七步完成：成功点击填钱包地址箭头3按钮");
        } else {
            console.log("⚠️ 第七步未找到填钱包地址箭头3按钮或点击失败");
        }
        // 第八步：延时3秒后点击钱包地址按钮
        console.log("📝 第八步：延时3秒后点击钱包地址按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 钱包地址图片 = "../../assets/算数游戏/新手教程图片/点击钱包地址.png";
        console.log("🔍 图片路径: " + 钱包地址图片);

        var 钱包地址结果 = 截图模块.查找_图片(钱包地址图片, 3, 512, 515, 378, 4, 500, "判断点击", 1, 0.8);

        if (钱包地址结果) {
            console.log("✅ 第八步完成：成功点击钱包地址按钮");
        } else {
            console.log("⚠️ 第八步未找到钱包地址按钮或点击失败");
        }
        // 第九步：延时3秒后点击数量按钮
        console.log("📝 第九步：延时3秒后点击数量按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 数量按钮图片 = "../../assets/算数游戏/新手教程图片/点击数量按钮.png";
        console.log("🔍 图片路径: " + 数量按钮图片);

        var 数量按钮结果 = 截图模块.查找_图片(数量按钮图片, 3, 512, 515, 378, 4, 500, "判断点击", 1, 0.8);

        if (数量按钮结果) {
            console.log("✅ 第九步完成：成功点击数量按钮");
        } else {
            console.log("⚠️ 第九步未找到数量按钮或点击失败");
        }
        // 第十步：延时3秒后点击提现确认按钮
        console.log("📝 第十步：延时3秒后点击提现确认按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 提现确认按钮图片 = "../../assets/算数游戏/新手教程图片/提现确认按钮.png";
        console.log("🔍 图片路径: " + 提现确认按钮图片);

        var 提现确认按钮结果 = 截图模块.查找_图片(提现确认按钮图片, 113, 758, 330, 160, 4, 500, "判断点击", 1, 0.8);

        if (提现确认按钮结果) {
            console.log("✅ 第十步完成：成功点击提现确认按钮");
        } else {
            console.log("⚠️ 第十步未找到提现确认按钮或点击失败");
        }
        // 第十一步：延时3秒后点击结束提现教程箭头3按钮
        console.log("📝 第十一步：延时3秒后点击结束提现教程箭头3按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 结束提现教程箭头3图片 = "../../assets/算数游戏/新手教程图片/结束提现教程箭头3.png";
        console.log("🔍 图片路径: " + 结束提现教程箭头3图片);

        var 结束提现教程箭头3结果 = 截图模块.查找_图片(结束提现教程箭头3图片, 9, 229, 515, 680, 4, 500, "判断点击", 1, 0.8);

        if (结束提现教程箭头3结果) {
            console.log("✅ 第十一步完成：成功点击结束提现教程箭头3按钮");
        } else {
            console.log("⚠️ 第十一步未找到结束提现教程箭头3按钮或点击失败");
        }


        //最后一步：延时3秒后点击提现退出按钮
        console.log("📝 最后一步：延时3秒后点击提现退出按钮");
        console.log("⏳ 延时3秒...");
        sleep(3000);

        var 提现退出按钮图片 = "../../assets/算数游戏/新手教程图片/提现退出按钮.png";
        console.log("🔍 图片路径: " + 提现退出按钮图片);

        var 提现退出按钮结果 = 截图模块.查找_图片(提现退出按钮图片, 7, 6, 107, 75, 5, 1000, "判断点击", 1, 0.8);

        if (提现退出按钮结果) {
            console.log("✅ 最后一步完成：成功点击提现退出按钮");
        } else {
            console.log("⚠️ 最步一步未找到提现退出按钮或点击失败");
        }

        console.log("🎉 首次提现教程流程执行完毕");
        return true;

    } catch (error) {
        // 注意：这里的错误处理是正常的容错机制，不是程序bug
        // 作用：防止图片文件缺失、权限问题等导致整个脚本崩溃
        // 效果：首次提现教程失败时程序会继续执行后续功能，而不是停止运行
        console.error("❌ 正常的容错机制，不是程序bug: " + error);
        return false;
    }
}

// 导出模块
module.exports = {
    首次提现教程: 首次提现教程
};