# AutoXjs 卡片圆角设置成功原因说明

## 问题背景
在AutoXjs项目中，最初卡片设置了 `cardCornerRadius="16dp"` 属性，但卡片仍然显示为直角，圆角效果不生效。

## 解决方案
通过修改卡片的背景属性设置，成功实现了圆角效果。

## 关键修改

### ❌ 错误的设置方式
```xml
<card
    id="游戏数据卡片"
    background={全局样式.颜色主题.背景次色}
    cardCornerRadius="16dp"
    cardElevation="2dp">
```

### ✅ 正确的设置方式
```xml
<card
    id="游戏数据卡片"
    cardBackgroundColor={全局样式.颜色主题.背景次色}
    cardCornerRadius="16dp"
    cardElevation="2dp">
```

## 核心原因分析

### 1. **属性名称差异**
- **错误属性**: `background` - 这是通用View的背景属性
- **正确属性**: `cardBackgroundColor` - 这是Card控件专用的背景属性

### 2. **AutoXjs Card控件特性**
根据AutoXjs官方文档，Card控件有专门的属性：
- `cardBackgroundColor`: 卡片的背景颜色
- `cardCornerRadius`: 卡片的圆角半径
- `cardElevation`: 卡片的阴影高度

### 3. **属性优先级**
当同时设置 `background` 和 `cardBackgroundColor` 时：
- `background` 属性会覆盖Card控件的内部圆角处理
- `cardBackgroundColor` 属性会正确应用到Card的圆角背景上

### 4. **渲染机制**
- 使用 `background` 时，AutoXjs将其作为普通View背景处理，忽略圆角
- 使用 `cardBackgroundColor` 时，AutoXjs会将背景与圆角一起渲染

## 官方文档支持
根据AutoXjs官方文档：
```
cardBackgroundColor: 卡片的背景颜色。比如 cardBackgroundColor="#ffffff"。
cardCornerRadius: 卡片的圆角半径。
cardElevation: 设置卡片在z轴上的高度，来控制阴影的大小。
```

## 最佳实践

### Card控件圆角设置标准格式：
```xml
<card
    cardBackgroundColor="#FFFFFF"
    cardCornerRadius="16dp"
    cardElevation="2dp">
    <!-- 卡片内容 -->
</card>
```

### 常用圆角值推荐：
- **小圆角**: 8dp - 适用于小型卡片或输入框
- **中圆角**: 12dp - 适用于按钮或中型卡片
- **大圆角**: 16dp - 适用于主要内容卡片
- **超大圆角**: 20dp+ - 适用于特殊设计需求

## Button控件圆角设置

### 问题背景
在AutoXjs项目中，尝试使用 `radius` 属性或 `shape` 属性为Button设置圆角时，按钮仍然显示为直角，圆角效果不生效。

### 解决方案
通过使用AutoXjs官方推荐的Material Design按钮样式，成功实现了圆角效果。

### 关键修改

#### ❌ 错误的设置方式
```xml
<button
    text="按钮"
    background="#4CAF50"
    radius="12dp"
    shape="200|48,12,12,12,12">
```

#### ✅ 正确的设置方式
```xml
<button
    text="按钮"
    style="Widget.AppCompat.Button.Colored"
    backgroundTint="#4CAF50"
    textColor="#FFFFFF">
```

### 核心原因分析

#### 1. **API兼容性问题**
- **错误方法**: `radius` 和 `shape` 属性在Button控件中不生效
- **正确方法**: 使用官方Material Design样式自带圆角

#### 2. **AutoXjs Button控件特性**
根据AutoXjs官方文档，Button控件支持的样式：
- `Widget.AppCompat.Button.Colored` - 带颜色的按钮（自带圆角）
- `Widget.AppCompat.Button.Borderless` - 无边框按钮
- `Widget.AppCompat.Button.Borderless.Colored` - 带颜色的无边框按钮

#### 3. **背景色设置差异**
- 使用 `style` 时，背景色应该用 `backgroundTint` 而不是 `background`
- `backgroundTint` 会正确应用到Material Design样式上

### 官方文档支持
根据AutoXjs官方文档：
> 按钮控件有一些内置的样式，通过`style`属性设置，包括：
> - Widget.AppCompat.Button.Colored 带颜色的按钮

### 最佳实践

#### Button控件圆角设置标准格式：
```xml
<button
    text="按钮文字"
    style="Widget.AppCompat.Button.Colored"
    backgroundTint="#4CAF50"
    textColor="#FFFFFF"
    textSize="16sp"
    h="48dp"/>
```

#### 可选的按钮样式：
- **带颜色圆角按钮**: `Widget.AppCompat.Button.Colored`
- **无边框按钮**: `Widget.AppCompat.Button.Borderless`
- **带颜色无边框按钮**: `Widget.AppCompat.Button.Borderless.Colored`

## 总结
- **卡片圆角**：使用 `cardBackgroundColor` + `cardCornerRadius` 属性
- **按钮圆角**：使用官方Material Design样式 `style="Widget.AppCompat.Button.Colored"` + `backgroundTint`

AutoXjs中不同控件有不同的圆角实现方式，关键是使用每个控件专门支持的API，而不是通用的属性。这确保了AutoXjs能够正确处理圆角渲染效果。
