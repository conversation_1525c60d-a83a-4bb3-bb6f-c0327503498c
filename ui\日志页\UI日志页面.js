/**
 * Magic 游戏辅助脚本 - 日志页面界面
 * 基于AutoXjs原生UI组件开发
 * 使用统一的#00A843绿色主题
 */

// 导入全局样式
var 全局样式 = require('../全局样式/公共样式.js');

// 日志页面XML布局
var 日志页面布局 = (
    <vertical>
        {/* 顶部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h={全局样式.尺寸规范.工具栏高度}
            gravity="center_vertical"
            elevation="4dp">

            <button
                id="菜单按钮"
                background="#00000000"
                textColor={全局样式.颜色主题.白色}
                textSize="22sp"
                w="56dp"
                h="56dp"
                gravity="center"/>

            <text
                id="页面标题"
                text="日志"
                textColor={全局样式.颜色主题.白色}
                textSize={全局样式.尺寸规范.字体超大}
                layout_weight="1"
                gravity="center"
                textStyle="bold"/>

            <button
                id="清空日志"
                background="#00000000"
                textColor={全局样式.颜色主题.白色}
                textSize="18sp"
                w="56dp"
                h="56dp"
                text="🗑️"
                gravity="center"/>
        </horizontal>

        {/* 主要内容区域 */}
        <ScrollView
            layout_weight="1"
            background={全局样式.颜色主题.背景主色}>

            <vertical padding={全局样式.尺寸规范.间距中}>
                {/* 日志卡片 */}
                <card
                    id="日志卡片"
                    cardBackgroundColor={全局样式.颜色主题.背景次色}
                    cardCornerRadius="16dp"
                    cardElevation="2dp">

                    <vertical>
                        {/* 卡片标题栏 */}
                        <horizontal
                            padding={全局样式.尺寸规范.间距中}
                            gravity="center_vertical">

                            <text
                                text="运行日志"
                                textSize={全局样式.尺寸规范.字体大}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"
                                fontWeight="bold"/>

                            <text
                                id="日志计数"
                                text="0 条记录"
                                textSize={全局样式.尺寸规范.字体小}
                                textColor={全局样式.颜色主题.文字次色}/>

                            <button
                                id="刷新异步日志"
                                background="#00000000"
                                textColor={全局样式.颜色主题.文字次色}
                                textSize="16sp"
                                w="40dp"
                                h="40dp"
                                text="🔄"
                                gravity="center"
                                margin="4dp 0 0 8dp"/>
                        </horizontal>

                        {/* 日志内容区域 */}
                        <vertical
                            id="日志内容区域"
                            padding={全局样式.尺寸规范.间距中}>

                            {/* 空状态显示 */}
                            <vertical
                                id="空状态区域"
                                padding="40dp"
                                gravity="center">

                                <text
                                    id="空状态图标"
                                    textSize="48sp"
                                    alpha="0.6"
                                    textColor={全局样式.颜色主题.文字次色}
                                    gravity="center"/>

                                <text
                                    text="暂无日志记录"
                                    textSize={全局样式.尺寸规范.字体大}
                                    textColor={全局样式.颜色主题.文字主色}
                                    margin="8dp"
                                    gravity="center"
                                    fontWeight="bold"/>

                                <text
                                    text="脚本运行后将显示日志信息"
                                    textSize={全局样式.尺寸规范.字体中}
                                    textColor={全局样式.颜色主题.文字次色}
                                    gravity="center"/>
                            </vertical>

                            {/* 日志列表容器 */}
                            <vertical
                                id="日志列表容器"
                                visibility="gone">

                                <ScrollView
                                    id="日志滚动区域"
                                    h="300dp">

                                    <vertical
                                        id="日志列表"
                                        padding="8dp">
                                        {/* 日志条目将动态添加到这里 */}
                                    </vertical>
                                </ScrollView>
                            </vertical>
                        </vertical>
                    </vertical>
                </card>
            </vertical>
        </ScrollView>

        {/* 底部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h="50dp"
            elevation="8dp">

            <vertical
                id="脚本标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="脚本图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>

            <vertical
                id="主页标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="主页图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>

            <vertical
                id="日志标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="日志图标"
                    textSize="20sp"
                    textColor={全局样式.颜色主题.白色}
                    gravity="center"/>
            </vertical>
        </horizontal>
    </vertical>
);

// 导出日志页面布局和相关功能
module.exports = {
    // 布局定义
    布局: 日志页面布局,

    // 添加日志条目
    添加日志: function(日志内容, 日志类型) {
        try {
            // 隐藏空状态，显示日志列表
            if (ui.空状态区域) {
                ui.空状态区域.attr("visibility", "gone");
            }
            if (ui.日志列表容器) {
                ui.日志列表容器.attr("visibility", "visible");
            }

            // 创建日志条目
            var 时间戳 = new Date().toLocaleTimeString();
            var 日志颜色 = 全局样式.颜色主题.文字主色;

            // 根据日志类型设置颜色，图标由FontAwesome管理器处理
            switch (日志类型) {
                case "成功":
                    日志颜色 = 全局样式.颜色主题.成功色;
                    break;
                case "警告":
                    日志颜色 = 全局样式.颜色主题.警告色;
                    break;
                case "错误":
                    日志颜色 = 全局样式.颜色主题.错误色;
                    break;
                default:
                    日志颜色 = 全局样式.颜色主题.信息色;
            }

            // 检查UI是否可用
            if (!ui || !ui.日志列表) {
                // 静默跳过，不输出调试信息
                return;
            }

            // 安全地创建日志条目布局
            var 日志条目;
            try {
                日志条目 = ui.inflate(
                    <horizontal padding="8dp 4dp" gravity="center_vertical">
                        <text
                            id={"日志图标_" + Date.now()}
                            textSize="14sp"
                            textColor={日志颜色}
                            w="24dp"
                            gravity="center"/>
                        <vertical layout_weight="1" margin="8dp 0 0 0">
                            <text
                                text={日志内容}
                                textSize="14sp"
                                textColor={全局样式.颜色主题.文字主色}/>
                            <text
                                text={时间戳}
                                textSize="12sp"
                                textColor={全局样式.颜色主题.文字次色}
                                margin="0 2dp 0 0"/>
                        </vertical>
                    </horizontal>
                );
            } catch (inflateError) {
                console.error("创建日志条目失败:", inflateError);
                return;
            }

            // 添加到日志列表
            if (ui.日志列表 && 日志条目) {
                ui.日志列表.addView(日志条目);
            }

            // 更新日志计数
            更新日志计数();

            // 安全地滚动到底部
            try {
                ui.post(() => {
                    if (ui.日志滚动区域) {
                        ui.日志滚动区域.fullScroll(android.widget.ScrollView.FOCUS_DOWN);
                    }
                });
            } catch (scrollError) {
                // 静默处理滚动错误
            }

        } catch (e) {
            console.error("添加日志失败:", e);
        }
    },

    // 清空所有日志
    清空日志: function() {
        try {
            if (ui.日志列表) {
                ui.日志列表.removeAllViews();
            }
            if (ui.空状态区域) {
                ui.空状态区域.attr("visibility", "visible");
            }
            if (ui.日志列表容器) {
                ui.日志列表容器.attr("visibility", "gone");
            }
            更新日志计数();
        } catch (e) {
            console.error("清空日志失败:", e);
        }
    },

    // 显示气泡提示
    显示提示: function(消息, 类型) {
        try {
            if (类型 === "成功") {
                toast("成功: " + 消息);
            } else if (类型 === "错误") {
                toast("错误: " + 消息);
            } else if (类型 === "警告") {
                toast("警告: " + 消息);
            } else {
                toast(消息);
            }
        } catch (e) {
            console.error("显示提示失败:", e);
        }
    }
};

// 更新日志计数的辅助函数
function 更新日志计数() {
    try {
        if (ui.日志列表 && ui.日志计数) {
            var 日志数量 = ui.日志列表.getChildCount();
            ui.日志计数.attr("text", 日志数量 + " 条记录");
        }
    } catch (e) {
        console.error("更新日志计数失败:", e);
    }
}