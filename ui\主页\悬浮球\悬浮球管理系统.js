/**
 * Magic悬浮球系统 - 统一管理模块
 * 提供悬浮球系统的统一初始化、控制和状态管理
 * 集成所有悬浮球相关模块，提供简化的API接口
 */

// 悬浮球管理系统状态
var 悬浮球管理系统 = {
    // 模块实例缓存
    _模块缓存: {
        XML悬浮球: null,
        悬浮球事件: null,
        悬浮球菜单: null,
        通用图标管理器: null
    },

    // 系统状态
    _系统状态: {
        已初始化: false,
        悬浮球运行中: false,
        菜单展开状态: false,
        错误状态: null
    },

    // 配置信息
    _系统配置: {
        主球位置: { x: -20, y: null },  // y值动态计算
        透明度: 0.6,
        图标配置: {
            主球: { 分类: "安全", 名称: "生化危险", 颜色: "#FFFFFF", 大小: "20sp" },
            开始: { 分类: "媒体", 名称: "播放", 颜色: "#333333", 大小: "16sp" },
            停止: { 分类: "媒体", 名称: "停止", 颜色: "#333333", 大小: "16sp" },
            主页: { 分类: "导航", 名称: "主页", 颜色: "#333333", 大小: "16sp" },
            日志: { 分类: "文件", 名称: "文档", 颜色: "#333333", 大小: "16sp" }
        }
    }
};

/**
 * 按需加载模块，避免启动时性能影响
 * @param {String} 模块名 
 * @returns {Object} 模块实例
 */
function 加载模块(模块名) {
    try {
        if (!悬浮球管理系统._模块缓存[模块名]) {
            var 模块路径;
            switch (模块名) {
                case 'XML悬浮球':
                    模块路径 = './XML悬浮球.js';
                    break;
                case '悬浮球事件':
                    模块路径 = './悬浮球事件.js';
                    break;
                case '悬浮球菜单':
                    模块路径 = './悬浮球菜单.js';
                    break;
                case '通用图标管理器':
                    模块路径 = '../../全局样式/通用图标管理器.js';
                    break;
                default:
                    throw new Error("未知模块: " + 模块名);
            }
            
            悬浮球管理系统._模块缓存[模块名] = require(模块路径);
            console.log("模块加载成功:", 模块名);
        }
        return 悬浮球管理系统._模块缓存[模块名];
    } catch (e) {
        console.error("模块加载失败:", 模块名, e);
        return null;
    }
}

/**
 * 检查模块依赖关系
 * @returns {boolean} 依赖检查是否通过
 */
function 检查模块依赖() {
    var 必需模块 = ['XML悬浮球', '悬浮球事件', '悬浮球菜单', '通用图标管理器'];
    var 缺失模块 = [];

    for (var i = 0; i < 必需模块.length; i++) {
        var 模块 = 加载模块(必需模块[i]);
        if (!模块) {
            缺失模块.push(必需模块[i]);
        }
    }

    if (缺失模块.length > 0) {
        throw new Error("缺失必需模块: " + 缺失模块.join(", "));
    }

    return true;
}

/**
 * 初始化悬浮球系统
 * @returns {boolean} 初始化是否成功
 */
function 初始化悬浮球系统() {
    try {
        console.log("开始初始化悬浮球系统...");

        // 1. 检查权限
        if (!floaty.checkPermission()) {
            throw new Error("缺少悬浮窗权限");
        }

        // 2. 检查模块依赖
        检查模块依赖();

        // 3. 初始化图标系统
        var 通用图标管理器 = 加载模块('通用图标管理器');
        通用图标管理器.初始化图标系统(function(成功, 消息) {
            if (!成功) {
                console.warn("图标系统初始化失败: " + 消息);
                // 不抛出错误，允许悬浮球在没有图标的情况下运行
            } else {
                console.log("图标系统初始化成功: " + 消息);
            }
        });

        // 4. 计算动态位置
        悬浮球管理系统._系统配置.主球位置.y = device.height - 300;

        // 5. 标记初始化完成
        悬浮球管理系统._系统状态.已初始化 = true;
        悬浮球管理系统._系统状态.错误状态 = null;

        console.log("悬浮球系统初始化完成");
        return true;

    } catch (e) {
        console.error("悬浮球系统初始化失败:", e);
        悬浮球管理系统._系统状态.错误状态 = e.message;
        return false;
    }
}

/**
 * 启动悬浮球
 * @returns {boolean} 启动是否成功
 */
function 启动悬浮球() {
    try {
        // 检查是否已经运行
        if (悬浮球管理系统._系统状态.悬浮球运行中) {
            console.log("悬浮球已经在运行中");
            return true;
        }

        if (!悬浮球管理系统._系统状态.已初始化) {
            if (!初始化悬浮球系统()) {
                return false;
            }
        }

        // 先停止可能存在的旧实例
        停止悬浮球();

        // 创建主悬浮球
        var XML悬浮球 = 加载模块('XML悬浮球');
        if (XML悬浮球) {
            // 确保先销毁旧实例
            if (XML悬浮球.获取状态()) {
                XML悬浮球.销毁();
            }

            if (XML悬浮球.创建()) {
                XML悬浮球.显示();

                // 绑定事件
                var 悬浮球事件 = 加载模块('悬浮球事件');
                if (悬浮球事件) {
                    var 悬浮球窗口 = XML悬浮球.获取窗口实例();
                    悬浮球事件.绑定主球事件(悬浮球窗口);
                }

                悬浮球管理系统._系统状态.悬浮球运行中 = true;
                console.log("悬浮球启动成功");
                return true;
            }
        }

        return false;
    } catch (e) {
        console.error("启动悬浮球失败:", e);
        return false;
    }
}

/**
 * 停止悬浮球
 * @returns {boolean} 停止是否成功
 */
function 停止悬浮球() {
    try {
        // 收起菜单
        var 悬浮球菜单 = 悬浮球管理系统._模块缓存.悬浮球菜单;
        if (悬浮球菜单) {
            悬浮球菜单.收起菜单();
        }

        // 隐藏主球
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        if (XML悬浮球) {
            XML悬浮球.隐藏();
            XML悬浮球.销毁();
        }

        // 解绑事件
        var 悬浮球事件 = 悬浮球管理系统._模块缓存.悬浮球事件;
        if (悬浮球事件) {
            悬浮球事件.解绑所有事件();
        }

        悬浮球管理系统._系统状态.悬浮球运行中 = false;
        console.log("悬浮球停止成功");
        return true;

    } catch (e) {
        console.error("停止悬浮球失败:", e);
        return false;
    }
}

/**
 * 销毁悬浮球系统
 * @returns {boolean} 销毁是否成功
 */
function 销毁悬浮球系统() {
    try {
        // 停止悬浮球
        停止悬浮球();

        // 清理模块缓存和事件监听器
        Object.keys(悬浮球管理系统._模块缓存).forEach(function(模块名) {
            var 模块 = 悬浮球管理系统._模块缓存[模块名];
            if (模块) {
                // 如果模块有销毁方法，调用它
                if (typeof 模块.销毁 === 'function') {
                    try {
                        模块.销毁();
                    } catch (e) {
                        console.warn("销毁模块失败:", 模块名, e);
                    }
                }
                // 如果模块有解绑事件方法，调用它
                if (typeof 模块.解绑所有事件 === 'function') {
                    try {
                        模块.解绑所有事件();
                    } catch (e) {
                        console.warn("解绑模块事件失败:", 模块名, e);
                    }
                }
            }
        });

        // 清理模块缓存
        悬浮球管理系统._模块缓存 = {
            XML悬浮球: null,
            悬浮球事件: null,
            悬浮球菜单: null,
            通用图标管理器: null
        };

        // 清理全局变量
        if (global.悬浮球点击处理器) {
            global.悬浮球点击处理器 = null;
        }
        if (global.所有悬浮球实例) {
            global.所有悬浮球实例 = [];
        }

        // 重置状态
        悬浮球管理系统._系统状态 = {
            已初始化: false,
            悬浮球运行中: false,
            菜单展开状态: false,
            错误状态: null
        };

        console.log("悬浮球系统销毁成功");
        return true;

    } catch (e) {
        console.error("销毁悬浮球系统失败:", e);
        return false;
    }
}

/**
 * 获取系统状态
 * @returns {Object} 系统状态信息
 */
function 获取系统状态() {
    return {
        已初始化: 悬浮球管理系统._系统状态.已初始化,
        悬浮球运行中: 悬浮球管理系统._系统状态.悬浮球运行中,
        菜单展开状态: 悬浮球管理系统._系统状态.菜单展开状态,
        错误状态: 悬浮球管理系统._系统状态.错误状态,
        主球位置: 悬浮球管理系统._系统配置.主球位置
    };
}

// 导出给主应用使用的接口
module.exports = {
    // 核心控制
    初始化: 初始化悬浮球系统,
    启动: 启动悬浮球,
    停止: 停止悬浮球,
    销毁: 销毁悬浮球系统,

    // 状态查询
    获取状态: 获取系统状态,

    // 功能控制
    显示: function() {
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        return XML悬浮球 ? XML悬浮球.显示() : false;
    },

    隐藏: function() {
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        return XML悬浮球 ? XML悬浮球.隐藏() : false;
    },

    // 配置管理
    设置位置: function(x, y) {
        悬浮球管理系统._系统配置.主球位置 = { x: x, y: y };
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        return XML悬浮球 ? XML悬浮球.设置位置(x, y) : false;
    },

    设置透明度: function(alpha) {
        悬浮球管理系统._系统配置.透明度 = alpha;
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        return XML悬浮球 ? XML悬浮球.设置透明度(alpha) : false;
    },

    // 拖拽状态查询

    获取拖拽状态: function() {
        var XML悬浮球 = 悬浮球管理系统._模块缓存.XML悬浮球;
        return XML悬浮球 ? XML悬浮球.获取拖拽状态() : null;
    },

    // 模块访问（高级用法）
    获取模块: function(模块名) {
        return 悬浮球管理系统._模块缓存[模块名] || null;
    }
};
