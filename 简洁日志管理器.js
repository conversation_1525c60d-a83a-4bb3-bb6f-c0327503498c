/**
 * Magic 游戏辅助脚本 - 简洁日志管理器
 * 替代复杂的全局日志桥接器，实现统一的日志输出
 * 支持控制台、UI页面、异步脚本的日志输出
 */

var 日志管理器 = {
    已初始化: false,
    UI模块: null,
    日志队列: [],
    启用文件日志: false,
    控制台已修复: false,
    原始console方法: null,

    // 方案三：文件日志配置
    异步日志文件路径: "/storage/emulated/0/magic_async_logs.json",
    文件监听定时器: null,
    上次文件修改时间: 0,
    
    /**
     * 初始化日志管理器
     */
    初始化: function() {
        if (this.已初始化) {
            console.log("日志管理器已初始化，跳过重复初始化");
            return true;
        }
        
        try {
            console.log("开始初始化简洁日志管理器...");

            // 1. 修复控制台配置错误
            this.修复控制台配置();

            // 2. 尝试加载UI日志模块
            this.UI模块 = require('./ui/日志页/日志逻辑.js');
            this.已初始化 = true;
            
            // 处理队列中的日志
            this.处理队列日志();
            
            // 设置全局接口
            if (typeof global !== 'undefined') {
                global.日志系统 = {
                    添加日志: this.输出日志.bind(this),
                    管理器: this
                };
            }
            
            console.log("✅ 简洁日志管理器初始化成功");
            return true;
            
        } catch (e) {
            console.error("❌ 日志管理器初始化失败:", e.message);
            this.已初始化 = false;
            return false;
        }
    },
    
    /**
     * 核心日志输出函数 - 三处同时输出
     */
    输出日志: function(消息, 类型, 来源) {
        类型 = 类型 || "信息";
        来源 = 来源 || "系统";
        
        var 时间戳 = new Date().toLocaleTimeString();
        var 格式化消息 = "[" + 时间戳 + "][" + 来源 + "] " + 消息;
        
        // 1. 始终输出到控制台（确保VS Code和控制台能看到）
        this.输出到控制台(格式化消息, 类型);
        
        // 2. 输出到UI日志页面（支持异步脚本）
        this.输出到UI(格式化消息, 类型);
        
        // 3. 可选：输出到文件（调试用）
        if (this.启用文件日志) {
            this.输出到文件(格式化消息, 类型);
        }

        // 4. 异步日志文件输出（方案三）
        this.写入异步日志(格式化消息, 类型, 来源);
    },
    
    /**
     * 控制台输出 - 安全版本
     */
    输出到控制台: function(消息, 类型) {
        try {
            switch (类型) {
                case "成功":
                    console.log("✅ " + 消息);
                    break;
                case "警告":
                    console.warn("⚠️ " + 消息);
                    break;
                case "错误":
                    console.error("❌ " + 消息);
                    break;
                default:
                    console.log("ℹ️ " + 消息);
            }
        } catch (e) {
            // 控制台输出失败时的降级处理
            try {
                console.log("[日志] " + 消息);
            } catch (e2) {
                // 完全无法输出到控制台，静默处理
            }
        }
    },
    
    /**
     * UI输出（支持异步脚本）
     */
    输出到UI: function(消息, 类型) {
        if (this.UI模块) {
            try {
                if (typeof ui !== 'undefined' && ui.post) {
                    // 异步脚本中使用ui.post
                    ui.post(() => {
                        try {
                            this.UI模块.添加日志(消息, 类型);
                        } catch (e) {
                            console.warn("UI日志输出失败:", e.message);
                        }
                    });
                } else {
                    // 主线程中直接调用
                    this.UI模块.添加日志(消息, 类型);
                }
            } catch (e) {
                // UI输出失败，添加到队列
                this.日志队列.push({
                    消息: 消息, 
                    类型: 类型, 
                    时间: new Date()
                });
                console.warn("UI日志输出失败，已添加到队列:", e.message);
            }
        } else {
            // UI模块未加载，添加到队列
            this.日志队列.push({
                消息: 消息, 
                类型: 类型, 
                时间: new Date()
            });
        }
    },
    
    /**
     * 文件输出（可选功能）
     */
    输出到文件: function(消息, 类型) {
        try {
            if (typeof files !== 'undefined') {
                var 日志文件路径 = "/storage/emulated/0/magic_logs.txt";
                var 日志内容 = new Date().toISOString() + " [" + 类型 + "] " + 消息 + "\n";
                files.append(日志文件路径, 日志内容);
            }
        } catch (e) {
            // 文件输出失败不影响其他输出
            console.warn("文件日志输出失败:", e.message);
        }
    },
    
    /**
     * 处理队列中的日志
     */
    处理队列日志: function() {
        if (!this.UI模块 || this.日志队列.length === 0) {
            return;
        }
        
        console.log("处理队列中的日志，数量:", this.日志队列.length);
        
        while (this.日志队列.length > 0) {
            var 日志项 = this.日志队列.shift();
            try {
                if (typeof ui !== 'undefined' && ui.post) {
                    ui.post(() => {
                        try {
                            this.UI模块.添加日志(日志项.消息, 日志项.类型);
                        } catch (e) {
                            console.warn("队列日志UI输出失败:", e.message);
                        }
                    });
                } else {
                    this.UI模块.添加日志(日志项.消息, 日志项.类型);
                }
            } catch (e) {
                console.warn("处理队列日志失败:", e.message);
                break; // 如果还是失败，停止处理
            }
        }
    },
    
    /**
     * 便捷方法
     */
    成功: function(消息, 来源) {
        this.输出日志(消息, "成功", 来源);
    },
    
    错误: function(消息, 来源) {
        this.输出日志(消息, "错误", 来源);
    },
    
    警告: function(消息, 来源) {
        this.输出日志(消息, "警告", 来源);
    },
    
    信息: function(消息, 来源) {
        this.输出日志(消息, "信息", 来源);
    },
    
    /**
     * 获取管理器状态
     */
    获取状态: function() {
        return {
            已初始化: this.已初始化,
            UI模块可用: this.UI模块 !== null,
            队列长度: this.日志队列.length,
            启用文件日志: this.启用文件日志
        };
    },
    
    /**
     * 启用文件日志
     */
    启用文件日志功能: function() {
        this.启用文件日志 = true;
        this.信息("文件日志功能已启用", "日志管理器");
    },
    
    /**
     * 禁用文件日志
     */
    禁用文件日志功能: function() {
        this.启用文件日志 = false;
        this.信息("文件日志功能已禁用", "日志管理器");
    },

    /**
     * 写入异步日志到文件（方案三核心功能）
     */
    写入异步日志: function(消息, 类型, 来源) {
        try {
            var 日志对象 = {
                时间戳: new Date().getTime(),
                时间: new Date().toLocaleString(),
                消息: 消息,
                类型: 类型 || "信息",
                来源: 来源 || "未知",
                ID: Date.now() + "_" + Math.random().toString(36).substr(2, 9)
            };

            var 日志JSON = JSON.stringify(日志对象) + "\n";

            // 追加写入文件
            if (files.exists(this.异步日志文件路径)) {
                files.append(this.异步日志文件路径, 日志JSON);
            } else {
                files.write(this.异步日志文件路径, 日志JSON);
            }

            return true;
        } catch (e) {
            console.error("写入异步日志失败:", e.message);
            return false;
        }
    },

    /**
     * 读取异步日志文件
     */
    读取异步日志: function() {
        try {
            if (!files.exists(this.异步日志文件路径)) {
                return [];
            }

            var 文件内容 = files.read(this.异步日志文件路径);
            if (!文件内容 || 文件内容.trim() === "") {
                return [];
            }

            var 日志列表 = [];
            var 行列表 = 文件内容.split('\n');

            for (var i = 0; i < 行列表.length; i++) {
                var 行内容 = 行列表[i].trim();
                if (行内容) {
                    try {
                        var 日志对象 = JSON.parse(行内容);
                        日志列表.push(日志对象);
                    } catch (e) {
                        // 忽略解析失败的行
                    }
                }
            }

            return 日志列表;
        } catch (e) {
            console.error("读取异步日志失败:", e.message);
            return [];
        }
    },

    /**
     * 清空异步日志文件
     */
    清空异步日志: function() {
        try {
            if (files.exists(this.异步日志文件路径)) {
                files.remove(this.异步日志文件路径);
            }
            this.信息("异步日志文件已清空", "日志管理器");
            return true;
        } catch (e) {
            this.错误("清空异步日志文件失败: " + e.message, "日志管理器");
            return false;
        }
    },

    /**
     * 获取文件修改时间
     */
    获取文件修改时间: function() {
        try {
            if (!files.exists(this.异步日志文件路径)) {
                return 0;
            }

            var 文件对象 = new java.io.File(this.异步日志文件路径);
            return 文件对象.lastModified();
        } catch (e) {
            return 0;
        }
    },



    /**
     * 修复控制台配置错误（整合控制台安全处理器功能）
     */
    修复控制台配置: function() {
        if (this.控制台已修复) {
            return true;
        }

        try {
            // 1. 清除可能有问题的控制台配置
            if (typeof console !== 'undefined' && console.setGlobalLogConfig) {
                try {
                    console.setGlobalLogConfig(null);
                } catch (e) {
                    // 清除失败，尝试设置最简配置
                    try {
                        console.setGlobalLogConfig({});
                    } catch (e2) {
                        // 完全无法配置，忽略
                    }
                }
            }

            // 2. 保存原始控制台方法并增强安全性
            if (typeof console !== 'undefined' && !this.原始console方法) {
                this.原始console方法 = {
                    log: console.log,
                    warn: console.warn,
                    error: console.error,
                    info: console.info
                };

                // 重写控制台方法，添加安全处理
                var self = this;
                console.log = function() {
                    try {
                        self.原始console方法.log.apply(console, arguments);
                    } catch (e) {
                        try {
                            print(Array.prototype.slice.call(arguments).join(' '));
                        } catch (e2) {
                            // 静默处理
                        }
                    }
                };

                console.warn = function() {
                    try {
                        self.原始console方法.warn.apply(console, arguments);
                    } catch (e) {
                        try {
                            self.原始console方法.log.apply(console, ["[WARN]"].concat(Array.prototype.slice.call(arguments)));
                        } catch (e2) {
                            try {
                                print("[WARN] " + Array.prototype.slice.call(arguments).join(' '));
                            } catch (e3) {
                                // 静默处理
                            }
                        }
                    }
                };

                console.error = function() {
                    try {
                        self.原始console方法.error.apply(console, arguments);
                    } catch (e) {
                        try {
                            self.原始console方法.log.apply(console, ["[ERROR]"].concat(Array.prototype.slice.call(arguments)));
                        } catch (e2) {
                            try {
                                print("[ERROR] " + Array.prototype.slice.call(arguments).join(' '));
                            } catch (e3) {
                                // 静默处理
                            }
                        }
                    }
                };
            }

            // 3. 禁用可能导致问题的控制台功能
            if (typeof console !== 'undefined') {
                var 危险方法列表 = ['setGlobalLogConfig', 'setLogConfig', 'setSize', 'setPosition', 'setTitle', 'setCanInput'];
                for (var i = 0; i < 危险方法列表.length; i++) {
                    var 方法名 = 危险方法列表[i];
                    if (console[方法名]) {
                        try {
                            console[方法名] = function() {
                                // 安全的空实现，防止WeakReference错误
                                console.warn("控制台方法 " + 方法名 + " 已被安全禁用，避免WeakReference错误");
                                return true; // 返回成功状态
                            };
                        } catch (e) {
                            // 无法重写，忽略
                        }
                    }
                }
            }

            this.控制台已修复 = true;
            console.log("✅ 控制台配置修复完成");
            return true;

        } catch (e) {
            console.warn("控制台配置修复失败:", e.message);
            return false;
        }
    }
};

// 自动初始化
try {
    日志管理器.初始化();
} catch (e) {
    console.error("日志管理器自动初始化失败:", e.message);
}

// 导出模块
module.exports = 日志管理器;
