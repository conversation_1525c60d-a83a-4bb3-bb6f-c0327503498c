# AutoXjs ozobiozobi v6.5.8.17 专业知识体系

## 核心技术栈掌握

### JavaScript ES5运行环境
- **Rhino 1.7.13引擎**：基于Java的JavaScript引擎，支持ES5标准
- **语法限制**：不支持ES6+特性，如箭头函数、let/const、模板字符串
- **兼容性要求**：所有代码必须使用ES5语法确保引擎兼容
- **性能特点**：相比V8引擎性能较低，需要注意代码优化

### Android原生UI开发
- **XML布局系统**：完全基于Android原生XML布局，支持LinearLayout、RelativeLayout等
- **控件体系**：TextView、Button、EditText、ImageView、Switch等原生控件
- **样式设置**：通过属性设置颜色、大小、边距、圆角等样式
- **事件处理**：使用on()方法绑定点击、长按、文本变化等事件

## AutoXjs ozobiozobi v6.5.8.17 新增功能

### 网络检测功能
```javascript
// WiFi连接状态检查
var wifi可用 = networkUtils.isWifiAvailable();

// 获取WiFi IP地址
var wifi_ip = networkUtils.getWifiIPv4();

// 获取所有IP地址列表
var ip列表 = networkUtils.getIPList();
```

### 屏幕信息实时获取
```javascript
// 获取当前屏幕宽高
var 屏幕宽度 = device.getCurWidth();
var 屏幕高度 = device.getCurHeight();

// 获取屏幕方向 (1=竖屏，2=横屏)
var 屏幕方向 = getCurOrientation();

// 获取状态栏高度
var 状态栏高度 = getStatusBarHeight();
```

### 调试增强功能
```javascript
// 增强的调试日志，可跟踪堆栈行号
traceLog("调试信息: 变量值 = " + 变量值);

// 输出到文件的调试日志
traceLog("重要信息", true);  // 第二个参数为true时输出到文件
```

### 截图增强功能
```javascript
// 强制返回新对象，避免缓存问题
var 截图 = images.captureScreen(true);

// 传统截图方式（可能使用缓存）
var 截图2 = captureScreen();
```

### UI控件色调设置
```javascript
// 设置输入框色调
ui.输入框.setTint("#00A843");

// 设置复选框色调
ui.复选框.setTint("#FF5722");

// 设置单选按钮色调
ui.单选按钮.setTint("#2196F3");

// 设置按钮渐变背景
ui.按钮.setBackgroundGradient(["#FF5722", "#FF9800"]);
```

### 布局分析增强
```javascript
// 带刷新的控件查找，提高查找成功率
var 控件 = text('文本内容').find(true);

// 传统查找方式
var 控件2 = text('文本内容').find();
```

### 时间处理增强
```javascript
// 格式化时间戳 (默认格式"yyyy-MM-dd HH:mm:ss.SSS")
var 格式化时间 = dateFormat(new Date().getTime());

// 自定义格式
var 自定义时间 = dateFormat(时间戳, "yyyy年MM月dd日");

// 时间字符串转时间戳
var 时间戳 = dateToTimestamp("2025-01-15 10:30:00");
```

### 视图工具功能
```javascript
// 通过ID查找父视图
var 父视图 = viewUtils.findParentById(当前视图, "父视图ID");

// 单位转换
var sp值 = viewUtils.pxToSp(像素值);
var dp值 = viewUtils.pxToDp(像素值);
var px值 = viewUtils.dpToPx(dp值);
```

## 推荐使用的新API规范

### 页面布局管理
```javascript
// ✅ 推荐：使用ui.layout()
ui.layout(页面模块.布局);

// ❌ 避免：ui.setContentView()可能导致XML解析错误
// ui.setContentView(布局);
```

### 控件属性设置
```javascript
// ✅ 推荐：使用attr()方法
ui.文本控件.attr("text", "新文本内容");
ui.按钮控件.attr("visibility", "visible");  // visible, gone, invisible
ui.开关控件.attr("checked", true);
ui.文本控件.attr("textColor", "#FF0000");
ui.控件.attr("background", "#00A843");

// ❌ 避免：直接方法调用可能不稳定
// ui.文本控件.setText("文本");
// ui.控件.setVisibility(View.VISIBLE);
```

### 安全的控件访问模式
```javascript
// ✅ 推荐：先检查控件是否存在
function 安全更新控件() {
    if (ui.日志列表) {
        ui.日志列表.removeAllViews();
    }
    if (ui.空状态区域) {
        ui.空状态区域.attr("visibility", "visible");
    }
    if (ui.权限开关) {
        ui.权限开关.on("check", function(checked) {
            // 处理开关事件
        });
    }
}
```

### 页面切换标准模式
```javascript
// ✅ 推荐的页面切换实现
function 切换到页面(页面名称) {
    try {
        var 页面模块 = require('./UI' + 页面名称 + '页面.js');
        ui.layout(页面模块.布局);

        var 逻辑模块 = require('./' + 页面名称 + '逻辑.js');
        if (逻辑模块.初始化逻辑) {
            逻辑模块.初始化逻辑();
        }
    } catch (e) {
        console.error("页面切换失败:", e);
        traceLog("页面切换错误详情: " + e.toString());
    }
}
```

## XML布局格式规范

### 正确的属性格式
```xml
<!-- ✅ 正确格式 - 使用空格分隔 -->
<horizontal padding="8dp 4dp" margin="16dp 8dp">
    <text padding="16dp 16dp 16dp 8dp"/>
    <button stroke="1dp #00A843"/>
</horizontal>

<!-- ❌ 错误格式 - 使用逗号分隔 -->
<horizontal padding="8dp,4dp" margin="16dp,8dp">
    <text padding="16dp,16dp,16dp,8dp"/>
    <button stroke="1dp,#00A843"/>
</horizontal>
```

### 标准卡片布局
```xml
<card cardBackgroundColor="#FFFFFF" cardCornerRadius="8dp" cardElevation="2dp">
    <vertical padding="16dp">
        <text text="功能标题" textSize="16sp" textColor="#333333" textStyle="bold"/>
        <text text="功能描述" textSize="14sp" textColor="#666666" marginTop="4dp"/>
        <horizontal marginTop="12dp">
            <button id="确认按钮" text="确认" background="#00A843" textColor="#FFFFFF"/>
            <button id="取消按钮" text="取消" background="#F5F5F5" textColor="#333333" marginLeft="8dp"/>
        </horizontal>
    </vertical>
</card>
```

### 模块导出规范
```javascript
// ✅ 统一的导出格式
module.exports = {
    布局: 布局变量,           // 统一使用"布局"作为XML布局导出名
    初始化逻辑: 初始化函数,    // 统一的初始化方法名
    // 其他功能函数...
};

// ✅ 正确的调用方式
var 页面模块 = require('./UI页面.js');
ui.layout(页面模块.布局);    // 使用统一的"布局"属性名
```

## 性能优化和错误处理

### 内存管理最佳实践
```javascript
// ✅ 及时释放图像资源
function 安全_图像处理() {
    var 截图 = images.captureScreen(true);
    try {
        var 结果 = findImage(截图, 模板图片);
        return 结果;
    } finally {
        if (截图) {
            截图.recycle();  // 及时释放内存
        }
    }
}

// ✅ 清理全局变量
function 清理_全局资源() {
    // 清空数组
    全局数组.length = 0;
    
    // 清理对象
    全局对象 = null;
    
    // 移除事件监听器
    if (ui.按钮) {
        ui.按钮.removeAllListeners();
    }
}
```

### 完善的错误处理
```javascript
// ✅ 标准错误处理模式
function 安全执行_操作(操作函数, 错误回调) {
    try {
        return 操作函数();
    } catch (e) {
        console.error("操作执行失败:", e);
        traceLog("详细错误信息: " + e.toString());
        if (错误回调) {
            错误回调(e);
        }
        return null;
    }
}

// ✅ 异步操作错误处理
function 异步_网络请求(url, 回调) {
    threads.start(function() {
        try {
            var 响应 = http.get(url);
            if (响应.statusCode === 200) {
                回调(null, 响应.body.string());
            } else {
                回调(new Error("HTTP错误: " + 响应.statusCode));
            }
        } catch (e) {
            回调(e);
        }
    });
}
```

## 官方文档资源

### 主要文档地址
- **项目地址**: https://github.com/ozobiozobi/Autoxjs_v6_ozobi
- **官方文档**: https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html
- **UI界面文档**: https://autox-doc.vercel.app/docs/rhino/base/ui
- **完整文档**: https://autox-doc.vercel.app/docs/rhino/documentation

### 版本信息
- **当前版本**: v6.5.8.17 (2025年5月13日)
- **目标SDK**: 35 (支持Android 15)
- **最低支持**: Android 9+
- **测试环境**: 雷电模拟器 (540x960, DPI 240)
