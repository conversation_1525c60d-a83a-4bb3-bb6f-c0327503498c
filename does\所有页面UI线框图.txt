Magic 游戏辅助脚本 - 所有页面UI线框图

╔═══════════════════════════════════════════════════════════════════╗
║                           主页界面                                ║
╚═══════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────┐
│                        顶部导航栏                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ☰     主页                                              │ │
│ │ 菜单   (18sp白色粗体,居中对齐)                          │ │
│ │ (56dp) 绿色背景 #4CAF50, 56dp高, 4dp阴影               │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     滚动内容区域                                │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    游戏数据卡片                             │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 游戏数据 ──────────────── 🔄 刷新  🗑️ 删除           │ │ │
│ │ │ (16sp粗体黑色)           (40x40dp绿色圆形按钮)        │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │              数据内容区域 (200dp高)                 │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │              📊 (48sp图标)                         │ │ │ │
│ │ │ │            暂无数据 (16sp粗体)                      │ │ │ │
│ │ │ │      开始运行脚本后将显示游戏数据                   │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ [游戏数据列表容器 - 动态添加数据条目]              │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影                                │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    脚本状态卡片                             │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 脚本状态 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ ● 脚本未运行 (16sp状态指示器 + 14sp状态文字)           │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影                                │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                    底部控制按钮区域                             │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────┬─────────────────────┐               │ │
│ │ │       运行          │       停止          │               │ │
│ │ │ (绿色背景#4CAF50)   │ (绿色背景#4CAF50)   │               │ │
│ │ │ 白色文字,16sp粗体   │ 白色文字,16sp粗体   │               │ │
│ │ │ 48dp高,12dp圆角     │ 48dp高,12dp圆角     │               │ │
│ │ │ 右8dp边距           │ 左8dp边距           │               │ │
│ │ └─────────────────────┴─────────────────────┘               │ │
│ │ 白色背景, 16dp内边距, 4dp阴影                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     底部导航栏                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │    ⚙️ 脚本配置      🏠 主页        📄 日志                │ │
│ │   (半透明白色)     (白色,当前页面)   (半透明白色)            │ │
│ │   20sp图标         20sp图标       20sp图标                 │ │
│ │   8dp内边距        8dp内边距      8dp内边距                │ │
│ │ 绿色背景#4CAF50, 50dp高, 8dp阴影                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                          日志页面                                 ║
╚═══════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────┐
│                        顶部导航栏                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ☰     日志                                      🗑️ 清空    │ │
│ │ 菜单   (18sp白色粗体,居中对齐)                  (18sp图标)  │ │
│ │ (56dp) 绿色背景 #4CAF50, 56dp高, 4dp阴影       (56x56dp)   │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     滚动内容区域                                │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                      日志卡片                               │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 运行日志 ──────────────────────────── 0 条记录          │ │ │
│ │ │ (16sp粗体黑色)                       (12sp灰色文字)     │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │                                                         │ │ │
│ │ │              空状态区域 (默认显示)                      │ │ │
│ │ │                                                         │ │ │
│ │ │              📄 (48sp图标)                             │ │ │ │
│ │ │            暂无日志记录 (16sp粗体)                      │ │ │
│ │ │      脚本运行后将显示日志信息                           │ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │            日志列表容器 (默认隐藏)                  │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ ┌─────────────────────────────────────────────────┐ │ │ │ │
│ │ │ │ │          日志滚动区域 (300dp高)                 │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ [动态添加的日志条目]                           │ │ │ │ │
│ │ │ │ │ ✓ 成功日志 - 时间戳                            │ │ │ │ │
│ │ │ │ │ ⚠ 警告日志 - 时间戳                            │ │ │ │ │
│ │ │ │ │ ❌ 错误日志 - 时间戳                           │ │ │ │ │
│ │ │ │ │ ℹ 信息日志 - 时间戳                            │ │ │ │ │
│ │ │ │ └─────────────────────────────────────────────────┘ │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 白色背景, 16dp圆角, 2dp阴影                                │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                        侧滑抽屉界面                               ║
╚═══════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────┐
│                    抽屉遮罩层 (半透明黑色)                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                                                             │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │                抽屉面板 (280dp宽, 绿色背景)             │ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │              顶部应用信息卡片                       │ │ │ │
│ │ │ │ ┌─────────────────────────────────────────────────┐ │ │ │ │
│ │ │ │ │              fa-biohazard(36sp图标)                     │ │ │ │ │
│ │ │ │ │         Magic 游戏助手 (16sp粗体)              │ │ │ │ │
│ │ │ │ │            v1.0.0 (10sp灰色)                   │ │ │ │ │
│ │ │ │ │   基于 AutoXjs ozobiozobi v6.5.8.17 (8sp)     │ │ │ │ │
│ │ │ │ └─────────────────────────────────────────────────┘ │ │ │ │
│ │ │ │ 白色背景, 12dp圆角, 4dp阴影, 12dp边距              │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │              权限管理卡片                           │ │ │ │
│ │ │ │ ┌─────────────────────────────────────────────────┐ │ │ │ │
│ │ │ │ │ 权限管理 (14sp粗体绿色)                        │ │ │ │ │
│ │ │ │ ├─────────────────────────────────────────────────┤ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ �️ 无障碍服务 ──────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 用于自动化操作 (10sp说明文字)              │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ 📱 悬浮窗权限 ──────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 显示悬浮控制面板 (10sp说明文字)            │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ 🛡️ 前台服务 ────────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 保持应用在前台运行，防止被系统杀死 (10sp)  │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ 📁 存储权限 ────────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 访问设备存储，保存配置和日志数据 (10sp)    │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ 🔔 通知访问 ────────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 监听游戏通知，实现智能辅助功能 (10sp)      │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ │                                                 │ │ │ │ │
│ │ │ │ │ ┌─────────────────────────────────────────────┐ │ │ │ │ │
│ │ │ │ │ │ 📷 屏幕截图 ────────────────── ●○ 开关     │ │ │ │ │ │
│ │ │ │ │ │ 用于图像识别功能 (10sp说明文字)            │ │ │ │ │ │
│ │ │ │ │ │ 浅绿背景, 8dp圆角                          │ │ │ │ │ │
│ │ │ │ │ └─────────────────────────────────────────────┘ │ │ │ │ │
│ │ │ │ └─────────────────────────────────────────────────┘ │ │ │ │
│ │ │ │ 白色背景, 12dp圆角, 2dp阴影, 6dp边距               │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │                                                         │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │              关于应用卡片                           │ │ │ │
│ │ │ │ ┌─────────────────────────────────────────────────┐ │ │ │ │
│ │ │ │ │ ℹ️ 关于应用 ──────────────────────── ➤         │ │ │ │ │
│ │ │ │ │ (14sp粗体绿色)                    (14sp箭头)   │ │ │ │ │
│ │ │ │ │ 48dp高, 可点击                                 │ │ │ │ │
│ │ │ │ └─────────────────────────────────────────────────┘ │ │ │ │
│ │ │ │ 白色背景, 12dp圆角, 2dp阴影, 6dp边距               │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ 点击遮罩区域关闭抽屉                                    │ │
│ └─────────────────────────────────────────────────────────────┐ │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                         脚本配置页面                              ║
╚═══════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────┐
│                        顶部导航栏                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ☰     脚本配置                                              │ │
│ │ 菜单   (18sp白色粗体,左对齐,16dp左边距)                     │ │
│ │ (56dp) 绿色背景 #4CAF50, 56dp高, 4dp阴影                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     滚动内容区域                                │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                  游戏配置卡片                               │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 游戏配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 自动启动游戏 ──────────────────────── ●○ 开关          │ │ │
│ │ │ 过游戏教程 ────────────────────────── ●○ 开关          │ │ │
│ │ │ 自动玩游戏 ────────────────────────── ●○ 开关          │ │ │
│ │ │ 每日领IOTA币 ──────────────────────── ●○ 开关          │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ cardBackgroundColor="#FFFFFF", cardCornerRadius="16dp"      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                 操作配置卡片                                │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 操作配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 左键设置 ──────────────────────────── ●○ 开关          │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ X坐标[___] Y坐标[___] (数字输入,8dp圆角)           │ │ │ │
│ │ │ │ 宽度[___]  高度[___]  (浅灰背景)                   │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │ 右键设置 ──────────────────────────── ●○ 开关          │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ X坐标[___] Y坐标[___] (相同布局)                   │ │ │ │
│ │ │ │ 宽度[___]  高度[___]                               │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ cardBackgroundColor="#FFFFFF", cardCornerRadius="16dp"      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                分数控制卡片                                 │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 分数控制 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 分数暂停 ──────────────────────────── ●○ 开关          │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ 最低[___] 最高[___] 分 (数字输入)                  │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ cardBackgroundColor="#FFFFFF", cardCornerRadius="16dp"      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                广告配置卡片                                 │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ 广告配置 (16sp粗体黑色)                                │ │ │
│ │ ├─────────────────────────────────────────────────────────┤ │ │
│ │ │ 广告左区域 (14sp黑色标题)                              │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ X坐标[___] Y坐标[___] (数字输入)                   │ │ │ │
│ │ │ │ 宽度[___]  高度[___]                               │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ │ 广告右区域 (14sp黑色标题)                              │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ X坐标[___] Y坐标[___] (相同布局)                   │ │ │ │
│ │ │ │ 宽度[___]  高度[___]                               │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │ cardBackgroundColor="#FFFFFF", cardCornerRadius="16dp"      │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                  底部操作按钮区域                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────┬─────────────────────┐               │ │
│ │ │     保存配置        │     重置配置        │               │ │
│ │ │ Material Design样式 │ Material Design样式 │               │ │
│ │ │ backgroundTint绿色  │ backgroundTint绿色  │               │ │
│ │ │ 白色文字,16sp粗体   │ 白色文字,16sp粗体   │               │ │
│ │ │ 48dp高,自带圆角     │ 48dp高,自带圆角     │               │ │
│ │ └─────────────────────┴─────────────────────┘               │ │
│ │ 16dp内边距                                                  │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘

╔═══════════════════════════════════════════════════════════════════╗
║                           设计规范                                ║
╚═══════════════════════════════════════════════════════════════════╝

统一设计元素:
• 颜色主题: 绿色 #4CAF50 (成功绿)
• 卡片样式: cardBackgroundColor="#FFFFFF", cardCornerRadius="16dp", cardElevation="2dp"
• 按钮样式: style="Widget.AppCompat.Button.Colored", backgroundTint="#4CAF50"
• 开关样式: 浅灰/绿色轨道 + 白色按钮
• 输入框样式: background="#FAFAFA", radius="8dp", padding="8dp"
• 导航栏: 50dp高, 绿色背景, 8dp阴影
• 工具栏: 56dp高, 绿色背景, 4dp阴影
• 字体规范: 18sp超大, 16sp大, 14sp中, 12sp小, 10sp微小

圆角设置技术规范:
• Card控件: 使用 cardBackgroundColor + cardCornerRadius (不能用background)
• Button控件: 使用 Material Design样式 + backgroundTint (不能用radius)
• Input控件: 使用 background + radius 属性
• 抽屉卡片: cardCornerRadius="12dp" (小卡片使用12dp圆角)

页面结构:
• 顶部导航栏 (菜单按钮 + 标题 + 功能按钮)
• 滚动内容区域 (卡片式布局)
• 底部操作区域 (可选)
• 底部导航栏 (三个标签页)
• 侧滑抽屉 (280dp宽, 权限管理 + 应用信息)

┌─────────────────────────────────────────────────────────────────┐
│                     底部导航栏                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │    ⚙️ 脚本配置      🏠 主页        📄 日志                │ │
│ │   (半透明白色)     (半透明白色)    (白色,当前页面)          │ │
│ │   20sp图标         20sp图标       20sp图标                 │ │
│ │   8dp内边距        8dp内边距      8dp内边距                │ │
│ │ 绿色背景#4CAF50, 50dp高, 8dp阴影                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
